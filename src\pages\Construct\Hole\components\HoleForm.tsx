import React from 'react';
import { Form, Input, DatePicker, Select } from 'antd';
import type { FormInstance } from 'antd/es/form';

interface HoleFormProps {
  form: FormInstance;
  onFinish: (values: any) => void;
}

const HoleForm: React.FC<HoleFormProps> = ({ form, onFinish }) => {
  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
    >
      <Form.Item
        label="日期"
        name="date"
        rules={[{ required: true, message: '请选择日期' }]}
      >
        <DatePicker style={{ width: '100%' }} format="YYYY-MM-DD" />
      </Form.Item>

      <Form.Item
        label="施工地点"
        name="position"
        rules={[{ required: true, message: '请输入施工地点' }]}
      >
        <Input placeholder="请输入施工地点" />
      </Form.Item>

      <Form.Item
        label="钻机编号"
        name="drillNumber"
        rules={[{ required: true, message: '请输入钻机编号' }]}
      >
        <Input placeholder="请输入钻机编号" />
      </Form.Item>

      <Form.Item
        label="钻孔类型"
        name="drillingType"
        rules={[{ required: true, message: '请输入钻孔类型' }]}
      >
        <Input placeholder="请输入钻孔类型" />
      </Form.Item>

      <Form.Item
        label="班次"
        name="shift"
        rules={[{ required: true, message: '请输入班次' }]}
      >
        <Input placeholder="请输入班次" />
      </Form.Item>

      <Form.Item
        label="开孔人员名称"
        name="personnel"
        rules={[{ required: true, message: '请输入开孔人员名称' }]}
      >
        <Input placeholder="请输入开孔人员名称" />
      </Form.Item>

      <Form.Item
        label="孔号"
        name="holeNumber"
        rules={[{ required: true, message: '请输入孔号' }]}
      >
        <Input placeholder="请输入孔号" />
      </Form.Item>

      <Form.Item
        label="孔深"
        name="holeDepth"
        rules={[{ required: true, message: '请输入孔深' }]}
      >
        <Input placeholder="请输入孔深" />
      </Form.Item>

      <Form.Item
        label="孔径"
        name="holeDiameter"
        rules={[{ required: true, message: '请输入孔径' }]}
      >
        <Input placeholder="请输入孔径" />
      </Form.Item>

      <Form.Item
        label="开孔角度"
        name="holeAngle"
        rules={[{ required: true, message: '请输入开孔角度' }]}
      >
        <Input placeholder="请输入开孔角度" />
      </Form.Item>

      <Form.Item
        label="方位"
        name="direction"
        rules={[{ required: true, message: '请输入方位' }]}
      >
        <Input placeholder="请输入方位" />
      </Form.Item>

      <Form.Item
        label="开孔高度"
        name="holeHeight"
        rules={[{ required: true, message: '请输入开孔高度' }]}
      >
        <Input placeholder="请输入开孔高度" />
      </Form.Item>

      <Form.Item
        label="见煤距离"
        name="coalDistance"
        rules={[{ required: true, message: '请输入见煤距离' }]}
      >
        <Input placeholder="请输入见煤距离" />
      </Form.Item>

      <Form.Item
        label="见岩距离"
        name="rockDistance"
        rules={[{ required: true, message: '请输入见岩距离' }]}
      >
        <Input placeholder="请输入见岩距离" />
      </Form.Item>

      <Form.Item
        label="扩孔起始距离"
        name="reamingStartDistance"
        rules={[{ required: true, message: '请输入扩孔起始距离' }]}
      >
        <Input placeholder="请输入扩孔起始距离" />
      </Form.Item>

      <Form.Item
        label="打钻起始距离"
        name="drillingStartDistance"
        rules={[{ required: true, message: '请输入打钻起始距离' }]}
      >
        <Input placeholder="请输入打钻起始距离" />
      </Form.Item>

      <Form.Item
        label="预计出煤量"
        name="estimatedCoalOutput"
        rules={[{ required: true, message: '请输入预计出煤量' }]}
      >
        <Input placeholder="请输入预计出煤量" />
      </Form.Item>

      <Form.Item
        label="开孔角度误差"
        name="holeAngleError"
        rules={[{ required: true, message: '请输入开孔角度误差' }]}
      >
        <Input placeholder="请输入开孔角度误差" />
      </Form.Item>

      <Form.Item
        label="开孔方位误差"
        name="holeDirectionError"
        rules={[{ required: true, message: '请输入开孔方位误差' }]}
      >
        <Input placeholder="请输入开孔方位误差" />
      </Form.Item>

      <Form.Item
        label="开孔高度误差"
        name="holeHeightError"
        rules={[{ required: true, message: '请输入开孔高度误差' }]}
      >
        <Input placeholder="请输入开孔高度误差" />
      </Form.Item>

      <Form.Item
        label="孔深误差"
        name="holeDepthError"
        rules={[{ required: true, message: '请输入孔深误差' }]}
      >
        <Input placeholder="请输入孔深误差" />
      </Form.Item>

      <Form.Item
        label="是否需要轨迹"
        name="ifTrajectoryRequired"
        rules={[{ required: true, message: '请选择是否需要轨迹' }]}
      >
        <Select placeholder="请选择是否需要轨迹">
          <Select.Option value={0}>不需要</Select.Option>
          <Select.Option value={1}>需要</Select.Option>
        </Select>
      </Form.Item>
    </Form>
  );
};

export default HoleForm; 