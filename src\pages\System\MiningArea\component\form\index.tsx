import React, { useState, useEffect } from 'react';
import { message, Breadcrumb, Card, Spin, Result, Button } from 'antd';
import { history } from '@umijs/max';
import SubmitForm, { Column } from '@/components/Form';
import { getRequest, postRequest } from '@/services/api/api';

interface NoodleInfo {
    noodlesId: string;
    name: string;
    status: number;
    length: string;
    width: string;
    thickness: string;
    method: string;
    yield: string;
    startTime: string;
    endTime: string;
    responsiblePerson: string;
    remark: string;
}

export default () => {
    const [messageApi, contextHolder] = message.useMessage();
    const { location } = history;
    const [title, setTitle] = useState("添加采面")
    const [id, setId] = useState(-1)
    const [api, setApi] = useState('noodles/post_add')
    const [msg, setMsg] = useState('')
    const [loading, setLoading] = useState<boolean>(false);
    const [initialValues, setInitialValues] = useState<Partial<NoodleInfo>>({})

    const columns: Column[] = [
        {
            label: '采面唯一标识编号',
            dataIndex: 'noodlesId',
            rules: [{ required: true, message: '请输入采面唯一标识编号!' }],
        },
        {
            label: '采面名称',
            dataIndex: 'name',
            rules: [{ required: true, message: '请输入采面名称!' }],
        },
        {
            label: '开采状态',
            dataIndex: 'status',
            type: 'Select',
            options: [
                { label: '未开采', value: 0 },
                { label: '开采中', value: 1 },
            ],
            rules: [{ required: true, message: '请选择开采状态!' }],
        },
        {
            label: '采面长度',
            dataIndex: 'length',
            rules: [{ required: true, message: '请输入采面长度!' }],
        },
        {
            label: '采面宽度',
            dataIndex: 'width',
            rules: [{ required: true, message: '请输入采面宽度!' }],
        },
        {
            label: '采面厚度',
            dataIndex: 'thickness',
            rules: [{ required: true, message: '请输入采面厚度!' }],
        },
        {
            label: '采煤方法',
            dataIndex: 'method',
            rules: [{ required: true, message: '请输入采煤方法!' }],
        },
        {
            label: '预计煤炭产量',
            dataIndex: 'yield',
            rules: [{ required: true, message: '请输入预计煤炭产量!' }],
        },
        {
            label: '开始日期',
            dataIndex: 'startTime',
            type: 'DatePicker',
            rules: [{ required: true, message: '请选择开始日期!' }],
        },
        {
            label: '结束日期',
            dataIndex: 'endTime',
            type: 'DatePicker',
            rules: [{ required: true, message: '请选择结束日期!' }],
        },
        {
            label: '责任人',
            dataIndex: 'responsiblePerson',
            rules: [{ required: true, message: '请输入责任人!' }],
        },
        {
            label: '备注',
            dataIndex: 'remark',
            type: 'TextArea',
            rules: [{ required: true, message: '请输入备注!' }],
        },
    ];

    const getIDFromURLUsingSubstring = (url: string) => {
        const idParam = 'id=';
        const startIndex = url.indexOf(idParam);
        if (startIndex === -1) {
            return null
        }
        const valueStartIndex = startIndex + idParam.length;
        let valueEndIndex = url.indexOf('&', valueStartIndex);
        if (valueEndIndex === -1) {
            valueEndIndex = url.length;
        }
        return url.substring(valueStartIndex, valueEndIndex);
    }

    const getInfo = async (id: string) => {
        setLoading(true)
        const result = await getRequest('noodles/get_info', { id });
        const { data, status, msg } = result as { data: NoodleInfo; status: number; msg: string };
        if (status === 0) {
            setInitialValues(data)
            setLoading(false)
        } else {
            setMsg(msg)
            setLoading(false)
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
    }

    useEffect(() => {
        if (location.search) {
            const url = location.search;
            const idValue = getIDFromURLUsingSubstring(url);
            if (idValue) {
                setId(parseInt(idValue, 10));
                setTitle("编辑采面")
                setApi('noodles/post_modify')
                getInfo(idValue)
            }
        }
    }, []);

    const onFinish = async (values: NoodleInfo) => {
        const postData = {
            ...values,
            id: id > 0 ? id : undefined
        }
        const result = await postRequest(api, postData);
        const { status, msg } = result as { status: number; msg: string };
        if (status === 0) {
            messageApi.open({
                type: 'success',
                content: "提交成功",
            });
            history.go(-1)
        } else {
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
    };

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '系统管理', },
                    { title: '矿区概览', },
                    { title: title, },
                ]}
            />
            <Spin spinning={loading}>
                {msg ? <Result
                    status="500"
                    title="error"
                    subTitle={msg}
                    extra={<Button type="primary" onClick={() => { history.go(-1) }}>返回上一层</Button>}
                /> :
                    <Card title={title} bordered={false} style={{ marginTop: 24 }}>
                        <SubmitForm
                            columns={columns}
                            initialValues={initialValues}
                            onFinish={onFinish}
                        />
                    </Card>
                }
            </Spin>
        </>
    );
};