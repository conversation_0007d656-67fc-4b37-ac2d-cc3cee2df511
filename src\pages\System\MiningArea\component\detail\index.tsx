import { Result, But<PERSON>, Tag, message, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Col, Row, Spin } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { getRequest } from '@/services/api/api';
import { history } from '@umijs/max';
import Tunnel from './components/Tunnel';
import DrillSite from './components/DrillSite';

interface NoodleInfo {
    noodlesId: string;
    name: string;
    status: number;
    length: string;
    width: string;
    thickness: string;
    method: string;
    yield: string;
    startTime: string;
    endTime: string;
    responsiblePerson: string;
    remark: string;
    createdAt: string;
    updateAt: string;
}

export default () => {
    const [messageApi, contextHolder] = message.useMessage();
    const { location } = history;
    const [initialValues, setInitialValues] = useState<NoodleInfo>();
    const [msg, setMsg] = useState('');
    const [loading, setLoading] = useState<boolean>(false);
    const [activeTabKey, setActiveTabKey] = useState<string>('tunnel');
    const [selectedTunnel, setSelectedTunnel] = useState<{ id: number; name: string }>();

    const getIDFromURLUsingSubstring = (url: string) => {
        const idParam = 'id=';
        const startIndex = url.indexOf(idParam);
        if (startIndex === -1) {
            return null;
        }
        const valueStartIndex = startIndex + idParam.length;
        let valueEndIndex = url.indexOf('&', valueStartIndex);
        if (valueEndIndex === -1) {
            valueEndIndex = url.length;
        }
        return url.substring(valueStartIndex, valueEndIndex);
    }

    const getInfo = async (id: string | null) => {
        if (!id) {
            setMsg('采面ID不能为空');
            messageApi.error('采面ID不能为空');
            return;
        }
        try {
            setLoading(true);
            const result = await getRequest('noodles/get_info', { id });
            const { data, status, msg } = result as any;
            if (status === 0 && data) {
                setInitialValues(data);
            } else {
                setMsg(msg || '获取采面信息失败');
                messageApi.error(msg || '获取采面信息失败');
            }
        } catch (error) {
            console.error('获取采面信息出错:', error);
            setMsg('获取采面信息失败');
            messageApi.error('获取采面信息失败');
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        if (location.search) {
            const url = location.search;
            const idValue = getIDFromURLUsingSubstring(url);
            getInfo(idValue)
        }
    }, []);

    const tabList = [
        {
            key: 'tunnel',
            tab: '巷道',
        },
        {
            key: 'drillsite',
            tab: '钻场',
        },
    ];

    const contentList: Record<string, React.ReactNode> = {
        tunnel: initialValues ? (
            <Tunnel 
                noodlesId={initialValues.id} 
                onSwitchTab={(tab, data) => {
                    setActiveTabKey(tab);
                    setSelectedTunnel(data);
                }}
            />
        ) : null,
        drillsite: initialValues ? (
            <DrillSite 
                noodlesId={initialValues.id} 
                selectedTunnel={selectedTunnel}
            />
        ) : null,
    };

    const onTabChange = (key: string) => {
        setActiveTabKey(key);
    };

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '系统管理', },
                    { title: '矿区概览', },
                    { title: '采面详情', },
                ]}
            />
            <Spin spinning={loading}>
                {msg ? <Result
                    status="500"
                    title="error"
                    subTitle={msg}
                    extra={<Button type="primary" onClick={() => { history.go(-1) }}>返回上一层</Button>}
                /> : initialValues ? (
                    <>
                        <Card bordered={false} style={{ marginTop: '18px' }}>
                            <div style={{ 
                                display: 'flex', 
                                justifyContent: 'space-between', 
                                alignItems: 'center'
                            }}>
                                <div style={{ fontWeight: 500, fontSize: '20px' }}>{initialValues?.name || '-'}</div>
                                <Button 
                                    icon={<EditOutlined />} 
                                    onClick={() => {
                                        const url = location.search;
                                        const idValue = getIDFromURLUsingSubstring(url);
                                        if (idValue) {
                                            history.push(`/system/miningArea/form?id=${idValue}`);
                                        }
                                    }}
                                >
                                    修改
                                </Button>
                            </div>
                            <Row style={{ marginTop: '14px' }} gutter={[16, 16]}>
                                <Col span={4}>采面编号：{initialValues?.noodlesId || '-'}</Col>
                                <Col span={4}>开采状态：
                                    <Tag color={initialValues?.status === 1 ? 'blue' : 'default'}>
                                        {initialValues?.status === 1 ? '开采中' : '未开采'}
                                    </Tag>
                                </Col>
                                <Col span={4}>责任人：{initialValues?.responsiblePerson || '-'}</Col>
                                <Col span={4}>长度(m)：{initialValues?.length || '-'}</Col>
                                <Col span={4}>宽度(m)：{initialValues?.width || '-'}</Col>
                                <Col span={4}>厚度(m)：{initialValues?.thickness || '-'}</Col>
                                <Col span={4}>采煤方法：{initialValues?.method || '-'}</Col>
                                <Col span={4}>预计产量(t)：{initialValues?.yield || '-'}</Col>
                                <Col span={4}>开始时间：{initialValues?.startTime || '-'}</Col>
                                <Col span={4}>预计结束时间：{initialValues?.endTime || '-'}</Col>
                                <Col span={24}>备注：{initialValues?.remark || '-'}</Col>
                            </Row>
                        </Card>
                        <Card
                            style={{ marginTop: '16px' }}
                            bordered={false}
                            tabList={tabList}
                            activeTabKey={activeTabKey}
                            onTabChange={onTabChange}
                        >
                            {contentList[activeTabKey]}
                        </Card>
                    </>
                ) : null}
            </Spin>
        </>
    );
};