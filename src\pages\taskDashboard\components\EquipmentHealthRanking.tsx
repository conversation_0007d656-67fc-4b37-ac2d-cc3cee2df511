/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\EquipmentHealthRanking.tsx
 * @Description: 设备健康评分排行榜组件 - 展示设备健康评分排行，支持自动分页滚动
 */

import { Card, Progress, Empty } from 'antd';
import { TrophyOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import styles from '../newTaskDashboard.less';

interface HealthScoreData {
  equipment: string;
  score: number;
  rank: number;
}

interface EquipmentHealthRankingProps {
  // 设备健康评分数据
  healthScoreData?: HealthScoreData[];
  // 每页显示数量
  pageSize?: number;
  // 自动滚动间隔（毫秒）
  scrollInterval?: number;
}

const EquipmentHealthRanking: React.FC<EquipmentHealthRankingProps> = ({
  healthScoreData,
  pageSize = 6,
  scrollInterval = 5000,
}) => {
  // 默认完整的设备健康数据（用于分页滚动）
  const defaultAllHealthScoreData = [
    { equipment: 'ZJ-1002', score: 92, rank: 1 },
    { equipment: 'ZJ-1003', score: 76, rank: 2 },
    { equipment: 'ZJ-1004', score: 70, rank: 3 },
    { equipment: 'ZJ-1005', score: 58, rank: 4 },
    { equipment: 'ZJ-1006', score: 52, rank: 5 },
    { equipment: 'ZJ-1007', score: 30, rank: 6 },
    { equipment: 'ZJ-1008', score: 85, rank: 7 },
    { equipment: 'ZJ-1009', score: 73, rank: 8 },
    { equipment: 'ZJ-1010', score: 65, rank: 9 },
    { equipment: 'ZJ-1011', score: 45, rank: 10 },
    { equipment: 'ZJ-1012', score: 38, rank: 11 },
    { equipment: 'ZJ-1013', score: 25, rank: 12 },
    { equipment: 'ZJ-1014', score: 88, rank: 13 },
    { equipment: 'ZJ-1015', score: 72, rank: 14 },
    { equipment: 'ZJ-1016', score: 68, rank: 15 },
    { equipment: 'ZJ-1017', score: 55, rank: 16 },
    { equipment: 'ZJ-1018', score: 48, rank: 17 },
    { equipment: 'ZJ-1019', score: 35, rank: 18 },
  ].sort((a, b) => b.score - a.score);

  const allHealthScoreData = healthScoreData || defaultAllHealthScoreData;

  // 获取圆圈颜色的函数
  const getCircleColor = (index: number) => {
    if (index === 0) return '#007FFF'; // 第1名：蓝色
    if (index === 1) return '#8B5CF6'; // 第2名：紫色
    if (index === 2) return '#FF9200'; // 第3名：黄色
    return '#666'; // 其他：灰色
  };

  // 分页状态
  const [currentPage, setCurrentPage] = useState(0);

  // 获取当前页数据
  const getCurrentPageData = () => {
    const startIndex = currentPage * pageSize;
    const endIndex = startIndex + pageSize;
    return allHealthScoreData.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      rank: startIndex + index + 1,
    }));
  };

  const currentHealthScoreData = getCurrentPageData();

  // 设备健康数据自动滚动定时器
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentPage((prevPage) => {
        const totalPages = Math.ceil(allHealthScoreData.length / pageSize);
        return (prevPage + 1) % totalPages;
      });
    }, scrollInterval);

    return () => clearInterval(timer);
  }, [pageSize, scrollInterval, allHealthScoreData.length]);

  return (
    <Card
      title="设备健康评分排行榜"
      bordered={false}
      className={styles.chartCard}
      headStyle={{
        fontSize: '22px',
        fontWeight: 'normal',
        fontFamily: 'PingFang SC',
        color: '#ffffff',
      }}
    >
      <div style={{ padding: '20px' }}>
        {allHealthScoreData.length > 0 ? (
          currentHealthScoreData.map((item, index) => (
            <div
              key={index}
              style={{
                marginBottom: '28px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              {/* 左侧：排名圆圈 + 设备名称 */}
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div
                  style={{
                    width: '28px',
                    height: '28px',
                    borderRadius: '50%',
                    backgroundColor: getCircleColor(index),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    color: '#fff',
                  }}
                >
                  {item.rank}
                </div>
                <span style={{ color: '#fff', fontSize: '14px' }}>{item.equipment}</span>
              </div>

              {/* 右侧：分数 + 进度条 */}
              <div style={{ display: 'flex', alignItems: 'center', width: '180px' }}>
                <span
                  style={{
                    color: index < 3 ? '#007FFF' : '#9E9E9E',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    marginRight: '12px',
                    minWidth: '50px',
                  }}
                >
                  {item.score}/100
                </span>
                <div style={{ flex: 1 }}>
                  <Progress
                    percent={item.score}
                    strokeColor={item.score >= 50 ? '#007FFF' : '#FF9200'}
                    trailColor="#333"
                    strokeWidth={6}
                    showInfo={false}
                  />
                </div>
              </div>
            </div>
          ))
        ) : (
          /* 无数据状态 */
          <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Empty
              image={<TrophyOutlined style={{ fontSize: 48, color: '#666' }} />}
              description={
                <span style={{ color: '#999', fontSize: '14px' }}>
                  暂无设备健康评分数据
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default EquipmentHealthRanking;
