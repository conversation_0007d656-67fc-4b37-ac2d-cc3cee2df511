/*
 * @Author: AI Assistant
 * @Date: 2025-01-01 00:00:00
 * @Description: 三维图表展示页面
 */

import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Row, Col, Button, Space, Upload, message } from 'antd';
import { UploadOutlined, FileTextOutlined } from '@ant-design/icons';
import ThreeDChart from '../Construction/component/ThreeDChart';

// 定义数据点类型
interface DataPoint {
  x: number;
  y: number;
  z: number;
}

const ThreeDPage: React.FC = () => {
  // 默认数据
  const defaultData: DataPoint[] = [
    {"x": 0, "y": 0, "z": 0}, 
    {"x": -0.38, "y": 4.58, "z": 1.96}, 
    {"x": -0.76, "y": 9.16, "z": 3.92}, 
    {"x": -1.14, "y": 13.74, "z": 5.88}, 
    {"x": -1.52, "y": 18.32, "z": 7.84}, 
    {"x": -1.9, "y": 22.9, "z": 9.8}, 
    {"x": -2.28, "y": 27.***************, "z": 11.760000000000002}, 
    {"x": -2.****************, "y": 32.059999999999995, "z": 13.720000000000002}, 
    {"x": -3.0399999999999996, "y": 36.63999999999999, "z": 15.680000000000003}, 
    {"x": -3.****************, "y": 41.21999999999999, "z": 17.640000000000004}, 
    {"x": -3.7999999999999994, "y": 45.79999999999999, "z": 19.600000000000005}, 
    {"x": -4.18, "y": 50.37999999999999, "z": 21.56000000000001}, 
    {"x": -4.56, "y": 54.95999999999999, "z": 23.520000000000007}, 
    {"x": -4.9399999999999995, "y": 59.539999999999985, "z": 25.480000000000008}, 
    {"x": -5.319999999999999, "y": 64.11999999999999, "z": 27.44000000000001}, 
    {"x": -5.699999999999999, "y": 68.69999999999999, "z": 29.40000000000001}, 
    {"x": -6.079999999999999, "y": 73.27999999999999, "z": 31.36000000000001}, 
    {"x": -6.459999999999999, "y": 77.85999999999999, "z": 33.32000000000001}, 
    {"x": -6.839999999999999, "y": 82.43999999999998, "z": 35.28000000000001}, 
    {"x": -7.219999999999999, "y": 87.01999999999998, "z": 37.24000000000001}
  ];

  const [chartData, setChartData] = useState<DataPoint[]>(defaultData);
  const [loading, setLoading] = useState(false);

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    setLoading(true);
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const jsonData = JSON.parse(content);
        
        // 验证数据格式
        if (Array.isArray(jsonData) && jsonData.length > 0) {
          const isValidData = jsonData.every(item => 
            typeof item === 'object' && 
            typeof item.x === 'number' && 
            typeof item.y === 'number' && 
            typeof item.z === 'number'
          );
          
          if (isValidData) {
            setChartData(jsonData);
            message.success(`成功加载 ${jsonData.length} 个数据点`);
          } else {
            message.error('数据格式不正确，请确保每个数据点包含 x, y, z 数值字段');
          }
        } else {
          message.error('数据格式不正确，请上传包含坐标点的 JSON 数组');
        }
      } catch (error) {
        message.error('文件解析失败，请确保上传的是有效的 JSON 文件');
      } finally {
        setLoading(false);
      }
    };
    
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  // 重置为默认数据
  const resetToDefault = () => {
    setChartData(defaultData);
    message.success('已重置为默认数据');
  };

  // 生成示例数据
  const generateSampleData = (type: string = 'spiral') => {
    const sampleData: DataPoint[] = [];
    const pointCount = 30;

    switch (type) {
      case 'spiral':
        for (let i = 0; i < pointCount; i++) {
          const t = i / pointCount;
          sampleData.push({
            x: Math.sin(t * Math.PI * 4) * 10,
            y: t * 50,
            z: Math.cos(t * Math.PI * 4) * 10
          });
        }
        message.success('已生成螺旋线示例数据');
        break;

      case 'wave':
        for (let i = 0; i < pointCount; i++) {
          const t = i / pointCount;
          sampleData.push({
            x: t * 40 - 20,
            y: Math.sin(t * Math.PI * 6) * 8,
            z: Math.cos(t * Math.PI * 3) * 5
          });
        }
        message.success('已生成波浪线示例数据');
        break;

      case 'random':
        for (let i = 0; i < pointCount; i++) {
          sampleData.push({
            x: (Math.random() - 0.5) * 40,
            y: Math.random() * 50,
            z: (Math.random() - 0.5) * 40
          });
        }
        message.success('已生成随机点示例数据');
        break;
    }

    setChartData(sampleData);
  };

  return (
    <PageContainer
      title="三维轨迹图表"
      subTitle="基于 Three.js 的三维数据可视化"
      extra={
        <Space>
          <Upload
            accept=".json"
            beforeUpload={handleFileUpload}
            showUploadList={false}
            loading={loading}
          >
            <Button icon={<UploadOutlined />} loading={loading}>
              上传数据文件
            </Button>
          </Upload>
          <Space.Compact>
            <Button onClick={() => generateSampleData('spiral')}>
              螺旋线
            </Button>
            <Button onClick={() => generateSampleData('wave')}>
              波浪线
            </Button>
            <Button onClick={() => generateSampleData('random')}>
              随机点
            </Button>
          </Space.Compact>
          <Button onClick={resetToDefault}>
            重置默认数据
          </Button>
        </Space>
      }
    >
      <Row gutter={[16, 16]}>
        {/* 主要图表区域 */}
        <Col span={24}>
          <ThreeDChart 
            data={chartData} 
            title={`三维轨迹图 (${chartData.length} 个数据点)`}
          />
        </Col>
        
        {/* 数据信息面板 */}
        <Col span={24}>
          <Card title="数据信息" size="small">
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                    {chartData.length}
                  </div>
                  <div style={{ color: '#666' }}>数据点总数</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {Math.max(...chartData.map(p => Math.sqrt(p.x*p.x + p.y*p.y + p.z*p.z))).toFixed(2)}
                  </div>
                  <div style={{ color: '#666' }}>最大距离</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                    {(Math.max(...chartData.map(p => p.x)) - Math.min(...chartData.map(p => p.x))).toFixed(2)}
                  </div>
                  <div style={{ color: '#666' }}>X轴跨度</div>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#eb2f96' }}>
                    {(Math.max(...chartData.map(p => p.y)) - Math.min(...chartData.map(p => p.y))).toFixed(2)}
                  </div>
                  <div style={{ color: '#666' }}>Y轴跨度</div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
        
        {/* 使用说明 */}
        <Col span={24}>
          <Card title="使用说明" size="small">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <h4>📁 数据格式</h4>
                <p>支持上传 JSON 格式的数据文件，格式如下：</p>
                <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px', fontSize: '12px' }}>
{`[
  {"x": 0, "y": 0, "z": 0},
  {"x": 1.5, "y": 2.3, "z": 0.8},
  ...
]`}
                </pre>
              </Col>
              <Col span={12}>
                <h4>🎮 操作指南</h4>
                <ul style={{ fontSize: '14px' }}>
                  <li><strong>旋转</strong>：按住鼠标左键拖拽</li>
                  <li><strong>平移</strong>：按住鼠标右键拖拽</li>
                  <li><strong>缩放</strong>：使用鼠标滚轮</li>
                  <li><strong>重置</strong>：点击"重置视图"按钮</li>
                  <li><strong>导出</strong>：点击"导出图片"保存当前视图</li>
                </ul>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default ThreeDPage;
