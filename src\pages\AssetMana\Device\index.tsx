import { EllipsisOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
    ProFormCascader
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Input, DatePicker, Form, Modal, Table, message, Breadcrumb, Row, Col, Select, Tag } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';

const { RangePicker } = DatePicker;

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}


export default () => {
    const [form] = Form.useForm();
    const ref = useRef();
    const { Option } = Select;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryType, setQueryType] = useState('0');
    const [rejectModalVisible, setRejectModalVisible] = useState(false);
    const [drillPlanOptions, setDrillPlanOptions] = useState<DrillPlanOption[]>([]);
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });
    const [currentDevice, setCurrentDevice] = useState({ deviceName: '' });
    const [departmentOptions, setDepartmentOptions] = useState([]);
    const [transferDevice, setTransferDevice] = useState({ id: '' });
    const [transferDeviceName, setTransferDeviceName] = useState({ deviceName: '' });
    const [selectedLocationNames, setSelectedLocationNames] = useState({
        miningFace: '',
        lane: '',
        drillSite: ''
    });
    const [selectedDepartment, setSelectedDepartment] = useState({
        id: '',
        name: ''
    });
    const [configAccountModalVisible, setConfigAccountModalVisible] = useState(false);
    const [currentConfigDevice, setCurrentConfigDevice] = useState({ id: '', deviceName: '' });
    const [accountForm] = Form.useForm();

    // 摄像头配置相关状态
    const [cameraConfigModalVisible, setCameraConfigModalVisible] = useState(false);
    const [currentCameraDevice, setCurrentCameraDevice] = useState({ id: '', deviceName: '' });
    const [cameraForm] = Form.useForm();

    const selectBefore = (
        <Select value={queryType} onChange={(value) => setQueryType(value)} >
            <Option value="0">单个查询</Option>
            <Option value="1">多个查询</Option>
        </Select>
    );

    const showModal = (op: any) => {
        console.log(op.deviceName, 'op')
        setCurrentDevice({ deviceName: op.deviceName, id: op.id });
        setRejectModalVisible(true);
    };

    const showTransferModal = (record: any) => {
        setTransferDevice({ id: record.id });
        setTransferDeviceName({ deviceName: record.deviceName });
        setIsModalOpen(true);
    };

    const showConfigAccountModal = (record: any) => {
        setCurrentConfigDevice({ id: record.id, deviceName: record.deviceName });
        setConfigAccountModalVisible(true);
    };

    // 显示摄像头配置弹框
    const showCameraConfigModal = async (record: any) => {
        setCurrentCameraDevice({ id: record.id, deviceName: record.deviceName });

        try {
            console.log('🎥 获取设备详情，设备ID:', record.id);

            // 调用设备详情接口获取现有配置
            const result = await getRequest('device/get_info', { id: record.id });
            const { status, msg, data } = result as any;

            console.log('🎥 设备详情API响应:', result);

            if (status === 0 && data) {
                // 如果有摄像头配置参数，解析并填充表单
                if (data.ysParam) {
                    try {
                        const ysParamData = JSON.parse(data.ysParam);
                        console.log('🎥 解析的摄像头配置:', ysParamData);

                        // 填充表单数据
                        cameraForm.setFieldsValue({
                            serialNumber: ysParamData.deviceSerial || '',
                            channelNumber: ysParamData.channelNo || ''
                        });
                    } catch (parseError) {
                        console.warn('🎥 解析摄像头配置参数失败:', parseError);
                        // 如果解析失败，清空表单
                        cameraForm.resetFields();
                    }
                } else {
                    // 如果没有配置参数，清空表单
                    cameraForm.resetFields();
                }
            } else {
                console.warn('🎥 获取设备详情失败:', msg);
                messageApi.warning('获取设备信息失败，将显示空白表单');
                cameraForm.resetFields();
            }
        } catch (error) {
            console.error('🎥 获取设备详情异常:', error);
            messageApi.error('获取设备信息异常');
            cameraForm.resetFields();
        }

        setCameraConfigModalVisible(true);
    };

    const handleOk = async () => {
        try {
            const formValues = await form.validateFields();
            console.log('formValues', formValues);

            let miningFace = '', lane = '', drillSite = '';

            if (formValues.drillingField && formValues.drillingField.length === 3) {
                const faceId = formValues.drillingField[0];
                const laneId = formValues.drillingField[1];
                const drillSiteId = formValues.drillingField[2];

                const faceOption = drillPlanOptions.find(option => option.value === faceId);
                if (faceOption) miningFace = faceOption.label;

                if (faceOption && faceOption.children) {
                    const laneOption = faceOption.children.find(option => option.value === laneId);
                    if (laneOption) lane = laneOption.label;

                    if (laneOption && laneOption.children) {
                        const drillSiteOption = laneOption.children.find(option => option.value === drillSiteId);
                        if (drillSiteOption) drillSite = drillSiteOption.label;
                    }
                }
            }

            // Find department name from the selected ID
            const selectedDeptOption = departmentOptions.find(option => option.value === formValues.team);
            const departmentName = selectedDeptOption ? selectedDeptOption.label : '';

            const postData = {
                id: transferDevice.id,
                deviceName: transferDeviceName.deviceName,
                departmentId: formValues.team,
                department: departmentName,
                faceId: formValues.drillingField[0],
                miningFace: miningFace,
                laneId: formValues.drillingField[1],
                lane: lane,
                drillSiteId: formValues.drillingField[2],
                drillSite: drillSite,
            };

            const result = await postRequest('device/post_modify', postData);
            const { status, msg } = result as any;

            if (status === 0) {
                messageApi.success('设备转移成功');
                setIsModalOpen(false);
                ref.current?.reload();
                form.resetFields();
            } else {
                messageApi.error(msg || '设备转移失败');
            }
        } catch (error) {
            if (error.errorFields) {
                messageApi.error('请完成所有必填字段');
            } else {
                messageApi.error('设备转移失败');
            }
        }
    };

    const handleCancel = () => {
        setIsModalOpen(false);
        form.resetFields();
    };

    const handleChange = (value: string) => {
        console.log(`selected ${value}`);
    };
    const handleRejectCancel = () => {
        setRejectModalVisible(false);
        // setRejectReason('');
        // setCurrentRejectId('');
    };

    const handleConfigAccountCancel = () => {
        setConfigAccountModalVisible(false);
        accountForm.resetFields();
    };

    const handleConfigAccountConfirm = async () => {
        try {
            const formValues = await accountForm.validateFields();
            console.log('配置账号数据:', formValues);

            // 这里可以调用配置账号的API
            // const result = await postRequest('device/config_account', {
            //     id: currentConfigDevice.id,
            //     account: formValues.account,
            //     password: formValues.password
            // });

            messageApi.success('配置账号成功');
            setConfigAccountModalVisible(false);
            accountForm.resetFields();
        } catch (error) {
            if (error.errorFields) {
                messageApi.error('请完成所有必填字段');
            } else {
                messageApi.error('配置账号失败');
            }
        }
    };

    // 摄像头配置取消处理
    const handleCameraConfigCancel = () => {
        setCameraConfigModalVisible(false);
        cameraForm.resetFields();
        console.log('🎥 摄像头配置弹框已取消');
    };

    // 摄像头配置确认处理
    const handleCameraConfigConfirm = async () => {
        try {
            const formValues = await cameraForm.validateFields();
            console.log('摄像头配置数据:', formValues);

            // 构造摄像头配置参数
            const ysParam = JSON.stringify({
                channelNo: formValues.channelNumber,
                deviceSerial: formValues.serialNumber
            });

            console.log('🎥 摄像头配置请求参数:', {
                id: currentCameraDevice.id,
                ysParam: ysParam,
                parsedYsParam: JSON.parse(ysParam)
            });

            // 调用摄像头配置API
            const result = await postRequest('device/post_modify', {
                id: currentCameraDevice.id,
                ysParam: ysParam
            });

            console.log('🎥 摄像头配置API响应:', result);

            const { status, msg } = result as any;
            if (status === 0) {
                messageApi.success('摄像头配置成功');
                setCameraConfigModalVisible(false);
                cameraForm.resetFields();
                // 刷新表格数据
                ref.current?.reload();
            } else {
                messageApi.error(msg || '摄像头配置失败');
            }
        } catch (error) {
            if (error.errorFields) {
                messageApi.error('请完成所有必填字段');
            } else {
                messageApi.error('摄像头配置失败');
            }
        }
    };
    // 获取钻场/drillsite/get_all_area
    const fetchDrillSite = async () => {
        const result = await getRequest('drillsite/get_all_area');
        const { data, status, msg } = result as any;
        if (status === 0 && data) {
            const transformedData = data.map((item: any) => ({
                value: item.id,
                label: item.name,
                children: item.children?.map((child: any) => ({
                    value: child.id,
                    label: child.name,
                    children: child.children?.map((subChild: any) => ({
                        value: subChild.id,
                        label: subChild.name
                    }))
                }))
            }));
            setDrillPlanOptions(transformedData);
        } else {
            messageApi.error(msg);
        }
    }
    const handleDelete = (record: any) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该设备吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const result = await getRequest('device/post_del', {
                        id: record.id
                    });
                    const { status, msg } = result as any;
                    if (status === 0) {
                        messageApi.success('删除成功');
                        ref.current?.reload();
                    } else {
                        messageApi.error(msg || '删除失败');
                    }
                } catch (error) {
                    messageApi.error('删除失败');
                }
            }
        });
    };
    // 修改设备名称
    const handleRejectConfirm = async () => {
        try {
            const result = await postRequest('device/post_modify', {
                id: currentDevice.id,
                deviceName: currentDevice.deviceName
            });
            const { status, msg } = result as any;
            if (status === 0) {
                messageApi.success('修改成功');
                setRejectModalVisible(false);
                ref.current?.reload();
            } else {
                messageApi.error(msg || '修改失败');
            }
        } catch (error) {
            messageApi.error('修改失败');
        }
    };
    const columns: ProColumns<TableListItem>[] = [
        {
            title: '设备名称',
            dataIndex: 'deviceName',
            search: false,
        },
        {
            title: '设备序列号',
            dataIndex: 'deviceCode',
            search: false,
        },
        {
            title: '设备型号',
            dataIndex: 'deviceModel',
            search: false,
        },
        // {
        //     title: '设备负责人',
        //     dataIndex: 'director',
        //     search: false,
        // },
        {
            title: '创建时间',
            dataIndex: 'creatAt',
            search: false,
        },
        {
            title: '最后更新时间',
            dataIndex: 'updateAt',
            search: false,
        },
        {
            title: '设备状态',
            dataIndex: 'deviceStatus',
            render: (text: any, record: any) => {
                let color = 'blue';
                let statusText = '工作中';

                if (text === 0) {
                    color = 'blue';
                    statusText = '工作中';
                } else if (text === 1) {
                    color = 'orange';
                    statusText = '停机';
                }

                return <Tag color={color}>{statusText}</Tag>;
            },
            search: false,
        },
        {
            title: '操作',
            dataIndex: 'option',
            valueType: 'option',
            render: (text, record,) => [
                <a key="modify"
                    onClick={() => { showModal(record) }}
                >
                    修改
                </a>,
                <a
                    key="transfer"
                    onClick={() => { showTransferModal(record) }}
                    style={{ marginLeft: '24px' }}
                >
                    设备转移
                </a>,
                // 配置账号
                <a
                    key="config"
                    onClick={() => { showConfigAccountModal(record) }}
                    style={{ marginLeft: '24px' }}
                >
                    配置账号
                </a>,
                // 摄像头配置
                <a
                    key="camera-config"
                    onClick={() => { showCameraConfigModal(record) }}
                    style={{ marginLeft: '24px' }}
                >
                    摄像头配置
                </a>,
                <a
                    key="config"
                    onClick={() => {
                        history.push(`/assetmana/device/detail/?id=${record.id}`)
                    }}
                    style={{ marginLeft: '24px' }}
                >
                    设备详情
                </a>
            ],
        },
        {
            title: '',
            dataIndex: 'deviceCode',
            colSize: 2,
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (
                    <>
                        <Input
                            value={status}
                            onChange={(e) => onchange(e.target.value)}
                            addonBefore={selectBefore}
                            placeholder={queryType === '0' ? '请输入设备序列号' : '支持以英文逗号,分隔组成的多个序列号'}
                        />
                    </>
                );
            },
        },
        {
            title: '',
            dataIndex: 'deviceName',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (<>
                    <Input
                        value={status}
                        onChange={(e) => onchange(e.target.value)}
                        placeholder='请输入设备名称'
                    />
                </>);
            },
        },
        // {
        //     title: '',
        //     dataIndex: 'text3',
        //     hideInTable: true,
        //     renderFormItem: (item, config, form) => {
        //         const label = item.dataIndex
        //         const status = form.getFieldValue(label);
        //         const onchange = (value) => {
        //             form.setFieldsValue({ [label]: value })
        //         }
        //         return (<>
        //             <Select
        //                 value={status}
        //                 onChange={(value) => onchange(value)}
        //                 placeholder='请选择型号'
        //                 options={[
        //                     { value: '1', label: '无鉴权' },
        //                     { value: '2', label: '自定义鉴权' },
        //                 ]}
        //             />
        //         </>);
        //     },
        // },
        // {
        //     title: '',
        //     dataIndex: 'currentState',
        //     hideInTable: true,
        //     renderFormItem: (item, config, form) => {
        //         const label = item.dataIndex
        //         const status = form.getFieldValue(label);
        //         const onchange = (value) => {
        //             form.setFieldsValue({ [label]: value })
        //         }
        //         return (<>
        //             <Select
        //                 value={status}
        //                 onChange={(value) => onchange(value)}
        //                 placeholder='请选择设备状态'
        //                 options={[
        //                     { value: 0, label: '打开' },
        //                     { value: 1, label: '关闭' },
        //                     { value: 2, label: '损坏' },
        //                 ]}
        //             />
        //         </>);
        //     },
        // },
        // {
        //     title: '',
        //     dataIndex: 'sort',
        //     hideInTable: true,
        //     renderFormItem: (item, config, form) => {
        //         const label = item.dataIndex
        //         const status = form.getFieldValue(label);
        //         const onchange = (value) => {
        //             form.setFieldsValue({ [label]: value })
        //         }
        //         return (<>
        //             <Select
        //                 value={status}
        //                 onChange={(value) => onchange(value)}
        //                 placeholder='创建时间'
        //                 options={[
        //                     { value: 0, label: '最新创建' },
        //                     { value: 1, label: '最早创建' },
        //                 ]}
        //             />
        //         </>);
        //     },
        // },
    ];

    const fetchDepartments = async () => {
        try {
            const result = await getRequest('department/get_all');
            const { data, status, msg } = result as any;
            if (status === 0 && data) {
                const formattedData = data.map(item => ({
                    value: item.id,
                    label: item.name
                }));
                setDepartmentOptions(formattedData);
            } else {
                messageApi.error(msg || '获取班组数据失败');
            }
        } catch (error) {
            messageApi.error('获取班组数据失败');
        }
    };

    useEffect(() => {
        fetchDrillSite(); // 获取钻场数据
        fetchDepartments(); // 获取班组数据
    }, []);

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '资产管理', },
                    { title: '设备管理', },
                ]}
            />
            <ProTable<TableListItem>
                style={{ marginTop: '24px' }}
                headerTitle='设备管理'
                actionRef={ref}
                columns={columns}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('6666666666666', params, sorter, filter);
                    const { current, pageSize, ...rest } = params;
                    let postData = {
                        page: current,
                        perPage: pageSize
                    }
                    const filteredData = {};
                    for (const key in rest) {
                        if (rest.hasOwnProperty(key)) {
                            const value = rest[key];
                            if (typeof value === 'number' && !isNaN(value) || typeof value === 'string' && value.trim() !== '') {
                                filteredData[key] = value;
                            }
                        }
                    }
                    postData = {
                        ...postData,
                        ...filteredData
                    }
                    const result = await postRequest('device/get_ls', postData);
                    const { data, status, msg } = result
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                rowKey="key"
                toolBarRender={() => [
                    // <Button
                    //     key="button"
                    //     onClick={() => { }}
                    //     type="primary"
                    // >
                    //     添加设备
                    // </Button>,
                ]}
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,
                    span: 3,
                }}
                form={{
                    initialValues: {
                        sort: 0
                    }
                }}
            />
            <Modal title="转移设备" style={{ minWidth: '750px' }} open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
                <Form form={form} layout="vertical" style={{ marginTop: '24px' }}>
                    <Form.Item>
                        <ProFormCascader
                            name="drillingField"
                            label="钻场"
                            placeholder="选择钻场"
                            fieldProps={{
                                options: drillPlanOptions,
                                changeOnSelect: false,
                                showSearch: true,
                                onChange: (values, selectedOptions) => {
                                    if (selectedOptions && selectedOptions.length === 3) {
                                        setSelectedLocationNames({
                                            miningFace: selectedOptions[0].label,
                                            lane: selectedOptions[1].label,
                                            drillSite: selectedOptions[2].label
                                        });
                                    }
                                }
                            }}
                            rules={[
                                { required: true, message: '请选择采面/巷道/钻场' },
                                {
                                    validator: (_, value) => {
                                        if (!value || value.length < 3) {
                                            return Promise.reject(new Error('请选择到钻场'));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        />
                    </Form.Item>
                    <Form.Item
                        label="班组"
                        name="team"
                        rules={[{ required: true, message: '请选择班组' }]}
                    >
                        <Select
                            placeholder="选择班组"
                            options={departmentOptions}
                            style={{ width: '100%' }}
                            onChange={(value, option) => {
                                setSelectedDepartment({
                                    id: value,
                                    name: option.label
                                });
                            }}
                        />
                    </Form.Item>
                </Form>
            </Modal>
            <Modal
                title="修改"
                open={rejectModalVisible}
                onOk={handleRejectConfirm}
                onCancel={handleRejectCancel}
            >
                <Form layout="vertical">
                    <Form.Item
                        label="设备名称"
                        required
                        rules={[{ required: true, message: '请输入设备名称' }]}
                    >
                        <Input
                            placeholder='请输入设备名称'
                            value={currentDevice.deviceName}
                            onChange={(e) => setCurrentDevice({ ...currentDevice, deviceName: e.target.value })}
                        />
                    </Form.Item>
                </Form>
            </Modal>
            <Modal
                title="配置账号"
                open={configAccountModalVisible}
                onOk={handleConfigAccountConfirm}
                onCancel={handleConfigAccountCancel}
                okText="确认"
                cancelText="取消"
            >
                <Form form={accountForm} layout="vertical" style={{ marginTop: '24px' }}>
                    <Form.Item
                        label="账号："
                        name="account"
                        rules={[{ required: true, message: '请输入账号' }]}
                    >
                        <Input
                            placeholder='请输入账号'
                            style={{ backgroundColor: '#2a2a2a', color: '#fff', border: '1px solid #555' }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="密码："
                        name="password"
                        rules={[{ required: true, message: '请输入密码' }]}
                    >
                        <Input.Password
                            placeholder='请输入密码'
                            style={{ backgroundColor: '#2a2a2a', color: '#fff', border: '1px solid #555' }}
                        />
                    </Form.Item>
                </Form>
            </Modal>

            {/* 摄像头配置弹框 */}
            <Modal
                title="摄像头配置"
                open={cameraConfigModalVisible}
                onOk={handleCameraConfigConfirm}
                onCancel={handleCameraConfigCancel}
                okText="确认"
                cancelText="取消"
            >
                <Form form={cameraForm} layout="vertical" style={{ marginTop: '24px' }}>
                    <Form.Item
                        label="设备序列号："
                        name="serialNumber"
                        rules={[{ required: true, message: '请输入设备序列号' }]}
                    >
                        <Input
                            placeholder='请输入设备序列号'
                            style={{ backgroundColor: '#2a2a2a', color: '#fff', border: '1px solid #555' }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="通道号："
                        name="channelNumber"
                        rules={[{ required: true, message: '请输入通道号' }]}
                    >
                        <Input
                            placeholder='请输入通道号'
                            style={{ backgroundColor: '#2a2a2a', color: '#fff', border: '1px solid #555' }}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};