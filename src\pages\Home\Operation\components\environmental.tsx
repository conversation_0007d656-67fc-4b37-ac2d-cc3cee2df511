import React, { useState, useEffect } from 'react';
import { Breadcrumb, message, Card, Row, Col, Tag, Result, Button, Spin, Steps } from 'antd';
import {
  CloudOutlined,
  ExperimentOutlined,
  FireOutlined
} from '@ant-design/icons';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';

interface EnvironmentalProps {
  deviceId: any; // 改为 any 类型，因为传入的是设备信息对象
}

const Environmental: React.FC<EnvironmentalProps> = ({ deviceId }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const { location } = history;

  const [initialValues, setInitialValues] = useState<any>({})
  const [msg, setMsg] = useState('')
  const [loading, setLoading] = useState<boolean>(false);

  // 当 deviceId 变化时，设置设备数据
  useEffect(() => {
    console.log('Environmental组件接收到的deviceId:', deviceId);
    if (deviceId && typeof deviceId === 'object') {
      setInitialValues(deviceId);
      console.log('Environmental组件设置的initialValues:', deviceId);
    } else {
      setInitialValues({});
    }
  }, [deviceId]);

  const sections = [
    {
      title: (
        <span>
          <CloudOutlined style={{ marginRight: '8px' }} />
          环境信息
        </span>
      ),
      items: [
        {
          label: (
            <span>
              <ExperimentOutlined style={{ marginRight: '6px' }} />
              一氧化碳
            </span>
          ),
          dataIndex: 'coConc',
        },
        {
          label: (
            <span>
              <FireOutlined style={{ marginRight: '6px' }} />
              甲烷
            </span>
          ),
          dataIndex: 'ch4Conc',
        },
      ]
    },
  ]

  // const getIDFromURLUsingSubstring = (url: string) => {
  //   const idParam = 'id=';
  //   const startIndex = url.indexOf(idParam);
  //   if (startIndex === -1) {
  //     return null
  //   }
  //   const valueStartIndex = startIndex + idParam.length;
  //   let valueEndIndex = url.indexOf('&', valueStartIndex);
  //   if (valueEndIndex === -1) {
  //     valueEndIndex = url.length;
  //   }
  //   return url.substring(valueStartIndex, valueEndIndex);
  // }
  // const getInfo = async (id) => {
  //   setLoading(true)
  //   const result = await getRequest('drillPlan/get_info', { id });
  //   const { data, status, msg } = result
  //   if (status === 0) {
  //     setInitialValues(data)
  //     setLoading(false)
  //   } else {
  //     setMsg(msg)
  //     setLoading(false)
  //     messageApi.open({
  //       type: 'error',
  //       content: msg,
  //     });
  //   }
  // }

  // useEffect(() => {
  //   if (location.search) {
  //     const url = location.search;
  //     const idValue = getIDFromURLUsingSubstring(url);
  //     getInfo(idValue)
  //   }
  // }, []);

  return (<>
    {contextHolder}
    <Spin spinning={loading}>
      {msg ? <Result
        status="500"
        title="error"
        subTitle={msg}
        extra={<Button type="primary" onClick={() => { history.go(-1) }}>返回上一层</Button>}
      /> :
        <div style={{ background: '#1f1f1f', padding: '24px', borderRadius: '10px' }}>
          {sections.map((section, index) => (
            <div key={index} style={{ marginBottom: '24px' }}>
              <div style={{
                color: 'rgba(255, 255, 255, 0.85)',
                fontSize: '18px',
                marginBottom: '24px'
              }}>
                {section.title}
              </div>
              <Row gutter={[16, 16]} >
                {section.items.map((item) => (
                  <Col span={4} key={item.dataIndex}>
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 10,
                      marginBottom: 5
                    }}>
                      <div style={{ color: 'rgba(255, 255, 255, 0.45)', fontSize: '16px' }}>
                        {item.label}
                      </div>
                      <div style={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: '16px' }}>
                        {(initialValues && initialValues[item.dataIndex]) || '-'}
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          ))}
        </div>
      }
    </Spin>
  </>)
}

export default Environmental;