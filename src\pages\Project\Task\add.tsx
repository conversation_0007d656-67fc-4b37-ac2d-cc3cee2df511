import React, { useRef } from 'react';
import { message } from 'antd';
import { history } from 'umi';
import { Form, Breadcrumb, Typography, Card, Divider, Space, Button } from 'antd';
import { postRequest } from '@/services/api/api';
import ThreeForm, { Column, ThreeFormRef } from '@/components/ThreeForm';

const { Title } = Typography;

interface FormData {
  taskNumber: string;
  taskDescription: string;
  constructionPlan: string;
  responsibleTeam: string;
  taskName: string;
  issueDate: string;
  constructionRequirements: string;
  taskReceiver: string;
  planStartDate: string;
  constructionDrawings: string;
  actualStartDate: string;
  planEndDate: string;
  taskIssuer: string;
  remark: string;
}

interface ApiResponse {
  status: number;
  msg?: string;
  data?: any;
}

const TaskAdd: React.FC = () => {
  const [form] = Form.useForm<FormData>();
  const formRef = useRef<ThreeFormRef>(null);
  const [messageApi, contextHolder] = message.useMessage();

  const columns1: Column[] = [
    {
      label: '施工任务下发编号',
      dataIndex: 'taskNumber',
      type: 'text',
      rules: [{ required: true, message: '请输入施工任务编号' }],
    },
    {
      label: '施工任务名称',
      dataIndex: 'taskName',
      type: 'text',
      rules: [{ required: true, message: '请输入施工任务名称' }],
    },
    {
      label: '施工任务描述',
      dataIndex: 'description',
      type: 'text',
      rules: [{ required: true, message: '请输入施工任务描述' }],
    },
  ];

  const columns2: Column[] = [
    {
      label: '责任施工队/个人',
      dataIndex: 'responsibleTeam',
      type: 'text',
      rules: [{ required: true, message: '请输入施工任务描述' }],
    },
    {
      label: '施工要求',
      dataIndex: 'constructionRequirements',
      type: 'text',
      rules: [{ required: true, message: '请输入施工要求' }],
    },
    {
      label: '施工图纸',
      dataIndex: 'constructionDrawings',
      type: 'text',
      rules: [{ required: true, message: '请输入施工图纸' }],
    },
    {
      label: '任务下发日期',
      dataIndex: 'taskDate',
      type: 'date',
      rules: [{ required: true, message: '请选择任务下发日期' }],
    },
    {
      label: '计划开始日期',
      dataIndex: 'startTime',
      type: 'date',
      rules: [{ required: true, message: '请选择计划开始日期' }],
    },
   
    {
      label: '计划完成日期',
      dataIndex: 'endTime',
      type: 'date',
      rules: [{ required: true, message: '请选择计划完成日期' }],
    },
    {
      label: '备注',
      dataIndex: 'remark',
      type: 'textarea',
      rules: [{ required: true, message: '请输入备注' }],
    },
  ];

  // 表单提交处理
  const onFinish = async (values: FormData) => {
    try {
      const result = await postRequest<ApiResponse>('task/post_add', values);
      if (result.status === 0) {
        messageApi.success('添加成功');
        history.push('/project/task');
        // history.go(-1);
      } else {
        messageApi.error(result.msg || '添加失败');
      }
    } catch (error) {
      messageApi.error('添加失败');
    }
  };

  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        console.log(values);
        
        onFinish(values);
      })
      .catch(info => {
        messageApi.error('请检查表单填写是否完整');
      });
  };

  return (
    <>
      {contextHolder}
      <Breadcrumb
        items={[
          { title: '首页' },
          { title: '施工管理' },
          { title: '施工计划' },
          { title: '下发施工计划' }
        ]}
      />      
        <Card style={{ marginTop: '24px' }}>
          <Title level={5}>基础信息</Title>
          <Divider />
          <ThreeForm
            form={form}
            columns={columns1}
            onFinish={onFinish}
          />
        </Card>
        
        <Card style={{ marginTop: '24px' }}>
          <Title level={5}>基础信息</Title>
          <Divider />
          <ThreeForm
            form={form}
            columns={columns2}
            onFinish={onFinish}
          />
        </Card>

        <Card style={{ textAlign: 'right', marginTop: '30px'}}>
          <Space>
            <Button onClick={() => history.go(-1)}>取消</Button>
            <Button type="primary" onClick={handleSubmit}>
              提交
            </Button>
          </Space>
        </Card>
     
    </>
  );
};

export default TaskAdd; 