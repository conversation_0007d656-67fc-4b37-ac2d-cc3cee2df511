import React from 'react';
import { Button, Modal, message,Input,Select,DatePicker,Tag } from 'antd';
import { history } from 'umi';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest,getRequest } from '@/services/api/api';
import { setTempData } from '@/utils/storage';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { RangePicker } = DatePicker;

interface DrillProps {
  deviceId: string;
}


// 定义表格数据类型
interface TableListItem {
  id: number;
  taskNumber: string;
  taskName: string;
  description: string;
  responsibleTeam: string;
  taskDate: string;
  startTime:string;
  endTime:string;
  constructionRequirements:string;
  constructionDrawings:string;
  taskPerson:string;
  taskRecipient:string;
  receiptDate:string;
  remark:string;
  state:number;
}
interface ApiResponse<T = any> {
    status: number;
    msg?: string;
    data?: T;
    total?: number;
  }
  
const Drill: React.FC<DrillProps> = ({ deviceId }) => {
  console.log(deviceId, 'deviceIddeviceId')
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();
  
  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
    {
      title: '钻机名称',
      dataIndex: 'drillName',
      fixed: 'left',
      ellipsis: true,
      search: false,
    },
    {
      title: '施工状态',
      dataIndex: 'status',
      ellipsis: true,
      width: 200,
      render: (text1,record) => {
          let color = 'green';
          let text = '下发任务';
          if (record.status === 0) {
            color = 'orange';
            text='下发任务';
          } else if (record.status === 1 || record.status === 2 || record.status === 3 || record.status === 4) {
            color = 'orange';
            text='施工中';
          } else if (record.status === 5) {
            color = 'red';
            text='终孔';
          } else if (record.status === 6) {
            color = 'green';
            text='已完成';
          } else if (record.status === 7) {
            color = 'default';
            text='废孔';
          }
          return <Tag color={color}>{text}</Tag>;
        },
      },
    // {
    //   title: '班次',
    //   dataIndex: 'shift',
    //   ellipsis: true,
    //   search: false,
    // },
      {
      title: '施工地点',
      dataIndex: 'position',
      // width:260,
      // fixed: 'left',
      ellipsis: true,
      search: false,
    },
    {
      title: '孔号',
      dataIndex: 'holeNumber',
      ellipsis: true,
      search: false,
    },
    {
      title: '施工位置',
      dataIndex: 'constructionLocation',
      ellipsis: true,
      width: 120,
      search: false,
    },
    {
      title: '施工地点',
      dataIndex: 'position',
      ellipsis: true,
      width: 120,
      search: false,
    },
    {
      title: '孔深（m）',
    //   width:140,
      dataIndex: 'holeDepth',
      ellipsis: true,
      search: false,
    },
    {
      title: '孔径（mm）',
      dataIndex: 'holeDiameter',
      ellipsis: true,
      search: false,
    },
    {
      title: '开孔角度（°）',
      dataIndex: 'holeAngle',
    //   width:120,
      ellipsis: true,
      search: false,
    },
    {
        title: '方位（°）',
        dataIndex: 'direction',
        // width:160,
        ellipsis: true,
        search: false,
    },
    {
        title: '开孔高度（m）',
        dataIndex: 'holeHeight',
        // width:160,
        ellipsis: true,
        search: false,
    },
    {
        title: '见煤距离（m）',
        dataIndex: 'coalDistance',
        // width:160,
        ellipsis: true,
        search: false,
    },
    {
        title: '见岩距离（m）',
        dataIndex: 'rockDistance',
        // width:160,
        ellipsis: true,
        search: false,
    },
    // {
    //     title: '扩孔起始距离',
    //     dataIndex: 'reamingStartDistance',
    //     ellipsis: true,
    //     search: false,
    // },
    // {
    //     title: '打钻起始距离',
    //     dataIndex: 'drillingStartDistance',
    //     ellipsis: true,
    //     search: false,
    // },
    // {
    //     title: '预计出煤量',
    //     dataIndex: 'estimatedCoalOutput',
    //     ellipsis: true,
    //     search: false,
    // },
    {
        title: '开孔角度误差（°）',
        dataIndex: 'holeAngleError',
        ellipsis: true,
        search: false,
    },
    {
        title: '开孔方位误差（°）',
        dataIndex: 'holeDirectionError',
        ellipsis: true,
        search: false,
    },
    // {
    //     title: '开孔高度误差',
    //     dataIndex: 'holeHeightError',
    //     ellipsis: true,
    //     search: false,
    // },
    {
        title: '孔深误差（m）',
        dataIndex: 'holeDepthError',
        ellipsis: true,
        search: false,
    },
  ];

  return (
    <>
    {contextHolder}
    <ProTable<TableListItem>
      headerTitle=""
      actionRef={actionRef}
      search={false}
      pagination={false}
      request={async (params, sorter, filter) => {
        let postData = {
            page: 1,
            perPage: 10,
            number:deviceId.deviceId.deviceCode?deviceId.deviceId.deviceCode:''
        }
        const result = await postRequest('drill/get_ls', postData);
        const { data, status, msg } = result
        let dataSource
        let total
        if (status === 0) {
            dataSource = data.items
            total = data.total
        } else {
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
        return Promise.resolve({
            data: dataSource,
            total: total,
            success: true,
        });
    }}
      rowKey="id"
      scroll={{ x: 2400 }}
      columns={columns}

     
    />
   
    </>
  );
  
};

export default Drill;
