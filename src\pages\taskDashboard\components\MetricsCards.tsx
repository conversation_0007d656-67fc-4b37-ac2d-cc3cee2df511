/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\MetricsCards.tsx
 * @Description: 核心指标卡片组件 - 展示总进尺、运行钻机数、总任务数、异常事件、设备开机数量等关键指标
 */

import { Card, Col, Image, Row, Statistic } from 'antd';
import React from 'react';
import styles from '../newTaskDashboard.less';

interface MetricsCardsProps {
  // 总进尺相关数据
  totalDistance: string;
  distanceChange: string;

  // 运行钻机数相关数据
  stationCount: string;
  operationRate: string;

  // 总任务数相关数据
  taskCount: string;
  taskCompletionRate: string;

  // 异常事件相关数据
  abnormalCount: string;
  abnormalChange: string;

  // 设备开机数量相关数据
  operatingSum: string;
  operatingRate: string;
}

const MetricsCards: React.FC<MetricsCardsProps> = ({
  totalDistance,
  distanceChange,
  stationCount,
  operationRate,
  taskCount,
  taskCompletionRate,
  abnormalCount,
  abnormalChange,
  operatingSum,
  operatingRate,
}) => {
  return (
    <Row gutter={[16, 16]} style={{ display: 'flex', flexWrap: 'wrap' }}>
      {/* 总进尺 */}
      <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
        <Card bordered={false} className={styles.dashboardCard} style={{ height: '214px' }}>
          <div className={styles.cardHeader}>
            <Statistic
              title="总进尺"
              value={totalDistance}
              precision={1}
              suffix="米"
              valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
            />
            <div>
              <span>
                <Image width={24} src="/icons/chizi.png" preview={false} />
              </span>
            </div>
          </div>
          <div className={styles.changeInfo}>
            <span>
              较昨日 {distanceChange} 米{' '}
              <Image width={14} src="/icons/yishang.png" preview={false} />
            </span>
          </div>
          <div className={styles.detailButton}>
            <span style={{ color: '#1890ff', cursor: 'pointer' }}>查看详情</span>
          </div>
        </Card>
      </Col>

      {/* 运行钻机数 */}
      <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
        <Card bordered={false} className={styles.dashboardCard} style={{ height: '214px' }}>
          <div className={styles.cardHeader}>
            <Statistic
              title="运行钻机数"
              value={stationCount}
              valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
            />
            <div>
              <span>
                <Image width={24} src="/icons/zuanji.png" preview={false} />
              </span>
            </div>
          </div>
          <div className={styles.changeInfo}>
            <span>运行率 {operationRate}%</span>
          </div>
          <div className={styles.detailButton}>
            <span style={{ color: '#1890ff', cursor: 'pointer' }}>查看详情</span>
          </div>
        </Card>
      </Col>

      {/* 总任务数 */}
      <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
        <Card bordered={false} className={styles.dashboardCard} style={{ height: '214px' }}>
          <div className={styles.cardHeader}>
            <Statistic
              title="总任务数"
              value={taskCount}
              valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
            />
            <div>
              <span>
                <Image width={24} src="/icons/renwu.png" preview={false} />
              </span>
            </div>
          </div>
          <div className={styles.changeInfo}>
            <span>任务完成率: {taskCompletionRate}%</span>
          </div>
          <div className={styles.detailButton}>
            <span style={{ color: '#1890ff', cursor: 'pointer' }}>查看详情</span>
          </div>
        </Card>
      </Col>

      {/* 异常事件 */}
      <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
        <Card bordered={false} className={styles.dashboardCard} style={{ height: '214px' }}>
          <div className={styles.cardHeader}>
            <Statistic
              title="异常事件"
              value={abnormalCount}
              suffix="起"
              valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
            />
            <div style={{ marginLeft: '20px' }}>
              <span>
                <Image width={24} src="/icons/yichang.png" preview={false} />
              </span>
            </div>
          </div>
          <div className={styles.changeInfo}>
            <span>
              较昨日 {abnormalChange} 起{' '}
              <Image width={14} src="/icons/yishang.png" preview={false} />
            </span>
          </div>
          <div className={styles.detailButton}>
            <span style={{ color: '#1890ff', cursor: 'pointer' }}>查看详情</span>
          </div>
        </Card>
      </Col>

      {/* 设备开机数量 */}
      <Col xs={24} sm={12} md={4} lg={4}>
        <Card bordered={false} className={styles.dashboardCard} style={{ height: '214px' }}>
          <div className={styles.cardHeader}>
            <Statistic
              title="设备开机数量"
              value={operatingSum}
              suffix="台"
              valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
            />
            <div>
              <span>
                <Image width={24} src="/icons/operatingSum.png" preview={false} />
              </span>
            </div>
          </div>
          <div className={styles.changeInfo}>
            <span>开机率 {operatingRate}%</span>
          </div>
          <div className={styles.detailButton}>
            <span style={{ color: '#1890ff', cursor: 'pointer' }}>查看详情</span>
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default MetricsCards;
