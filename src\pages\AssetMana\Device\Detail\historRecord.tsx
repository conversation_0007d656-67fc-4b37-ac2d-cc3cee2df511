import { ProTable } from '@ant-design/pro-components';
import type { ActionType} from '@ant-design/pro-components';
import {message, Flex, Select} from 'antd';
import { useState, useRef,useEffect } from 'react';
import { postRequest } from '@/services/api/api';

interface IoTPointProps {
  number: string;
}

type TableListItem = {
  id: number;
  title: string;
  var: string;
  type: number;
  createdAt: string;
  updatedAt: string;
};

export default ({ number,params }: IoTPointProps) => {

  const ref = useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();
  const [queryForm, setQueryForm] = useState('');
  const [options, setOptions] = useState<{label:string,value:number}[]>([]);
  const [selectedType, setSelectedType] = useState<{label:string,value:number}>();
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  useEffect(() => {
  getVariableName()
  }, [number]);
  // 获取全部变量名
  const getVariableName = async () => {
    // console.log('1111111111111111');
    setIsDataLoaded(false);
    const result = await postRequest('device_data/get_all', {
      number: number,
    });
    const { data, status, msg } = result as any;
    if (status === 0) {
      // console.log(data,'获取的数据');
      if(params){
        setSelectedType({
          label: params.title,
          value: params.var
        });
        setQueryForm(params.var)
      }else{
        setSelectedType({
          label: data[0].title,
          value: data[0].var
        });
        setQueryForm(data[0].var)

      }
      setOptions(data);
    }else{
      messageApi.error(msg);
    }
    setIsDataLoaded(true);
    // console.log(result,'result122222212');
  }
// 处理类型变化
   const handleTypeChange = (value: string,option:any) => {
    console.log(value,'valuevaluevalue');
    console.log(option,'optionoptionoption');
    setQueryForm(value)
    setSelectedType({
      label: option.label,
      value: option.value
    });
    ref.current?.reload();
  };
  const columns= [
    {
      title: '时间',
      dataIndex: 'time',
      width: 150,
      ellipsis: true,
    },
    {
      title: selectedType?.label,
      dataIndex: 'value',
      width: 150,
      ellipsis: true,
    },
  ];
  return (
    <>
      {contextHolder}
      {isDataLoaded && (
        <ProTable<TableListItem>
          actionRef={ref}
          columns={columns}
          options={false}
          request={async (params) => {
            console.log('222222',queryForm)
            const postData = {
              page: params.current,
              perPage: params.pageSize,
              number:number.deviceCode || number,
              keyWord:(queryForm ? queryForm  : ''),
            };
            try {
              const result = await postRequest('device_data/get_history', postData);
              const { data, status, msg } = result as any;
              if (status === 0) {
                return {
                  data: data.items,
                  total: data.total,
                  success: true,
                };
              }
              messageApi.error(msg || '获取数据失败');
              return {
                data: [],
                total: 0,
                success: false,
              };
            } catch (error) {
              messageApi.error('请求失败');
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          }}
          toolbar={{
            search: <Flex>
              <Select 
              style={{ width: 320 }} 
              value={selectedType?.label}
              onChange={handleTypeChange}
              options={options.map((item:any)=>({
                label:item.title,
                value:item.var
              }))}
            />
            </Flex>,
          }}
          rowKey="id"
          search={false}
        />
      )}
    </>
  );
}; 