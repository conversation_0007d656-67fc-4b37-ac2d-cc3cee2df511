/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-04-03 15:07:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-07 09:11:10
 * @FilePath: \diy_tfl_pc\src\pages\report\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { message, Breadcrumb, Card, Col, Row, Tag, Spin } from 'antd';
import React, { useState, useEffect } from 'react';
import { getRequest } from '@/services/api/api';
import DailyReport from './component/dailyReport';
import WeeklyReport from './component/weeklyReport';
import MonthlyReport from './component/monthlyReport';
export default () => {
    // const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>();
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [activeTabKey, setActiveTabKey] = useState('0');
    // const [infoLoaded, setInfoLoaded] = useState(false);

    const tabListNoTitle = [
        {
            label: '日报表',
            key: '0',
        },
        {
            label: '周报表',
            key: '1',
        },
        {
            label: '月报表',
            key: '2',
        },
    ];

    const contentListNoTitle: Record<string, React.ReactNode> = {
        '0': <DailyReport/> ,
        '1': <WeeklyReport/>,
        '2': <MonthlyReport/>,
    };

    const onTabChange = (key: string) => {
        setActiveTabKey(key);
    };

    return (
        <>
            {contextHolder}
            {/* <Card bordered={false} loading={loading}> */}
                <Breadcrumb
                    items={[
                        { title: '首页', },
                        { title: '报表', },
                    ]}
                />
            {/* </Card> */}

            <Card
                bordered={false}
                tabList={tabListNoTitle}
                activeTabKey={activeTabKey}
                onTabChange={onTabChange}
                style={{ marginTop: '24px' }}
                tabProps={{
                    size: 'middle',
                }}
            >
                {contentListNoTitle[activeTabKey]}
            </Card>
        </>
    );
};