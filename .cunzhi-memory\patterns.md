# 常用模式和最佳实践

- 施工报告模块API对接完成：使用getRequest调用/drill/get_construction_report接口，处理嵌套data结构，能耗信息和计划对比数据正确映射，空值显示为空白而不是"-"，表格render函数正确处理null值显示
- 成功创建卡片管理模块完整CRUD功能：1.类型定义(types.ts)包含CardItem、CardFormData等接口和状态枚举；2.API服务(service.ts)包含增删改查、绑定解绑员工等接口；3.主列表页面(index.tsx)使用ProTable实现搜索筛选分页；4.表单组件(CardForm.tsx)使用ProForm实现数据验证；5.添加页面(add.tsx)和编辑页面(edit.tsx)复用表单组件；6.路由配置已更新，添加/system/cardmanagement相关路由；参考静态IP管理模块的代码结构和实现方式，遵循项目现有的代码规范和架构模式
- 为卡片管理模块创建了完整的模拟数据：1.创建mock/cardManagement.mock.js文件，包含5条示例卡片数据；2.实现了所有CRUD接口的模拟：/card/get_ls(列表)、/card/get_info(详情)、/card/post_add(添加)、/card/post_modify(修改)、/card/post_del(删除)、/card/post_bind(绑定员工)、/card/post_unbind(解绑员工)；3.支持卡片编号、状态、员工姓名的搜索筛选；4.包含完整的数据验证和错误处理；5.注意：需要使用npm run start启动项目以启用mock功能，start:dev脚本会禁用mock
- 卡片管理模块改用假数据实现：1.删除了mock/cardManagement.mock.js文件；2.在service.ts中直接定义mockCards假数据数组，包含5条示例卡片；3.所有API方法改为直接操作假数据：getList支持搜索筛选分页、getDetail获取详情、add添加卡片、update修改卡片、delete删除卡片、bindEmployee绑定员工、unbindEmployee解绑员工；4.每个方法都添加了模拟网络延迟(200-500ms)；5.包含完整的数据验证和错误处理；6.数据会在内存中持久化，页面刷新后重置
