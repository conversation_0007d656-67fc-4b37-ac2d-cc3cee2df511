# 常用模式和最佳实践

- 施工报告模块API对接完成：使用getRequest调用/drill/get_construction_report接口，处理嵌套data结构，能耗信息和计划对比数据正确映射，空值显示为空白而不是"-"，表格render函数正确处理null值显示
- 成功创建卡片管理模块完整CRUD功能：1.类型定义(types.ts)包含CardItem、CardFormData等接口和状态枚举；2.API服务(service.ts)包含增删改查、绑定解绑员工等接口；3.主列表页面(index.tsx)使用ProTable实现搜索筛选分页；4.表单组件(CardForm.tsx)使用ProForm实现数据验证；5.添加页面(add.tsx)和编辑页面(edit.tsx)复用表单组件；6.路由配置已更新，添加/system/cardmanagement相关路由；参考静态IP管理模块的代码结构和实现方式，遵循项目现有的代码规范和架构模式
