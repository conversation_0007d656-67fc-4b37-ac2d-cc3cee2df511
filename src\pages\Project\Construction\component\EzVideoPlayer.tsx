import React, { useEffect, useRef, useState } from 'react';
import { Spin } from 'antd';
import { EZUIKitPlayer } from 'ezuikit-js';
import { getRequest } from '@/services/api/api';

interface EzVideoPlayerProps {
  ezOpenUrl: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  style?: React.CSSProperties;
  onCapture?: (type: 'image' | 'video', data: any) => void; // 抓拍回调
  isLive?: boolean; // 是否为直播模式，true时使用pcLive模板，false时使用pcRec模板
}

interface AccessTokenResponse {
  status: number;
  msg: string;
  data: string;
}

const EzVideoPlayer: React.FC<EzVideoPlayerProps> = ({
  ezOpenUrl,
  width = '100%',
  height = '100%',
  className,
  style,
  onCapture,
  isLive = false // 默认为录像模式
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<any>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null); // 用于清理定时器
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');



  // 获取萤石云访问令牌
  const fetchAccessToken = async (): Promise<string> => {
    try {
      const response = await getRequest('api/ys/get_access_token', {});
      const { status, msg, data } = response as AccessTokenResponse;

      if (status === 0 && data) {
        return data;
      } else {
        throw new Error(msg || '获取访问令牌失败');
      }
    } catch (error) {
      console.error('获取萤石云访问令牌失败:', error);
      throw error;
    }
  };

  // 彻底清理播放器实例和DOM
  const cleanupPlayer = async () => {
    if (playerRef.current) {
      try {
        console.log('🧹 开始清理播放器实例');

        // 移除所有事件监听器
        if (playerRef.current.eventEmitter && typeof playerRef.current.eventEmitter.off === 'function') {
          try {
            playerRef.current.eventEmitter.off('capturePicture');
            playerRef.current.eventEmitter.off('startSave');
            playerRef.current.eventEmitter.off('stopSave');
            playerRef.current.eventEmitter.off('play');
            playerRef.current.eventEmitter.off('pause');
            playerRef.current.eventEmitter.off('stop');
            playerRef.current.eventEmitter.off('firstFrameDisplay');
            console.log('✅ 事件监听器清理完成');
          } catch (eventErr) {
            console.warn('⚠️ 移除事件监听器失败:', eventErr);
          }
        }

        // 停止播放器
        if (typeof playerRef.current.stop === 'function') {
          playerRef.current.stop();
          console.log('✅ 播放器已停止');
        }

        // 销毁播放器实例
        if (typeof playerRef.current.destroy === 'function') {
          playerRef.current.destroy();
          console.log('✅ 播放器实例已销毁');
        }

        playerRef.current = null;
      } catch (err) {
        console.warn('⚠️ 清理播放器时出错:', err);
      }
    }

    // 清理DOM内容
    if (containerRef.current) {
      try {
        // 清空容器内容
        containerRef.current.innerHTML = '';
        // 移除ID属性，避免冲突
        containerRef.current.removeAttribute('id');
        console.log('✅ DOM容器已清理');
      } catch (domErr) {
        console.warn('⚠️ 清理DOM时出错:', domErr);
      }
    }
  };

  // 使用指定 URL 初始化萤石云播放器
  const initializePlayerWithUrl = async (url: string) => {
    if (!containerRef.current || !url) return;

    try {
      setLoading(true);
      setError('');

      console.log('正在初始化萤石云播放器，URL:', url);

      // 获取访问令牌
      const token = await fetchAccessToken();

      console.log('获取到 AccessToken:', token);

      // 检查容器是否存在
      if (!containerRef.current) {
        console.error('播放器容器未找到');
        setError('播放器容器初始化失败');
        setLoading(false);
        return;
      }

      // {{ AURA: Modify - 简化初始化，依赖组件重新创建 }}
      // 生成唯一的容器ID
      const containerId = `ez-video-player-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

      // 设置容器ID
      containerRef.current.id = containerId;

      console.log('创建播放器实例，容器ID:', containerId);

      // {{ AURA: Modify - 使用更安全的播放器配置 }}
      console.log('🎬 准备创建播放器实例，容器ID:', containerId);

      // 验证容器确实存在且有正确的ID
      const containerElement = document.getElementById(containerId);
      if (!containerElement) {
        throw new Error(`容器元素未找到: ${containerId}`);
      }

      console.log('✅ 容器验证通过，开始创建播放器');

      // {{ AURA: Modify - 使用try-catch包装播放器创建，防止同步错误 }}
      let player;
      try {
        player = new EZUIKitPlayer({
          id: containerId,
          accessToken: token,
          url: url,
          template: isLive ? 'pcLive' : 'pcRec', // 直播使用pcLive模板，录像使用pcRec模板
          audio: true,
          handleSuccess: () => {
            console.log('✅ 萤石云播放器初始化成功');
            setLoading(false);
            setError('');
          },
          handleError: (err: any) => {
            console.error('❌ 萤石云播放器错误:', err);
            // 不同错误类型的处理
            if (err && err.code === 5) {
              setError('设备密码错误');
            } else if (err && err.message && err.message.includes('network')) {
              setError('网络连接失败');
            } else {
              setError('暂无视频');
            }
            setLoading(false);
          }
        });
      } catch (syncError) {
        console.error('🚨 播放器创建时发生同步错误:', syncError);
        setError('播放器初始化失败');
        setLoading(false);
        throw syncError; // 重新抛出，让外层catch捕获
      }

      // 等待播放器实例创建完成
      await new Promise(resolve => setTimeout(resolve, 200));

      playerRef.current = player;

      // 监听播放器事件
      player.eventEmitter.on('firstFrameDisplay', () => {
        console.log('首帧渲染完成');
        setLoading(false);
      });

      player.eventEmitter.on('play', () => {
        console.log('开始播放');
      });

      player.eventEmitter.on('pause', () => {
        console.log('暂停播放');
      });

      player.eventEmitter.on('stop', () => {
        console.log('停止播放');
      });

      // 监听抓拍事件
      const handleCapturePicture = (eventData: any) => {
        console.log('萤石播放器抓拍图片事件:', eventData);
        if (onCapture) {
          // 传递完整的事件数据给父组件
          onCapture('image', eventData);
        }
      };

      const handleStartSave = (eventData: any) => {
        console.log('萤石播放器开始录像:', eventData);
      };

      const handleStopSave = (eventData: any) => {
        console.log('萤石播放器停止录像:', eventData);
        if (onCapture) {
          // 传递完整的事件数据给父组件
          onCapture('video', eventData);
        }
      };

      player.eventEmitter.on('capturePicture', handleCapturePicture);
      player.eventEmitter.on('startSave', handleStartSave);
      player.eventEmitter.on('stopSave', handleStopSave);

    } catch (error) {
      console.error('初始化萤石云播放器失败:', error);
      setError('暂无视频');
      setLoading(false);
    }
  };



  // {{ AURA: Modify - 简化初始化策略，依赖key属性重新创建 }}
  useEffect(() => {
    console.log('🎬 EzVideoPlayer useEffect 触发, ezOpenUrl:', ezOpenUrl);

    // 清理之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (ezOpenUrl) {
      console.log('🎬 准备初始化播放器，URL:', ezOpenUrl);

      // {{ AURA: Modify - 添加全局错误捕获，防止页面崩溃 }}
      timeoutRef.current = setTimeout(async () => {
        try {
          await initializePlayerWithUrl(ezOpenUrl);
        } catch (error) {
          console.error('🚨 播放器初始化过程中出现未捕获错误:', error);
          setError('视频加载失败，请稍后重试');
          setLoading(false);
        }
      }, 200);
    } else {
      setError('暂无视频');
      setLoading(false);
    }

    // {{ AURA: Modify - 使用统一的清理函数 }}
    return () => {
      console.log('🎬 EzVideoPlayer useEffect 清理');

      // 清理定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // 清理播放器
      cleanupPlayer();
    };
  }, [ezOpenUrl]);



  if (error) {
    return (
      <div
        className={className}
        style={{
          width,
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          color: '#999',
          fontSize: '16px'
        }}
      >
        暂无视频
      </div>
    );
  }

  return (
    <div
      className={className}
      style={{
        position: 'relative',
        width: width,
        height: height,
        minHeight: typeof height === 'string' ? '450px' : height,
        backgroundColor: '#000',
        ...style
      }}
    >
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            zIndex: 10,
            color: '#fff'
          }}
        >
          <Spin size="large" tip="正在加载萤石云视频..." />
        </div>
      )}
      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#000'
        }}
      />
    </div>
  );
};

export default EzVideoPlayer;
