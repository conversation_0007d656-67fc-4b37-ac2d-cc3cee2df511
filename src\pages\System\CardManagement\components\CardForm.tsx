import React from 'react';
import { ProForm, ProFormText, ProFormSelect, ProFormTextArea, ProFormDigit } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { CardFormData, CardStatusEnum } from '../types';

interface CardFormProps {
  initialValues?: CardFormData;
  onFinish: (values: CardFormData) => Promise<void>;
  loading?: boolean;
}

const CardForm: React.FC<CardFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleFinish = async (values: CardFormData) => {
    try {
      await onFinish(values);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 卡片编号验证规则
  const validateCardNumber = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入卡片编号'));
    }
    
    if (value.length < 4) {
      return Promise.reject(new Error('卡片编号至少4位'));
    }
    
    if (value.length > 50) {
      return Promise.reject(new Error('卡片编号最多50位'));
    }
    
    // 卡片编号只能包含字母、数字、下划线和横线
    const cardNumberRegex = /^[a-zA-Z0-9_-]+$/;
    if (!cardNumberRegex.test(value)) {
      return Promise.reject(new Error('卡片编号只能包含字母、数字、下划线和横线'));
    }
    
    return Promise.resolve();
  };

  return (
    <ProForm
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleFinish}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '重置',
        },
        submitButtonProps: {
          loading,
        },
      }}
    >
      <ProFormText
        name="cardNumber"
        label="卡片编号"
        placeholder="请输入卡片编号"
        rules={[
          { validator: validateCardNumber },
        ]}
        fieldProps={{
          maxLength: 50,
        }}
        extra="卡片编号只能包含字母、数字、下划线和横线，长度4-50位"
      />

      <ProFormSelect
        name="cardStatus"
        label="卡片状态"
        placeholder="请选择卡片状态"
        options={[
          { label: '禁用', value: CardStatusEnum.DISABLED },
          { label: '启用', value: CardStatusEnum.ENABLED },
          { label: '丢失', value: CardStatusEnum.LOST },
          { label: '损坏', value: CardStatusEnum.DAMAGED },
        ]}
        initialValue={CardStatusEnum.ENABLED}
        rules={[
          { required: true, message: '请选择卡片状态' },
        ]}
      />

      <ProFormDigit
        name="employeeId"
        label="绑定员工ID"
        placeholder="请输入员工ID（可选）"
        fieldProps={{
          precision: 0,
          min: 1,
        }}
        extra="如需绑定员工，请输入员工ID"
      />

      <ProFormText
        name="employeeName"
        label="绑定员工姓名"
        placeholder="请输入员工姓名（可选）"
        fieldProps={{
          maxLength: 100,
        }}
        extra="如需绑定员工，请输入员工姓名"
      />

      <ProFormText
        name="employeeAccount"
        label="绑定员工账号"
        placeholder="请输入员工账号（可选）"
        fieldProps={{
          maxLength: 50,
        }}
        extra="如需绑定员工，请输入员工账号"
      />

      <ProFormTextArea
        name="remark"
        label="备注信息"
        placeholder="请输入备注信息（可选）"
        fieldProps={{
          maxLength: 500,
          rows: 4,
          showCount: true,
        }}
      />
    </ProForm>
  );
};

export default CardForm;
