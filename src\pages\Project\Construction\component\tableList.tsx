import { ProTable } from '@ant-design/pro-components';
import type { ActionType} from '@ant-design/pro-components';
import {message, Flex, Select} from 'antd';
import { useState, useRef,useEffect } from 'react';
import { postRequest } from '@/services/api/api';

interface IoTPointProps {
  number: {
    drillNumber: string;
    id: string;
  };
  dataSource: string[];
  value: string[];
  options: any[];
}

type TableListItem = {
  id: number;
  title: string;
  var: string;
  type: number;
  createdAt: string;
  updatedAt: string;
};

export default ({ dataSource, number, value, options }: IoTPointProps) => {
  const [tableColumns, setTableColumns] = useState<any[]>([]);
  const ref = useRef<ActionType>();

  // 监听value变化，自动刷新表格数据
  useEffect(() => {
    if (value && value.length > 0 && ref.current) {
      console.log('TableList: value变化，刷新表格数据', value);
      ref.current.reload();
    }
  }, [value]);

  // 优化：在组件挂载和value/options变化时计算列，而不是每次渲染时
  useEffect(() => {
    // 基础列配置
    const baseColumns = [
      {
        title: '时间',
        dataIndex: 'time',
        width: 150,
        ellipsis: true,
        fixed: 'left',
      },
      {
        title: '孔深',
        dataIndex: 'workDepth',
        width: 150,
        ellipsis: true,
      }
    ];
    
    // 仅当有选择的字段时添加动态列
    if (value && value.length > 0 && options) {
      // 创建映射以提高查找效率
      const optionsMap = new Map(options.map((opt: any) => [opt.var, opt.title]));
      
      // 为每个选中的变量添加列
      const dynamicColumns = value.map((fieldName: string, index: number) => ({
        title: optionsMap.get(fieldName) || fieldName,
        dataIndex: fieldName,
        width: 150,
        ellipsis: true,
        fixed: index < 2 ? 'left' : undefined,
      }));
      
      setTableColumns([...baseColumns, ...dynamicColumns]);
    } else {
      setTableColumns(baseColumns);
    }
  }, [value, options]);

  return(
    <ProTable<TableListItem>
      actionRef={ref}
      columns={tableColumns}
      options={false}
      request={async (params, sorter, filter) => {
        const { current, pageSize, keyWord, ...rest } = params;
        const postData = {
          page: current,
          perPage: pageSize,
          number: number.drillNumber,
          parentId: number.id,  // 新增parentId参数
          keyWord: value.join(',')
        };

        console.log('TableList: 调用 device_data/get_record_all 接口', postData);

        try {
          const result = await postRequest('device_data/get_record_all', postData);
          const { data, status, msg } = result;
          
          if (status === 0) {
            // 处理返回的数据
            const processedData = data.items.map((item: any) => {
              // 创建基础记录对象
              const record: any = {
                time: item.time,
                workDepth: item.workDepth,
              };
              
              // 直接将值添加到记录中，无需中间fieldValues数组
              value.forEach((fieldName: string) => {
                record[fieldName] = item.hasOwnProperty(fieldName) ? item[fieldName] : null;
              });
              
              return record;
            });
            
            return {
              data: processedData,
              total: data.total,
              success: true,
            };
          } else {
            message.error(msg || '获取数据失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        } catch (error) {
          console.error('请求出错:', error);
          message.error('获取数据失败');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }
      }}
      rowKey="id"
      search={false}
    />
  );
}; 