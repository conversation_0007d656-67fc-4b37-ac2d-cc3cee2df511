import { request } from '@umijs/max';
import Hls from 'hls.js';
import React, { useEffect, useRef, useState } from 'react';
const Monitor: React.FC = (props) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [hlsUrl, setHlsUrl] = useState<string>('');
  const { data } = props;
  const cameraData = JSON.parse(data.cameraUrl);
  const deviceCode = data.deviceCode;
  console.log(cameraData, 'cameraData');

  useEffect(() => {
    const fetchCameraData = async () => {
      try {
        // const token = sessionStorage.getItem('token');
        // const result = await request(`${VITE_APP_VIDEO_URL}api/camera`, {
        const result = await request('http://117.131.137.61:18080/api/play/start/34020000001320001234/34020000001320000001', {
          method: 'GET',
        });
        // const { data, status, msg } = result as any;
        console.log(result.data, 'result.data111111');
        setHlsUrl(result.data.ws_fmp4);
        // if (result.status === 0 && result.data) {
          // 确保视频元素存在
          if (videoRef.current) {
            const video = videoRef.current;

            // 如果URL是m3u8格式
            if (result.data.includes('.m3u8')) {
              if (Hls.isSupported()) {
                if (hlsRef.current) {
                  hlsRef.current.destroy();
                }
                hlsRef.current = new Hls({
                  enableWorker: true,
                  lowLatencyMode: true,
                  backBufferLength: 90,
                  maxBufferLength: 30,
                  maxMaxBufferLength: 600,
                  maxBufferSize: 60 * 1000 * 1000,
                  maxBufferHole: 0.5,
                  manifestLoadPolicy: {
                    default: {
                      maxTimeToFirstByteMs: 10000,
                      maxLoadTimeMs: 20000,
                      timeoutRetry: {
                        maxNumRetry: 6,
                        retryDelayMs: 1000,
                        maxRetryDelayMs: 8000,
                      },
                      errorRetry: {
                        maxNumRetry: 6,
                        retryDelayMs: 1000,
                        maxRetryDelayMs: 8000,
                      },
                    },
                  },
                });

                hlsRef.current.attachMedia(video);
                hlsRef.current.loadSource(result.data.hls);

                hlsRef.current.on(Hls.Events.ERROR, (event, data) => {
                  if (data.fatal) {
                    switch (data.type) {
                      case Hls.ErrorTypes.NETWORK_ERROR:
                        console.error('Network error, trying to recover...');
                        hlsRef.current?.startLoad();
                        break;
                      case Hls.ErrorTypes.MEDIA_ERROR:
                        console.error('Media error, trying to recover...');
                        hlsRef.current?.recoverMediaError();
                        break;
                      case Hls.ErrorTypes.KEY_SYSTEM_ERROR:
                        console.error('Key system error, destroying HLS instance');
                        hlsRef.current?.destroy();
                        break;
                      case Hls.ErrorTypes.MUX_ERROR:
                        console.error('Mux error, destroying HLS instance');
                        hlsRef.current?.destroy();
                        break;
                      default:
                        console.error('Fatal error, destroying HLS instance');
                        hlsRef.current?.destroy();
                        break;
                    }
                  }
                });

                hlsRef.current.on(Hls.Events.MANIFEST_LOADED, () => {
                  console.log('Manifest loaded successfully');
                });

                hlsRef.current.on(Hls.Events.MEDIA_ATTACHED, () => {
                  console.log('Media attached successfully');
                });
              } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = result.data.hls;
              }
            } else {
              // 如果是普通视频格式
              video.src = result.data.hls;
            }

            // 设置视频属性
            video.autoplay = true;
            video.controls = true;
            video.muted = true;
            video.playsInline = true;

            // 添加视频加载事件监听
            video.addEventListener('loadeddata', () => {
              console.log('Video data loaded successfully');
            });

            video.addEventListener('error', (e) => {
              console.error('Video error:', e);
            });
          }
        // }
      } catch (error) {
        console.error('Failed to fetch camera data:', error);
      }
    };
    fetchCameraData();

    // 优化清理函数
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
      if (videoRef.current) {
        const video = videoRef.current;
        video.pause();
        video.src = '';
        video.load(); // 确保资源被释放
      }
    };
  }, []);

  return (
    <div style={{ width: '100%', height: '100%', padding: '20px' }}>
      {/* <h2>监控内容:</h2> */}
      <div style={{ width: '100%', margin: '0 auto' }}>
        <video
          ref={videoRef}
          style={{
            width: '100%',
            height: '580px',
            backgroundColor: '#000',
          }}
          src={hlsUrl}
          controls
        />
      </div>
    </div>
  );
};

export default Monitor;
