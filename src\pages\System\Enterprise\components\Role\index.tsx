import React, { useState, useRef, useEffect } from 'react';
import { Layout, Flex, Button, Tree, message, Drawer, Form, TreeSelect, Modal, Result, Popconfirm } from 'antd';
import type { DataNode } from 'antd/es/tree';
import type { GetProp, TreeSelectProps } from 'antd';
import { ApartmentOutlined, SlidersOutlined, PlusOutlined, EllipsisOutlined } from '@ant-design/icons';
import { ProTable, ProForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest, getRequest } from '@/services/api/api';
import type { Key } from 'rc-tree/lib/interface';

const { Sider, Content } = Layout;

interface ApiResponse<T = any> {
    status: number;
    msg?: string;
    data: T;
}

interface RoleItem {
    id: string;
    name: string;
    description?: string;
    createdAt?: string;
    updatedAt?: string;
}

interface DepartmentItem {
    id: string;
    name: string;
    parentId: string;
    hasChildren: boolean;
    children?: DepartmentItem[];
}

export type TableListItem = {
    id: number;
    account: string;
    name: string;
    position: string;
    bindIdcard: string;
    bind: '0' | '1';
    departmentName: string;
    departmentId: string;
    createdAt: string;
    updatedAt: string;
};

interface MemberApiResponse {
    data: {
        items: TableListItem[];
        total: number;
    };
    status: number;
    msg: string;
}

interface AddEmployeeResponse {
    status: number;
    msg: string;
    data: any;
}

type DefaultOptionType = GetProp<TreeSelectProps, 'treeData'>[number];

interface Props {
    onTabChange?: (tab: string) => void;
}

const Member: React.FC<Props> = ({ onTabChange }) => {
    const [treeData, setTreeData] = useState<DataNode[]>([]);
    const [selectedDept, setSelectedDept] = useState<string>();
    const [selectedDeptName, setSelectedDeptName] = useState<string>('');
    const [loading, setLoading] = useState(true);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [addDeptDrawerVisible, setAddDeptDrawerVisible] = useState(false);
    const [editingEmployee, setEditingEmployee] = useState<TableListItem>();
    const [editDrawerVisible, setEditDrawerVisible] = useState(false);
    const actionRef = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm();
    const [editForm] = Form.useForm();
    const [deptForm] = Form.useForm();
    const [deptTreeData, setDeptTreeData] = useState<DefaultOptionType[]>([]);
    const [isFirstLevel, setIsFirstLevel] = useState(false);
    const [addRoleDrawerVisible, setAddRoleDrawerVisible] = useState(false);
    const [roleForm] = Form.useForm();
    const [showTree, setShowTree] = useState<boolean>(true);
    const [rightClickNodeTreeItem, setRightClickNodeTreeItem] = useState<{
        pageX: number;
        pageY: number;
        nodeData: DataNode;
    } | null>(null);
    const [editingRole, setEditingRole] = useState<{
        id: string;
        name: string;
        parentId?: string;
        isLeaf?: boolean;
        roleId: string;
        state:string;
    } | null>(null);

    // 处理删除
    const handleDelete = async (id: number) => {
        try {
            const result = await getRequest('employee/post_del', { id }) as AddEmployeeResponse;
            if (result.status === 0) {
                messageApi.success('删除成功');
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '删除失败');
            }
        } catch (error) {
            messageApi.error('删除失败');
        }
    };

    // 处理编辑
    const handleEdit = async (values: any) => {
        try {
            const result = await postRequest('employee/post_modify', {
                id: editingEmployee?.id,
                account: values.account,
                name: values.name,
                position: values.position,
                bindIdcard: values.bindIdcard || '',
                departmentId: values.departmentId,
                bind: values.bindIdcard ? '1' : '0'
            }) as AddEmployeeResponse;

            if (result.status === 0) {
                messageApi.success('编辑成功');
                setEditDrawerVisible(false);
                editForm.resetFields();
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '编辑失败');
            }
        } catch (error) {
            messageApi.error('编辑失败');
        }
    };

    // 将部门数据转换为树形结构
    const transformToTreeData = (items: DepartmentItem[], isRoot: number): DataNode[] => {
        return items.map((item: DepartmentItem) => ({
            title: `${item.name}`,
            key: item.id,
            isLeaf: isRoot === 1, // isRoot为1时表示是角色,为叶子节点
            parentId: item.parentId,
            roleId: item.roleId,
            state:item.state  // 添加 roleId 字段
        }));
    };

    // 处理树节点选择
    const onSelect = (selectedKeys: React.Key[], info: { node: DataNode & { parentId?: string; isLeaf?: boolean } }) => {
        const roleId = selectedKeys[0]?.toString();
        setSelectedDept(roleId);
        setSelectedDeptName(info.node.title?.toString() || '');

        // 判断是否是角色组(非叶子节点)
        const node = info.node;
        setIsFirstLevel(!node.isLeaf);

        // 刷新列表
        actionRef.current?.reload();
    };

    // 修改初始化加载部门列表函数
    const initDepartmentList = async () => {
        setLoading(true);
        try {
            const result = await postRequest('role/get_ls', { parentId: 0 }) as ApiResponse<DepartmentItem[]>;
            if (result.status === 0) {
                const treeData = transformToTreeData(result.data, 0);
                setTreeData(treeData);
                // 重置所有状态
                setSelectedDept(undefined);
                setSelectedDeptName('');
                setIsFirstLevel(true);
                // 清空表格数据
                actionRef.current?.reset?.();
            } else {
                messageApi.error(result.msg || '获取角色列表失败');
            }
        } catch (error) {
            messageApi.error('获取角色列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 修改点击加载部门列表函数
    const clickDepartmentList = async (id: string) => {
        try {
            const result = await postRequest('role/get_ls', { parentId: id }) as ApiResponse<DepartmentItem[]>;
            if (result.status === 0) {
                const treeData = transformToTreeData(result.data, 1);
                // 移除这里的状态重置逻辑,因为这会影响树的展开/收起操作
                return treeData;
            } else {
                messageApi.error(result.msg || '获取角色列表失败');
                return [];
            }
        } catch (error) {
            messageApi.error('获取角色列表失败');
            return [];
        }
    };

    // 更新树节点数据
    const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] => {
        return list.map((node: DataNode) => {
            if (node.key === key) {
                return {
                    ...node,
                    children,
                };
            }
            if (node.children) {
                return {
                    ...node,
                    children: updateTreeData(node.children, key, children),
                };
            }
            return node;
        });
    };

    const onLoadData = ({ key }: DataNode) =>
        new Promise<void>((resolve) => {
            clickDepartmentList(key.toString()).then(childrenData => {
                setTreeData(origin => updateTreeData(origin, key, childrenData));
                resolve();
            });
        });

    useEffect(() => {
        initDepartmentList();
    }, []);

    // 生成部门树节点
    const transformToDeptNode = (item: DepartmentItem): DefaultOptionType => {
        return {
            id: item.id,
            pId: item.parentId,
            value: item.id,
            title: item.name,
            state:item.state,
            isLeaf: !item.hasChildren,
        };
    };

    // 获取当前部门的父级部门链
    const getDepartmentChain = async (departmentId: string) => {
        const loadParentDepartment = async (id: string) => {
            try {
                const result = await postRequest('department/get_ls', { parentId: id }) as ApiResponse;
                if (result.status === 0 && result.data) {
                    const nodes = result.data.map(item => transformToDeptNode(item));
                    setDeptTreeData(origin => {
                        const newData = [...origin];
                        nodes.forEach(node => {
                            if (!newData.find(item => item.id === node.id)) {
                                newData.push(node);
                            }
                        });
                        return newData;
                    });

                    // 如果有父级部门，继续递归加载
                    const currentDept = result.data.find(item => item.id === departmentId);
                    if (currentDept && currentDept.parentId && currentDept.parentId !== '0') {
                        await loadParentDepartment(currentDept.parentId);
                    }
                }
            } catch (error) {
                messageApi.error('获取部门数据失败');
            }
        };

        // 从当前部门开始向上加载
        await loadParentDepartment('0');
    };

    // 初始化部门树数据
    const initDeptTreeData = async () => {
        interface TreeNode {
            id: string;
            pId: string;
            value: string;
            title: string;
            state: string;
            isLeaf: boolean;
            children?: TreeNode[];
        }

        const processTreeData = (data: DataNode[]): TreeNode[] => {
            return data.map(item => ({
                id: item.key as string,
                pId: (item as any).parentId,
                value: item.key as string,
                title: item.title as string,
                isLeaf: item.isLeaf as boolean,
                children: item.children ? processTreeData(item.children) : undefined
            }));
        };

        const processedTreeData = processTreeData(treeData);
        setDeptTreeData(processedTreeData);
    };

    // 加载子部门数据
    const onLoadDeptData: TreeSelectProps['loadData'] = ({ id }) =>
        new Promise((resolve) => {
            postRequest('role/get_ls', { parentId: id })
                .then((response: any) => {
                    const result = response as ApiResponse;
                    if (result.status === 0) {
                        const newNodes = result.data.map(item => transformToDeptNode(item));
                        console.log('测试2222222222222222222222222222', newNodes);

                        setDeptTreeData(origin => [...origin, ...newNodes]);
                    }
                    resolve(undefined);
                })
                .catch(() => {
                    messageApi.error('加载子部门失败');
                    resolve(undefined);
                });
        });

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '账号',
            dataIndex: 'account',
            key: 'account',
            width: 150,
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 150,
        },
        {
            title: '所属部门',
            dataIndex: 'departmentName',
            key: 'departmentName',
            width: 200,
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (_, record) => (
                <Button
                    type="link"
                    onClick={() => {
                        Modal.confirm({
                            title: '确认移除',
                            content: `确定要移除 ${record.name} 吗？`,
                            onOk: () => handleRemoveUser(record.id),
                        });
                    }}
                >
                    移除
                </Button>
            ),
        },
    ];

    // 处理添加人员
    const handleAdd = async (values: any) => {
        try {
            const result = await postRequest('role/add_user', {
                id: selectedDept,  // 当前选中的角色id
                employeeId: values.employeeId,  // 选择的员工id
            }) as ApiResponse;

            if (result.status === 0) {
                messageApi.success('添加成功');
                setDrawerVisible(false);
                form.resetFields();
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '添加失败');
            }
        } catch (error) {
            messageApi.error('添加失败');
        }
    };

    // 处理右键菜单点击修改
    const handleRightClickEdit = () => {
        const node = rightClickNodeTreeItem?.nodeData;
        if (!node) return;

        console.log('node data:', node);

        setEditingRole({
            id: node.key as string,
            name: node.title as string,
            parentId: (node as any).parentId,
            isLeaf: node.isLeaf,
            roleId: (node as any).roleId || (node as any).parentId  // 优先使用 roleId，如果没有则使用 parentId
        });

        if (node.isLeaf) {
            // 如果是角色(叶子节点)
            roleForm.setFieldsValue({
                name: node.title,
                parentId: (node as any).parentId
            });
            setAddRoleDrawerVisible(true);
        } else {
            // 如果是角色组
            deptForm.setFieldsValue({
                name: node.title
            });
            setAddDeptDrawerVisible(true);
        }
    };

    // 修改角色处理函数
    const handleRole = async (values: any) => {
        if (editingRole) {
            // 修改操作
            try {
                const result = await postRequest('role/post_modify', {
                    id: editingRole.id,
                    name: values.name,
                    roleId: editingRole.roleId, // 使用保存的 roleId
                }) as ApiResponse;

                if (result.status === 0) {
                    messageApi.success('修改成功');
                    setAddRoleDrawerVisible(false);
                    roleForm.resetFields();
                    setRightClickNodeTreeItem(null);
                    setEditingRole(null);

                    // 重新加载数据
                    setShowTree(false);
                    await initDepartmentList();
                    setTimeout(() => {
                        setShowTree(true);
                    }, 100);
                } else {
                    messageApi.error(result.msg || '修改失败');
                }
            } catch (error) {
                messageApi.error('修改失败');
            }
        } else {
            // 新增操作
            try {
                const result = await postRequest('role/post_add', {
                    name: values.name,
                    roleId: values.parentId,
                }) as ApiResponse;

                if (result.status === 0) {
                    messageApi.success('添加成功');
                    setAddRoleDrawerVisible(false);
                    roleForm.resetFields();

                    // 重置状态
                    setSelectedDept(undefined);
                    setSelectedDeptName('');
                    setIsFirstLevel(true);
                    actionRef.current?.reset?.();

                    // 触发树重新加载
                    setShowTree(false);
                    if (values.parentId) {
                        const parentData = await clickDepartmentList(values.parentId);
                        setTreeData(origin => updateTreeData(origin, values.parentId, parentData));
                    }
                    setTimeout(() => {
                        setShowTree(true);
                    }, 100);
                } else {
                    messageApi.error(result.msg || '添加失败');
                }
            } catch (error) {
                messageApi.error('添加失败');
            }
        }
    };

    // 修改角色组处理函数
    const handleDept = async (values: any) => {
        if (editingRole) {
            // 修改操作
            try {
                const result = await postRequest('role/post_modify', {
                    id: editingRole.id,
                    name: values.name,
                }) as ApiResponse;

                if (result.status === 0) {
                    messageApi.success('修改成功');
                    setAddDeptDrawerVisible(false);
                    deptForm.resetFields();
                    setRightClickNodeTreeItem(null);
                    setEditingRole(null);

                    // 重新加载数据
                    setShowTree(false);
                    await initDepartmentList();
                    setTimeout(() => {
                        setShowTree(true);
                    }, 100);
                } else {
                    messageApi.error(result.msg || '修改失败');
                }
            } catch (error) {
                messageApi.error('修改失败');
            }
        } else {
            // 新增操作
            try {
                const result = await postRequest('role/post_add', {
                    name: values.name,
                }) as ApiResponse;

                if (result.status === 0) {
                    messageApi.success('添加成功');
                    setAddDeptDrawerVisible(false);
                    deptForm.resetFields();

                    // 重置状态
                    setSelectedDept(undefined);
                    setSelectedDeptName('');
                    setIsFirstLevel(true);
                    actionRef.current?.reset?.();

                    // 触发树重新加载
                    setShowTree(false);
                    await initDepartmentList();
                    setTimeout(() => {
                        setShowTree(true);
                    }, 100);
                } else {
                    messageApi.error(result.msg || '添加失败');
                }
            } catch (error) {
                messageApi.error('添加失败');
            }
        }
    };

    // 处理删除角色/角色组
    const handleDeleteRole = async (nodeData: DataNode) => {
        try {
            const result = await getRequest('role/post_del', { id: nodeData.key }) as ApiResponse;
            if (result.status === 0) {
                messageApi.success('删除成功');
                // 重新加载数据
                setShowTree(false);
                await initDepartmentList();
                setTimeout(() => {
                    setShowTree(true);
                }, 100);
            } else {
                messageApi.error(result.msg || '删除失败');
            }
        } catch (error) {
            messageApi.error('删除失败');
        }
    };

    // 处理移除人员
    const handleRemoveUser = async (employeeId: number) => {
        try {
            const result = await postRequest('role/del_user', {
                id: selectedDept,  // 当前选中的角色id
                employeeId: employeeId,  // 要移除的员工id
            }) as ApiResponse;

            if (result.status === 0) {
                messageApi.success('移除成功');
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '移除失败');
            }
        } catch (error) {
            messageApi.error('移除失败');
        }
    };

    return (
        <Layout style={{ height: '100%' }}>
            {contextHolder}
            <Sider width={280} style={{ background: 'transparent', borderRight: '1px solid #313131' }}>
                <div style={{ padding: '16px' }}>
                    <Flex gap="small" style={{ marginBottom: '16px' }}>
                        <Button
                            icon={<ApartmentOutlined />}
                            onClick={async () => {
                                initDeptTreeData()
                                deptForm.resetFields();
                                deptForm.setFieldsValue({
                                    parentId: selectedDept || '0'
                                });
                                setAddDeptDrawerVisible(true);
                            }}
                        >
                            新增角色组
                        </Button>
                        <Button
                            icon={<SlidersOutlined />}
                            onClick={() => {
                                roleForm.resetFields();
                                setAddRoleDrawerVisible(true);
                            }}
                        >
                            新增角色
                        </Button>
                    </Flex>
                    {showTree && (
                        <>
                            <Tree<DataNode>
                                loadData={onLoadData}
                                treeData={treeData}
                                onSelect={onSelect}
                                selectedKeys={selectedDept ? [selectedDept] : []}
                                style={{
                                    background: 'transparent',
                                    fontSize: '14px'
                                }}
                                titleRender={(nodeData: any) => (                                    
                                    <div 
                                        style={{
                                            position: 'relative',
                                            width: '100%',
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center'
                                        }}
                                        onClick={(e) => {
                                            // 允许点击事件继续传播到树节点
                                            e.stopPropagation();
                                            const keys = [nodeData.key];
                                            console.log(nodeData,'nodeDatanodeData');
                                            
                                            onSelect(keys, { node: nodeData, selectedNodes: [nodeData] });
                                        }}
                                    >
                                        <span style={{ width: '180px' }}>{nodeData.title}</span>
                                        {nodeData.state !== 1 && (
                                        <Popconfirm
                                            onConfirm={async (e) => {
                                                e?.stopPropagation();
                                                const result = await getRequest('role/post_del', { id: nodeData.key }) as ApiResponse;
                                                if (result.status === 0) {
                                                    messageApi.success('删除成功');
                                                    setShowTree(false);
                                                    await initDepartmentList();
                                                    setTimeout(() => {
                                                        setShowTree(true);
                                                    }, 100);
                                                } else {
                                                    messageApi.error(result.msg || '删除失败');
                                                }
                                            }}
                                            onCancel={(e) => {
                                                e?.stopPropagation();
                                            }}
                                            description="是否确认删除角色组"
                                            okText="是的"
                                            cancelText="取消"
                                        >
                                            <EllipsisOutlined
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                }}
                                                style={{
                                                    fontSize: '16px',
                                                    float: 'right',
                                                    // marginLeft: '8px'
                                                }}
                                            />
                                        </Popconfirm>
                                        )}
                                    </div>
                                )}
                                onRightClick={({ event, node }) => {
                                    console.log('node:', node);

                                    event.preventDefault();
                                    const rect = (event.target as HTMLElement).getBoundingClientRect();
                                    setRightClickNodeTreeItem({
                                        pageX: rect.x,
                                        pageY: rect.y + rect.height,
                                        nodeData: node,
                                    });
                                }}
                            />
                            {rightClickNodeTreeItem && (
                                <div
                                    style={{
                                        position: 'fixed',
                                        left: rightClickNodeTreeItem.pageX,
                                        top: rightClickNodeTreeItem.pageY,
                                        backgroundColor: '#1f1f1f',
                                        padding: '4px 0',
                                        borderRadius: '2px',
                                        boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                                        zIndex: 100,
                                        minWidth: '120px',
                                        border: '1px solid #303030'
                                    }}
                                    onMouseLeave={() => setRightClickNodeTreeItem(null)}
                                >
                                    <Button
                                        type="text"
                                        style={{
                                            display: 'block',
                                            width: '100%',
                                            textAlign: 'left',
                                            padding: '4px 12px',
                                            height: '32px',
                                            lineHeight: '24px',
                                            color: '#fff',
                                        }}
                                        onClick={handleRightClickEdit}
                                    >
                                        修改
                                    </Button>
                                    <Button
                                        type="text"
                                        danger
                                        style={{
                                            display: 'block',
                                            width: '100%',
                                            textAlign: 'left',
                                            padding: '4px 12px',
                                            height: '32px',
                                            lineHeight: '24px',
                                            color: '#ff4d4f',
                                        }}
                                        onClick={() => {
                                            Modal.confirm({
                                                title: '确认删除',
                                                content: `确定要删除${rightClickNodeTreeItem.nodeData.title}吗？`,
                                                onOk: () => handleDeleteRole(rightClickNodeTreeItem.nodeData),
                                                onCancel: () => setRightClickNodeTreeItem(null),
                                            });
                                        }}
                                    >
                                        删除
                                    </Button>
                                </div>
                            )}
                        </>
                    )}
                </div>
            </Sider>
            <Content style={{ padding: '16px' }}>
                {!loading && (
                    !selectedDept || isFirstLevel ? (
                        <Result
                            status="403"
                            title="角色管理"
                            subTitle="请选择具体角色查看人员列表"
                        />
                    ) : (
                        <ProTable<TableListItem>
                            columns={columns}
                            actionRef={actionRef}
                            request={async (params) => {
                                try {
                                    const result = await postRequest('employee/get_ls', {
                                        ...params,
                                        roleId: selectedDept
                                    }) as MemberApiResponse;
                                    return {
                                        data: result.data.items || [],
                                        success: result.status === 0,
                                        total: result.data.total || 0,
                                    };
                                } catch (error) {
                                    return {
                                        data: [],
                                        success: false,
                                        total: 0,
                                    };
                                }
                            }}
                            rowKey="id"
                            search={false}
                            pagination={{
                                pageSize: 10,
                            }}
                            dateFormatter="string"
                            headerTitle={`${selectedDeptName || '请选择角色'} - 人员列表`}
                            toolBarRender={() => [
                                <Button
                                    key="add"
                                    type="primary"
                                    onClick={() => {
                                        form.resetFields();
                                        setDrawerVisible(true);
                                    }}
                                >
                                    添加人员
                                </Button>
                            ]}
                        />
                    )
                )}
                <Drawer
                    title="添加人员"
                    width={600}
                    open={drawerVisible}
                    onClose={() => {
                        setDrawerVisible(false);
                        form.resetFields();
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setDrawerVisible(false);
                            form.resetFields();
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setDrawerVisible(false);
                                form.resetFields();
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => form.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={form}
                        onFinish={handleAdd}
                        submitter={false}
                    >
                        <ProFormSelect
                            name="employeeId"
                            label="选择人员"
                            placeholder="请选择人员"
                            rules={[{ required: true, message: '请选择人员' }]}
                            request={async () => {
                                try {
                                    const result = await postRequest('employee/get_ls', {}) as MemberApiResponse;
                                    if (result.status === 0) {
                                        return result.data.items.map(item => ({
                                            label: `${item.name} (${item.account})`,
                                            value: item.id
                                        }));
                                    }
                                    return [];
                                } catch (error) {
                                    return [];
                                }
                            }}
                        />
                    </ProForm>
                </Drawer>
                <Drawer
                    title="编辑成员"
                    width={600}
                    open={editDrawerVisible}
                    onClose={() => {
                        setEditDrawerVisible(false);
                        editForm.resetFields();
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setEditDrawerVisible(false);
                            editForm.resetFields();
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setEditDrawerVisible(false);
                                editForm.resetFields();
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => editForm.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={editForm}
                        onFinish={handleEdit}
                        submitter={false}
                    >
                        <ProFormText
                            name="account"
                            label="账号"
                            placeholder="请输入账号"
                            rules={[{ required: true, message: '请输入账号' }]}
                        />
                        <ProFormText
                            name="name"
                            label="姓名"
                            placeholder="请输入姓名"
                            rules={[{ required: true, message: '请输入姓名' }]}
                        />
                        <ProFormText
                            name="position"
                            label="职位"
                            placeholder="请输入职位"
                            rules={[{ required: true, message: '请输入职位' }]}
                        />
                        <ProFormText
                            name="bindIdcard"
                            label="绑定卡号"
                            placeholder="请输入绑定卡号"
                        />
                        <Form.Item
                            name="departmentId"
                            label="所属部门"
                            rules={[{ required: true, message: '请选择所属部门' }]}
                        >
                            <TreeSelect
                                treeDataSimpleMode
                                style={{ width: '100%' }}
                                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                placeholder="请选择所属部门"
                                loadData={onLoadDeptData}
                                treeData={deptTreeData}
                                showSearch
                                treeNodeFilterProp="title"
                            />
                        </Form.Item>
                    </ProForm>
                </Drawer>
                {/* 添加角色抽屉 */}
                <Drawer
                    title={editingRole ? "修改角色" : "添加角色"}
                    width={600}
                    open={addRoleDrawerVisible}
                    onClose={() => {
                        setAddRoleDrawerVisible(false);
                        roleForm.resetFields();
                        setRightClickNodeTreeItem(null);
                        setEditingRole(null);
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setAddRoleDrawerVisible(false);
                            roleForm.resetFields();
                            setRightClickNodeTreeItem(null);
                            setEditingRole(null);
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setAddRoleDrawerVisible(false);
                                roleForm.resetFields();
                                setRightClickNodeTreeItem(null);
                                setEditingRole(null);
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => roleForm.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={roleForm}
                        onFinish={handleRole}
                        submitter={false}
                    >
                        <ProFormText
                            name="name"
                            label={editingRole ? "修改角色名称" : "角色名称"}
                            placeholder="请输入角色名称"
                            rules={[{ required: true, message: '请输入角色名称' }]}
                        />
                        {!editingRole && (
                            <ProFormSelect
                                name="parentId"
                                label="所属角色组"
                                placeholder="请选择所属角色组"
                                rules={[{ required: true, message: '请选择所属角色组' }]}
                                request={async () => {
                                    try {
                                        const result = await postRequest('role/get_ls', { parentId: 0 }) as ApiResponse<DepartmentItem[]>;
                                        if (result.status === 0) {
                                            return result.data.map(item => ({
                                                label: item.name,
                                                value: item.id
                                            }));
                                        }
                                        return [];
                                    } catch (error) {
                                        return [];
                                    }
                                }}
                            />
                        )}
                    </ProForm>
                </Drawer>
                {/* 添加角色组抽屉 */}
                <Drawer
                    title={editingRole ? "修改角色组" : "添加角色组"}
                    width={600}
                    open={addDeptDrawerVisible}
                    onClose={() => {
                        setAddDeptDrawerVisible(false);
                        deptForm.resetFields();
                        setRightClickNodeTreeItem(null);
                        setEditingRole(null);
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setAddDeptDrawerVisible(false);
                            deptForm.resetFields();
                            setRightClickNodeTreeItem(null);
                            setEditingRole(null);
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setAddDeptDrawerVisible(false);
                                deptForm.resetFields();
                                setRightClickNodeTreeItem(null);
                                setEditingRole(null);
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => deptForm.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={deptForm}
                        onFinish={handleDept}
                        submitter={false}
                        initialValues={{
                            parentId: selectedDept || '0'
                        }}
                    >
                        <ProFormText
                            name="name"
                            label={editingRole ? "修改角色组名称" : "角色组名称"}
                            placeholder="请输入角色组名称"
                            rules={[{ required: true, message: '请输入角色组名称' }]}
                        />
                    </ProForm>
                </Drawer>
            </Content>
        </Layout>
    );
};

export default Member; 