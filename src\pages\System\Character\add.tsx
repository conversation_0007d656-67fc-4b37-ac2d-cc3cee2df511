import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, message, Space, Breadcrumb, Tree, Typography, Divider, Checkbox,Select } from 'antd';
import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons';
import { history, useLocation } from 'umi';
import { postRequest } from '@/services/api/api';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { DataNode } from 'antd/es/tree';

const { Title } = Typography;

interface ActionItem {
  id: number;
  title: string;
  type: number;
  className: string;
  productId: number;
  acts: string;
  desc: string;
  lock: number;
  createdAt: string;
  updatedAt: string;
}

interface Act {
  act: string;
  title: string;
}

interface ApiResponse<T = any> {
  status: number;
  msg?: string;
  data?: T;
}

interface LocationState {
  type?: string;
  record?: any;
}

interface PostData {
  title: string;
  actions: string;
  id?: number;
}

interface TreeNodeWithOriginalData extends DataNode {
  originalData: {
    id: number;
    title: string;
    type: number;
    className: string;
    productId: number;
    parsedActs: Array<{
      act: string;
      title: string;
    }>;
  };
}

// 添加 UserItem 接口定义
interface UserItem {
  id: string;
  name: string;
  account: string;
}

// 自定义全选标题组件
const SelectAllTitle: React.FC<{
  checked: boolean;
  indeterminate: boolean;
  expanded: boolean;
  onChange: (e: CheckboxChangeEvent) => void;
  onExpandChange: () => void;
}> = ({ checked, indeterminate, expanded, onChange, onExpandChange }) => (
  <div style={{ 
    display: 'flex', 
    alignItems: 'center', 
    padding: '4px 8px'
  }}>
     <div 
      onClick={onExpandChange}
      style={{ cursor: 'pointer' }}
    >
      {expanded ? <CaretDownOutlined /> : <CaretRightOutlined />}
    </div>
    <Checkbox
      checked={checked}
      indeterminate={indeterminate}
      onChange={onChange}
      style={{ 
        
        cursor: 'pointer',
        padding: '0 8px',
        display: 'flex',
        alignItems: 'center'
      }}
    >
        
      全部权限
    </Checkbox>
    <div 
      onClick={onExpandChange}
      style={{ 
        cursor: 'pointer',
        padding: '0 8px',
        display: 'flex',
        alignItems: 'center'
      }}
    >
     
    </div>
    <div style={{ flex: 1 }} />
   
  </div>
);

// 获取所有树节点的key，包括父节点和子节点
const getAllKeys = (data: DataNode[]): string[] => {
  const keys: string[] = [];
  data.forEach((node) => {
    keys.push(node.key.toString());
    if (node.children) {
      node.children.forEach(child => {
        keys.push(child.key.toString());
      });
    }
  });
  return keys;
};

// 检查父节点状态
const checkParentStatus = (checkedKeys: string[], treeData: DataNode[]): string[] => {
  const newCheckedKeys = [...checkedKeys];
  
  treeData.forEach((node) => {
    if (!node.children) return;
    
    const parentKey = node.key.toString();
    const childKeys = node.children.map(child => child.key.toString());
    
    // 如果所有子节点都被选中，但父节点没被选中，则选中父节点
    const allChildrenChecked = childKeys.every(key => checkedKeys.includes(key));
    if (allChildrenChecked && !checkedKeys.includes(parentKey)) {
      newCheckedKeys.push(parentKey);
    } 
    // 如果不是所有子节点都被选中，但父节点被选中，则取消选中父节点
    else if (!allChildrenChecked && checkedKeys.includes(parentKey)) {
      const index = newCheckedKeys.indexOf(parentKey);
      if (index !== -1) {
        newCheckedKeys.splice(index, 1);
      }
    }
  });
  
  return newCheckedKeys;
};

// 将权限数据转换为树形结构
const convertToTreeData = (permissions: ActionItem[]): DataNode[] => {
  return permissions.map(permission => {
    // 解析 acts JSON 字符串
    let acts: Act[] = [];
    try {
      acts = typeof permission.acts === 'string' ? JSON.parse(permission.acts) : permission.acts;
    } catch (error) {
      console.error('解析权限失败:', error);
    }

    // 创建子节点（具体操作权限）
    const children = acts.map(act => ({
      title: act.title,
      key: `${permission.id}-${act.act}`,
    }));

    return {
      title: permission.title,
      key: permission.id.toString(),
      children,
      originalData: {
        ...permission,
        parsedActs: acts,
      },
    };
  });
};

const CharacterAdd: React.FC = () => {
  const location = useLocation();
  const state = location.state as LocationState;
  const isEdit = state?.type === 'edit';
  const editRecord = state?.record;

  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [expanded, setExpanded] = useState(true);
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [userList, setUserList] = useState<UserItem[]>([]);
  const [allKeys, setAllKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  // const [selectedUsers, setSelectedUsers] = useState<string[]>([]); 
  // const [value, setValue] = useState(["123456789","15137384484","19103961234"]);

  // 获取权限列表
  const getPermissionList = async () => {
    try {
      const result = await postRequest<ApiResponse<ActionItem[]>>('permission/get_all');
      if (result.status === 0 && result.data) {
        const transformedData = convertToTreeData(result.data);
        setTreeData(transformedData);
        const allExpandedKeys = transformedData.map(item => item.key as string);
        setExpandedKeys(allExpandedKeys);
        const allTreeKeys = getAllKeys(transformedData);
        setAllKeys(allTreeKeys);
        // console.log('所有权限数据:', transformedData);
        // console.log('所有权限的keys:', allTreeKeys);
        
        // 在数据加载完成后设置编辑数据
        setEditData(transformedData);
      } else {
        messageApi.error(result.msg || '获取权限列表失败');
      }
    } catch (error) {
      messageApi.error('获取权限列表失败');
    }
  };
// 查询全部用户
const getUserList = async () => {
  const result = await postRequest<ApiResponse<UserItem[]>>('personal/get_all');
  if (result.status === 0 && result.data) {
    setUserList(result.data);
    // console.log('获取用户列表:', result.data);
  } else {
    messageApi.error(result.msg || '获取用户列表失败');
  }
}
  useEffect(() => {
    getPermissionList();
    getUserList();
  }, []);
  
  // 处理全选
  const handleCheckAll = (e: CheckboxChangeEvent) => {
    const newCheckedKeys = e.target.checked ? allKeys : [];
    // console.log('全选状态:', e.target.checked, '选中的keys:', newCheckedKeys);
    setCheckedKeys(newCheckedKeys);
    form.setFieldsValue({ permissions: newCheckedKeys });
  };
// 处理选择人员信息
  // 处理展开/收起
  const handleExpand = () => {
    setExpanded(!expanded);
  };

  // 处理树节点选择
  const handleTreeCheck = (checked: any, info: any) => {
    // console.log('树节点选择:', checked, info);
    const checkedKeys = Array.isArray(checked) ? checked : checked.checked;
    
    // 找到当前选中/取消选中的key
    const currentKey = info?.node?.key;
    if (!currentKey) return;
    
    let newCheckedKeys = [...checkedKeys];
    
    // 如果是父节点
    if (!currentKey.includes('-')) {
      const parentNode = treeData.find(node => node.key === currentKey);
      if (parentNode && parentNode.children) {
        const childKeys = parentNode.children.map(child => child.key);
        if (checkedKeys.includes(currentKey)) {
          // 选中父节点时，添加所有子节点
          newCheckedKeys = [...new Set([...newCheckedKeys, ...childKeys])];
        } else {
          // 取消选中父节点时，移除所有子节点
          newCheckedKeys = newCheckedKeys.filter(key => !childKeys.includes(key));
        }
      }
    }
    
    // 检查所有父节点的状态并更新
    newCheckedKeys = checkParentStatus(newCheckedKeys, treeData);
    
    // console.log('更新后的选中keys:', newCheckedKeys);
    setCheckedKeys(newCheckedKeys);
    form.setFieldsValue({ permissions: newCheckedKeys });
  };

  // 表单提交处理
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
    //   console.log('表单提交的值:', values);
    //   console.log('当前选中的keys:', checkedKeys);
    //   console.log('所有可选的keys:', allKeys);

      // 构建提交数据
      let selectedPermissions = [];
      
      // 如果是全选状态
      if (checkedKeys.length === allKeys.length) {
        selectedPermissions = treeData.map((node: any) => {
          const originalData = node.originalData;
          return {
            id: originalData.id,
            title: originalData.title,
            type: originalData.type,
            className: originalData.className,
            productId: originalData.productId,
            acts: originalData.parsedActs
          };
        });
      } else {
        // 部分选择的处理
        selectedPermissions = treeData
          .filter((node: any) => checkedKeys.includes(node.key))
          .map((node: any) => {
            const originalData = node.originalData;
            return {
              id: originalData.id,
              title: originalData.title,
              type: originalData.type,
              className: originalData.className,
              productId: originalData.productId,
              acts: originalData.parsedActs.filter((act: Act) => {
                const childKey = `${node.key}-${act.act}`;
                return checkedKeys.includes(childKey);
              })
            };
          })
          .filter((item: any) => item.acts.length > 0);
      }

      console.log('提交的权限数据:', selectedPermissions);
      const userData = processUserData(values);
      console.log(userData,'userlstuserlst');
      
      // if()

      const postData: PostData = {
        title: values.roleName,
        actions: JSON.stringify(selectedPermissions),
        userlst: values.name.userlst || userData.userlst,
        type:1
      };
      console.log('提交的数据:', postData);  
      if (isEdit && editRecord) {
        postData.id = editRecord.id;
      }

      const url = isEdit ? 'permission/post_modify' : 'permission/post_add';
      const result = await postRequest<ApiResponse>(url, postData);
      if (result.status === 0) {
        message.success(isEdit ? '修改成功' : '添加成功');
        history.push('/system/character');
      } else {
        message.error(result.msg || (isEdit ? '修改失败' : '添加失败'));
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error(isEdit ? '修改失败' : '添加失败');
    } finally {
      setLoading(false);
    }
  };
  const processUserData = (values: any) => {
    // 如果values.name.userlst不存在，则处理用户数据
    if (!values.name.userlst) {
      const selectedUserObjects = userList
        .filter(user => values.name.includes(user.account))
        .map(user => ({ name: user.name, account: user.account }));
  
      return {
        userlst: JSON.stringify({ users: selectedUserObjects })
      };
    }else{
      return values.name;
    }
    
    // 如果userlst已存在，直接返回
    // return values.name.userlst;
  };
  // 设置编辑数据
  const setEditData = (transformedData: DataNode[]) => {
    if (isEdit && editRecord) {
      try {
        // console.log('编辑数据:', editRecord);
        let actions = [];
        try {
          actions = typeof editRecord.actions === 'string' ? JSON.parse(editRecord.actions) : editRecord.actions;
        } catch (e) {
          console.error('解析actions失败:', e);
          actions = editRecord.actions;
        }
        // console.log('解析后的权限:', actions);
        const selectedKeys: string[] = [];
        
        if (Array.isArray(actions)) {
          actions.forEach((action: any) => {
            // 添加父节点key
            selectedKeys.push(action.id.toString());
            // 添加子节点key
            if (Array.isArray(action.acts)) {
              action.acts.forEach((act: any) => {
                selectedKeys.push(`${action.id}-${act.act}`);
              });
            }
          });
        }
        
        const parsedData = JSON.parse(editRecord.userlst);
        const selectedValues = parsedData.users.map(user => user.account);
        console.log('选中的权限:****************', selectedValues);
        setCheckedKeys(selectedKeys);
        form.setFieldsValue({
          roleName: editRecord.title,
          permissions: selectedKeys,
          name:selectedValues,
        });
      } catch (error) {
        console.error('设置编辑数据失败:', error);
        messageApi.error('解析权限数据失败');
      }
    }
  };
  useEffect(() => {

    
  }, []);
  // 取消处理
  const handleCancel = () => {
    history.push('/system/character');
  };
  const handleChange = (value: string[]) => {
    const selectedUserObjects = userList.filter(user => 
      value.includes(user.account)
    );
    const namesArray = selectedUserObjects.map(item => ({ name: item.name, account: item.account }));
    const userlst = {
      userlst: JSON.stringify({ users: namesArray })
    };
    form.setFieldsValue({ name: userlst });
  };
  return (
    <>
      {contextHolder}
      <Breadcrumb
        items={[
          { title: '首页' },
          { title: '系统设置' },
          { title: '权限管理' },
          { title: isEdit ? '编辑权限' : '添加权限' }
        ]}
      />
      <Card bordered={false} style={{ marginTop: '24px' }}>
        <Title level={5}>{isEdit ? '编辑权限' : '添加权限'}</Title>
        <Divider />
        
        <div style={{ display: 'flex' }}>
          <div style={{ flex: 1, maxWidth: '600px' }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              requiredMark={true}
            >
              <Form.Item
                label={<><span style={{ color: 'red' }}>*</span>管理组名称</>}
                name="roleName"
                required={false}
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="请输入管理组名称" />
              </Form.Item>
              <Form.Item
                label={<><span style={{ color: 'red' }}>*</span>管理人员</>}
                name="name"
                required={false}
                rules={[{ required: true, message: '请选择管理人员' }]}
              >
                <Select
                  onChange={handleChange}
                  placeholder="请选择管理人员"
                  mode="multiple"
                  options={userList.map(user => ({
                    value: user.account,
                    label: user.name,
                  }))}
                />
              </Form.Item>

              <Form.Item
                label={<><span style={{ color: 'red' }}>*</span>权限范围</>}
                name="permissions"
                required={false}
                rules={[{ required: true, message: '请选择权限范围' }]}
              >
                <div style={{ 
                  border: '1px solid rgba(255, 255, 255, 0.2)', 
                  borderRadius: '8px',
                }}>
                  <SelectAllTitle
                    checked={checkedKeys.length === allKeys.length}
                    indeterminate={checkedKeys.length > 0 && checkedKeys.length < allKeys.length}
                    expanded={expanded}
                    onChange={handleCheckAll}
                    onExpandChange={handleExpand}
                  />
                  <div style={{ display: expanded ? 'block' : 'none' }}>
                    <div style={{ 
                      // height: '300px', 
                      overflow: 'auto',
                      padding: '0 8px',
                    }}>
                      <Tree
                        checkable
                        expandedKeys={expandedKeys}
                        onExpand={(keys) => setExpandedKeys(keys as string[])}
                        treeData={treeData}
                        checkedKeys={checkedKeys}
                        onCheck={handleTreeCheck}
                        checkStrictly={true}
                      />
                    </div>
                  </div>
                </div>
              </Form.Item>

              <Form.Item style={{ textAlign: 'right', marginTop: '40px' }}>
                <Space>
                  <Button onClick={handleCancel}>取消</Button>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    确定
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>

          <div style={{ flex: 1 }}>
            {/* 右侧内容 */}
          </div>
        </div>
      </Card>
    </>
  );
};

export default CharacterAdd;
