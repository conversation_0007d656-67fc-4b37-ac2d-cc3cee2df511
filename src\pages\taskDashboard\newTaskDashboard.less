.dashboardContainer {
  min-height: 100vh;
  padding: 16px;
  color: #fff;
}

.dashboardCard {
  height: 100%;
  color: #fff;
  border-radius: 4px;

  :global {
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #303030;
    }

    .ant-statistic-title {
      color: rgba(255, 255, 255, 0.85);
      font-size: 22px;
    }

    .ant-statistic-content {
      color: #fff;
    }
  }
}

.chartCard {
  width: 100%;
  height: 425px;
  color: #fff;
  border-radius: 4px;

  :global {
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #303030;
    }
  }
}

.tableCard {
  width: 100%;
  height: 425px;
  color: #fff;
  border-radius: 4px;

  :global {
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #303030;
    }

    .ant-table {
      color: #fff;
      background-color: transparent;
    }

    .ant-table-thead > tr > th {
      color: rgba(255, 255, 255, 0.85);
      background-color: #1f1f1f;
      border-bottom: 1px solid #303030;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #303030;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #1f1f1f;
    }
  }
}

.changeInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  color: rgba(255, 255, 255, 0.65);
  font-size: 20px;
  line-height: 28px;
}

.cardHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #1890ff;
  border-radius: 4px;
}

.detailButton {
  font-size: 16px;
  font-weight: 500;
  line-height: 28px;
  margin-top: 18px;
}
