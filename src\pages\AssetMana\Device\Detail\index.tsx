import { message, Breadcrumb, Card, Col, Row, Tag, Spin } from 'antd';
import React, { useState, useEffect } from 'react';
import { getRequest } from '@/services/api/api';
import { history } from '@umijs/max';
// import Status from './status'
import State from './state'
import Health from './health'
import HistoryRecord from './historRecord'
import Information from './component/information'
import Life from './component/life'
// import Maintain from './component/maintain'

interface DeviceInfo {
    deviceName: string;           // 设备名称
    deviceCode: string;           // 设备序列号
    deviceModel: string;          // 设备型号
    configurationState?: number;  // 配置状态：0-启用 1-禁用
    creatAt: string;             // 创建时间
    updateAt: string;            // 最后更新时间
    currentState: number;        // 当前状态：0-打开 1-关闭 2-损坏
    lastMaintenanceDate?: string; // 最后保养日期
    miningFace?: string;         // 采面
    lane?: string;               // 巷道
    drillSite?: string;          // 矿场
    companyName?: string;        // 客户名称
    director?: string;           // 设备负责人
    account?: string;            // 设备负责人账号
    label?: string;              // 设备标签
    deviceModelId: string;       // 设备型号ID
}

interface ApiResponse<T> {
    [x: string]: number;
    // status: number;
    msg?: string;
    data: T;
}

// 获取配置状态显示文本和颜色
const getConfigStateText = (state?: number) => {
    switch (state) {
        case 0:
            return { text: '启用', color: 'success' };
        case 1:
            return { text: '禁用', color: 'error' };
        default:
            return { text: '-', color: 'default' };
    }
};

// 获取当前状态显示文本和颜色
const getCurrentStateText = (state: number) => {
    switch (state) {
        case 0:
            return { text: '打开', color: 'success' };
        case 1:
            return { text: '关闭', color: 'warning' };
        case 2:
            return { text: '损坏', color: 'error' };
        default:
            return { text: '-', color: 'default' };
    }
};

export default () => {
    const { location } = history;
    const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>();
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [stateData, setStateData] = useState<string | null>(null);
    const [activeTabKey, setActiveTabKey] = useState('0');
    const [infoLoaded, setInfoLoaded] = useState(false);    
    const handleDataFromState = (data:any) => {
        console.log("从State组件收到数据:", data);
        if(data){
            setActiveTabKey('1')
            setStateData(data);
        }
        // 处理接收到的数据
    };
    // 获取设备详情
    const fetchDeviceInfo = async () => {
        const searchParams = new URLSearchParams(location.search);
        const id = searchParams.get('id');
        if (!id) {
            messageApi.error('设备ID不能为空');
            return;
        }
        // console.log(searchParams,'tab111111111111');
        // console.log(id,'id');
        setLoading(true);
        setInfoLoaded(false);
        try {
            const result = await getRequest<ApiResponse<DeviceInfo>>('device/get_info', {
                id
            });

            if (result.status === 0) {
                setDeviceInfo(result.data);
                setInfoLoaded(true);
            } else {
                messageApi.error(result.msg || '获取设备信息失败');
            }
        } catch (error) {
            messageApi.error('请求失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        console.log(location,'location');
        fetchDeviceInfo();
    }, []);

    const tabListNoTitle = [
        {
            label: '状态监控',
            key: '0',
        },
        {
            label: '历史记录',
            key: '1',
        },
        {
            label: '健康分析',
            key: '2',
        },
        // {
        //     label: '设备寿命跟踪',
        //     key: '3',
        // },
        // {
        //     label: '数据转移记录',
        //     key: '4',
        // },
    ];

    const contentListNoTitle: Record<string, React.ReactNode> = {
        '0': infoLoaded && deviceInfo ? <State number={deviceInfo.deviceCode} onDataChange={handleDataFromState} /> : <Spin />,
        '1': infoLoaded ? <HistoryRecord number={deviceInfo.deviceCode} params={stateData} /> : <Spin />,
        '2': infoLoaded ? <Health number={deviceInfo.deviceCode} /> : <Spin />,
        '3': infoLoaded ? <Life /> : <Spin />,
        '4': infoLoaded ? <Information /> : <Spin />,
    };

    const onTabChange = (key: string) => {
        setActiveTabKey(key);
    };

    const configState = getConfigStateText(deviceInfo?.configurationState);
    const currentState = getCurrentStateText(deviceInfo?.currentState || 0);

    return (
        <>
            {contextHolder}
            <Breadcrumb
                    items={[
                        { title: '首页', },
                        { title: '资产管理', },
                        { title: <a style={{background:'#000'}} onClick={() => history.replace('/assetmana/device')}>设备管理</a>, },
                        { title: '设备详情', },
                    ]}
                />
            <Card bordered={false} loading={loading} style={{ marginTop: '24px'}}>
                <div style={{ marginTop: '6px', fontWeight: 500, fontSize: '20px' }}>{deviceInfo?.deviceName || '-'}</div>
                <Row style={{ marginTop: '14px' }} gutter={[16, 8]}>
                    <Col span={6}>设备序列号：{deviceInfo?.deviceCode || '-'}</Col>
                    <Col span={6}>设备型号：{deviceInfo?.deviceModel || '-'}</Col>
                    {/* <Col span={6}>配置状态：<Tag color={configState.color}>{configState.text}</Tag></Col> */}
                    {/* <Col span={6}>当前状态：<Tag color={currentState.color}>{currentState.text}</Tag></Col> */}
                    <Col span={6}>创建时间：{deviceInfo?.creatAt || '-'}</Col>
                    <Col span={6}>最后更新时间：{deviceInfo?.updateAt || '-'}</Col>
                </Row>
            </Card>

            <Card
                bordered={false}
                tabList={tabListNoTitle}
                activeTabKey={activeTabKey}
                onTabChange={onTabChange}
                style={{ marginTop: '24px' }}
                tabProps={{
                    size: 'middle',
                }}
            >
                {contentListNoTitle[activeTabKey]}
            </Card>
        </>
    );
};