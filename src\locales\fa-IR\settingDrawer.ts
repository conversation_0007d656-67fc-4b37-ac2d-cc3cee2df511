export default {
  'app.setting.pagestyle': 'تنظیم نوع صفحه',
  'app.setting.pagestyle.dark': 'سبک تیره',
  'app.setting.pagestyle.light': 'سبک سبک',
  'app.setting.content-width': 'عرض محتوا',
  'app.setting.content-width.fixed': 'ثابت',
  'app.setting.content-width.fluid': 'شناور',
  'app.setting.themecolor': 'رنگ تم',
  'app.setting.themecolor.dust': 'گرد و غبار قرمز',
  'app.setting.themecolor.volcano': 'آتشفشان',
  'app.setting.themecolor.sunset': 'غروب نارنجی',
  'app.setting.themecolor.cyan': 'فیروزه ای',
  'app.setting.themecolor.green': 'سبز قطبی',
  'app.setting.themecolor.daybreak': 'آبی روشن(پیشفرض)',
  'app.setting.themecolor.geekblue': 'چسب گیک',
  'app.setting.themecolor.purple': 'بنفش طلایی',
  'app.setting.navigationmode': 'حالت پیمایش',
  'app.setting.sidemenu': 'طرح منوی کناری',
  'app.setting.topmenu': 'طرح منوی بالایی',
  'app.setting.fixedheader': 'سرصفحه ثابت',
  'app.setting.fixedsidebar': 'نوار کناری ثابت',
  'app.setting.fixedsidebar.hint': 'کار بر روی منوی کناری',
  'app.setting.hideheader': 'هدر پنهان هنگام پیمایش',
  'app.setting.hideheader.hint': 'وقتی Hidden Header فعال باشد کار می کند',
  'app.setting.othersettings': 'تنظیمات دیگر',
  'app.setting.weakmode': 'حالت ضعیف',
  'app.setting.copy': 'تنظیمات کپی',
  'app.setting.copyinfo':
    'موفقیت در کپی کردن ， لطفا defaultSettings را در src / models / setting.js جایگزین کنید',
  'app.setting.production.hint':
    'صفحه تنظیم فقط در محیط توسعه نمایش داده می شود ، لطفاً دستی تغییر دهید',
};
