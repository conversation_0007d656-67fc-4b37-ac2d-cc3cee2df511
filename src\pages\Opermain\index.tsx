import {
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Select, Input, message, Breadcrumb, DatePicker } from 'antd';
import React, { useRef } from 'react';
import { postRequest } from '@/services/api/api';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

export type TableListItem = {
    partNumber?: string;
    partName?: string;
    deviceModel?: string;
    location?: string;
    serviceLife?: string;
    actualServicelife?: string;
    replacementDate?: string;
    nextDate?: string;
    frequency?: string;
    failureRate?: string;
    maintainRecords?: string;
    status?: number;
    alertThreshold?: string;
    troubleshooting?: string;
    inventory?: string;
    createdAt?: string;
    updateAt?: string;
    corpId?: string;
};

interface ApiResponse {
    data: {
        items: TableListItem[];
        total: number;
    };
    status: number;
    msg: string;
}

interface RequestParams {
    page?: number;
    perPage?: number;
    keyWord?: string;
    dateStart?: string;
    dateEnd?: string;
    status?: number;
    sort?: number;
    partNumber?: string;
    partName?: string;
}

export default () => {
    const ref = useRef<any>();
    const [messageApi, contextHolder] = message.useMessage();

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '部件编号',
            dataIndex: 'partNumber',
            ellipsis: true,
            hideInTable: true,
            renderFormItem: () => {
                return (
                    <Select
                        placeholder="请选择查询方式"
                        options={[
                            { value: '0', label: '单个查询' },
                            { value: '1', label: '多个查询' },
                        ]}
                    />
                );
            },
            search: false,
        },
        {
            title: '部件名称',
            dataIndex: 'partName',
            ellipsis: true,
            hideInTable: true,
            renderFormItem: () => {
                return (
                    <Input
                        placeholder='请输入部件名称'
                    />
                );
            },
            search: false,
        },
        {
            title: '运行状态',
            dataIndex: 'status',
            hideInTable: true,
            renderFormItem: () => {
                return (
                    <Select
                        placeholder='请选择运行状态'
                        options={[
                            { value: 0, label: '禁用' },
                            { value: 1, label: '正常' },
                        ]}
                    />
                );
            },
            search: false,
        },
        {
            title: '创建时间',
            dataIndex: 'sort',
            hideInTable: true,
            renderFormItem: () => {
                return (
                    <Select
                        placeholder='创建时间'
                        options={[
                            { value: 0, label: '最新创建' },
                            { value: 1, label: '最早创建' },
                        ]}
                    />
                );
            },
            search: false,
        },
        // 表格显示列
        {
            title: '部件编号',
            dataIndex: 'partNumber',
            ellipsis: true,
            search: false,
            width: 120,
            fixed: 'left',
        },
        {
            title: '部件名称',
            dataIndex: 'partName',
            ellipsis: true,
            search: false,
            width: 120,
            fixed: 'left',
        },
        {
            title: '设备型号',
            dataIndex: 'deviceModel',
            ellipsis: true,
            search: false,
            width: 120,
        },
        {
            title: '安装位置',
            dataIndex: 'location',
            ellipsis: true,
            search: false,
            width: 120,
        },
        {
            title: '预计使用寿命',
            dataIndex: 'serviceLife',
            ellipsis: true,
            search: false,
            width: 140,
        },
        {
            title: '实际使用寿命',
            dataIndex: 'actualServicelife',
            ellipsis: true,
            search: false,
            width: 140,
        },
        {
            title: '更换日期',
            dataIndex: 'replacementDate',
            ellipsis: true,
            search: false,
            width: 140,
        },
        {
            title: '下次预计更换日期',
            dataIndex: 'nextDate',
            ellipsis: true,
            search: false,
            width: 160,
        },
        {
            title: '使用频率',
            dataIndex: 'frequency',
            ellipsis: true,
            search: false,
            width: 120,
        },
        {
            title: '故障率',
            dataIndex: 'failureRate',
            ellipsis: true,
            search: false,
            width: 120,
        },
        {
            title: '维护记录',
            dataIndex: 'maintainRecords',
            ellipsis: true,
            search: false,
            width: 140,
        },
        {
            title: '运行状态',
            dataIndex: 'status',
            valueEnum: {
                0: { text: '禁用', status: 'Error' },
                1: { text: '正常', status: 'Success' },
            },
            search: false,
            width: 100,
        },
        {
            title: '预警阈值',
            dataIndex: 'alertThreshold',
            ellipsis: true,
            search: false,
            width: 120,
        },
        {
            title: '故障诊断',
            dataIndex: 'troubleshooting',
            ellipsis: true,
            search: false,
            width: 140,
        },
        {
            title: '备件库存',
            dataIndex: 'inventory',
            ellipsis: true,
            search: false,
            width: 120,
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            ellipsis: true,
            search: false,
            width: 160,
        },
        {
            title: '修改时间',
            dataIndex: 'updateAt',
            ellipsis: true,
            search: false,
            width: 160,
            fixed: 'right',
        },
        {
            title: '',
            dataIndex: 'keyWord',
            hideInTable: true,
            renderFormItem: (schema, config, form) => {
                const label = schema.dataIndex as string;
                const status = form.getFieldValue(label);
                const onchange = (value: string) => {
                    form.setFieldsValue({ [label]: value });
                };
                return (
                    <Input
                        value={status}
                        onChange={(e) => onchange(e.target.value)}
                        placeholder='请输入关键词'
                    />
                );
            },
        },
        {
            title: '',
            dataIndex: 'dateRange',
            hideInTable: true,
            renderFormItem: (schema, config, form) => {
                const label = schema.dataIndex as string;
                const status = form.getFieldValue(label);
                const onchange = (value: any) => {
                    form.setFieldsValue({ [label]: value });
                };
                return (
                    <RangePicker
                        value={status ? [dayjs(status[0]), dayjs(status[1])] : null}
                        onChange={(dates) => {
                            if (dates) {
                                const [start, end] = dates;
                                onchange([
                                    start ? start.format('YYYY-MM-DD') : '',
                                    end ? end.format('YYYY-MM-DD') : ''
                                ]);
                            } else {
                                onchange(null);
                            }
                        }}
                        placeholder={['开始日期', '结束日期']}
                        picker="date"
                        format="YYYY-MM-DD"
                        showTime={false}
                        style={{ width: '100%' }}
                    />
                );
            },
        },  
    ];

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '运维中心', },
                ]}
            />
            <ProTable<TableListItem>
                style={{ marginTop: '24px' }}
                actionRef={ref}
                columns={columns}
                scroll={{ x: 2400 }}
                pagination={{
                    defaultPageSize: 10,
                }}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('搜索参数:', params, sorter, filter);
                    const { current, pageSize, keyWord, dateRange, status, sort, partNumber, partName } = params;
                    
                    // 构建请求参数
                    let postData: RequestParams = {
                        page: current,
                        perPage: pageSize,
                    }

                    // 处理关键词搜索
                    if (keyWord) {
                        postData.keyWord = keyWord;
                    }

                    // 处理日期范围
                    if (dateRange && Array.isArray(dateRange) && dateRange.length === 2) {
                        const [start, end] = dateRange;
                        postData.dateStart = start.split('T')[0];
                        postData.dateEnd = end.split('T')[0];
                    }

                    // 处理状态筛选
                    if (status !== undefined) {
                        postData.status = status;
                    }

                    // 处理创建时间排序
                    if (sort !== undefined) {
                        postData.sort = sort;
                    }

                    // 处理部件编号
                    if (partNumber) {
                        postData.partNumber = partNumber;
                    }

                    // 处理部件名称
                    if (partName) {
                        postData.partName = partName;
                    }

                    const result = await postRequest<ApiResponse>('componentlife/get_ls', postData);
                    const { data, status: responseStatus, msg } = result
                    let dataSource
                    let total
                    if (responseStatus === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                headerTitle="部件寿命"
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,
                    span: 6,
                    layout: 'horizontal',
                    defaultColsNumber: 6,
                }}
                form={{
                    initialValues: {
                        sort: 0
                    }
                }}
                rowKey="partNumber"
            />
        </>
    );
};