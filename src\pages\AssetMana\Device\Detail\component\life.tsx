import { EllipsisOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Breadcrumb } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';

const { RangePicker } = DatePicker;

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}


export default () => {
    const ref = useRef();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });


    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '设备寿命跟踪ID',
            dataIndex: 'name',
        },
        {
            title: '设备编号',
            dataIndex: 'containers',
        },
        {
            title: '设备名称',
            dataIndex: 'containers',
        },
        {
            title: '设备型号',
            dataIndex: 'containers',
        },
        {
            title: '跟踪部件',
            dataIndex: 'containers',
        },
        {
            title: '预计寿命',
            dataIndex: 'containers',
        },
        {
            title: '当前使用时长/次数',
            dataIndex: 'containers',
        },
        {
            title: '剩余寿命',
            dataIndex: 'containers',
        },
        {
            title: '寿命报警阈值',
            dataIndex: 'containers',
        },
        {
            title: '最近一次更换日期',
            dataIndex: 'containers',
        },
        {
            title: '下一次更换预测日期',
            dataIndex: 'containers',
        },
        {
            title: '更换周期',
            dataIndex: 'containers',
        },
        {
            title: '跟踪状态',
            dataIndex: 'containers',
        },
        {
            title: '备注',
            dataIndex: 'containers',
        },
        {
            title: '创建日期',
            dataIndex: 'containers',
        },
        {
            title: '更新日期',
            dataIndex: 'containers',
        },
        {
            title: '操作',
            key: 'option',
            width: 120,
            valueType: 'option',
            render: () => {
                return (
                    <a key="link" onClick={showModal}>详情</a>
                )
            }
        },
    ];

    const columns1 = [
        {
            title: '变更字段',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '变更前',
            dataIndex: 'age',
            key: 'age',
        },
        {
            title: '变更后',
            dataIndex: 'address',
            key: 'address',
        },
    ];

    const data1 = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];

    useEffect(() => {
    }, []);

    return (
        <>
            {contextHolder}
            <ProTable<TableListItem>
                actionRef={ref}
                columns={columns}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('6666666666666', params, sorter, filter);
                    let postData = {
                        page: params.current,
                        perPage: params.pageSize
                    }
                    const result = await getRequest('mock/get_ls', postData);
                    const { data, status, msg } = result
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.data
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                toolbar={{
                    search: <Flex>
                        <Input style={{ width: '250px' }}
                            allowClear
                            value={queryForm.keyWord}
                            onChange={(e) => {
                                console.log('e', e.target.value);
                                setQueryForm({ ...queryForm, keyWord: e.target.value })
                                // ref.current.submit();
                                ref.current.reload();
                            }}
                            placeholder="搜索" />
                    </Flex>
                }}
                rowKey="key"
                search={false}
            />
            <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal>
        </>
    );
};