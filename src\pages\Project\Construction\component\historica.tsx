/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-24 19:12:25
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-03 16:06:54
 * @FilePath: \diy_tfl_pc\src\pages\Project\Construction\component\historica.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useState } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Select, message, Button, Segmented, Form, Empty, DatePicker, Input } from 'antd';
import { postRequest } from '@/services/api/api';
import { Chart } from '@antv/g2';
import TableList from './tableList';

const { RangePicker } = DatePicker;
// 定义接口
interface HistoricalProps {
  number: {
    drillNumber: string;
    id: string;
  }
}

interface DataOption {
  var: string;
  title: string;
}

interface ApiResponse {
  data: any;
  status: number;
  msg: string;
}

const MAX_COUNT = 5;

const HistoricalChart: React.FC<HistoricalProps> = ({ number }) => {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<string[]>([]);
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [options, setOptions] = useState<DataOption[]>([]);
  const [activeTab, setActiveTab] = useState('0');
  const [segmentedValue, setSegmentedValue] = useState('看板视图');
  const [shep, setShep] = useState(false);
  const [tableKey, setTableKey] = useState(0); // 用于强制TableList重新渲染

  // 添加辅助函数：根据 var 查找对应的 title
  const getTitlesByVars = (vars: string[]): string[] => {
    return vars.map(varValue => {
      const option = options.find(opt => opt.var === varValue);
      return option ? option.title : '';
    }).filter(title => title !== '');
  };

  //修改搜索方法
  const handleSearch = async (values = selectedValues, tabValue = activeTab) => {
    console.log('handleSearch开始执行', { values, tabValue, selectedValues, activeTab });

    const formValues = form.getFieldsValue();
    console.log('表单值:', formValues);

    // 验证必要参数
    if (!formValues.layout || formValues.layout.length === 0) {
      console.warn('没有选择任何字段，跳过搜索');
      return;
    }

    if (!number.id || !number.drillNumber) {
      console.error('缺少必要的number参数:', number);
      message.error('缺少必要参数');
      return;
    }

    setSelectedValues(formValues.layout);
    const titles = getTitlesByVars(formValues.layout);
    setDataSource(titles);

    // 创建请求参数对象
    const requestParams: any = {
      parentId: number.id,
      keyWord: formValues.layout.join(','),
      action: Number(tabValue),
      number: number.drillNumber,
    };

    console.log('请求参数:', requestParams);

    // 仅当时间范围有值时，才添加时间参数
    if (formValues.timeRange && formValues.timeRange[0] && formValues.timeRange[1]) {
      requestParams.timeStart = formValues.timeRange[0].format('YYYY-MM-DD HH:mm:ss');
      requestParams.timeEnd = formValues.timeRange[1].format('YYYY-MM-DD HH:mm:ss');
    }

    try {
      const response = await postRequest('device_data/get_record', requestParams);
      
      const { data, status, msg } = response as ApiResponse;
      
      if (status === 0 && data) {
        if (data && data.length > 0) {
          // 延迟检查容器元素，确保DOM已经渲染完成
          setTimeout(() => {
            const containerElement = document.getElementById('line');
            if (!containerElement) {
              console.error('图表容器元素不存在，跳过渲染');
              setShep(true);
              return;
            }

            try {
              // 清理之前的图表内容
              containerElement.innerHTML = '';

              // 将字符串类型的数值转换为数字类型
              const formattedData = data.map((item: any) => ({
                ...item,
                value: Number(item.value)
              }));

              const chart = new Chart({
                container: 'line',
                autoFit: true,
                theme: 'dark',
                paddingBottom: 60,
              });

              chart
                .line()
                .data(formattedData)
                .encode('x', 'index')
                .encode('y', 'value')
                .encode('color', 'key')
                .scale('y', { nice: true })
                .scale('x', { type: 'band' });

              chart.render();
              setShep(false);
            } catch (chartError) {
              console.error('图表渲染出错:', chartError);
              setShep(true);
            }
          }, 100);
        } else {
          setShep(true);
        }
      } else {
        message.error(msg || '获取数据失败');
      }
    } catch (error) {
      console.error('handleSearch请求出错:', error);
      message.error('请求失败，请稍后重试');
    }
  };



  // 统一搜索处理函数
  const handleSearchClick = () => {
    if (segmentedValue === '看板视图') {
      handleSearch();
    } else {
      // 列表视图：更新搜索参数并触发TableList组件的刷新
      const formValues = form.getFieldsValue();
      console.log('列表视图搜索，表单值:', formValues);

      if (!formValues.layout || formValues.layout.length === 0) {
        message.warning('请选择要查询的字段');
        return;
      }

      // 更新状态，这会触发TableList组件的useEffect
      const newSelectedValues = formValues.layout || [];
      console.log('更新selectedValues:', newSelectedValues);
      setSelectedValues(newSelectedValues);

      const titles = getTitlesByVars(newSelectedValues);
      setDataSource(titles);

      // 强制TableList重新渲染
      setTableKey(prev => prev + 1);
    }
  };

  // Tab 切换处理
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    console.log(value, 'key2111111111');
    // 直接将点击的value值传递给handleSearch，不依赖activeTab状态
    handleSearch(selectedValues, value);
  };

  // 获取历史记录
  const fetchHistoryRecord = async () => {
    try {
      const response = await postRequest('device_data/get_all', {
        number: number.drillNumber
      });
      
      const { data, status, msg } = response as ApiResponse;
      
      if (status === 0 && data) {
        const defaultSelected = data.slice(0, 1).map((item: DataOption) => item.var);
        form.setFieldsValue({ layout: defaultSelected });
        setSelectedValues(defaultSelected);
        setOptions(data);
        // 不在这里调用handleSearch，避免重复调用
      } else {
        message.error(msg || '获取数据失败');
      }
    } catch (error) {
      console.error('fetchHistoryRecord请求出错:', error);
      message.error('请求失败，请稍后重试');
    }
  };

  // 监听钻机号变化
  useEffect(() => {
    fetchHistoryRecord();
  }, [number.drillNumber]);

  // 监听options和selectedValues变化，确保数据准备好后再调用handleSearch
  useEffect(() => {
    if (options.length > 0 && selectedValues.length > 0 && segmentedValue === '看板视图') {
      console.log('数据准备完成，调用handleSearch', { options: options.length, selectedValues });
      handleSearch();
    }
  }, [options, selectedValues]);

  // 监听视图切换
  useEffect(() => {
    if (segmentedValue === '看板视图' && selectedValues.length > 0) {
      handleSearch();
    }
  }, [segmentedValue]);

  const suffix = (
    <>
      <span>
        {selectedValues.length} / {MAX_COUNT}
      </span>
      <DownOutlined />
    </>
  );

  return (
    <div>
      <div style={{ textAlign: 'right', marginBottom: '16px' }}>
        <Segmented<string>
          options={['看板视图', '列表视图']}
          value={segmentedValue}
          onChange={(value) => setSegmentedValue(value)}
        />
      </div>

      <div>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '16px', width: "70%" }}>
          {segmentedValue === '看板视图' && (
            <Segmented
              value={activeTab}
              options={[
                { value: '0', label: '时间' },
                { value: '1', label: '孔深' }
              ]}
              onChange={handleTabChange}
            />
          )}
          <Form
            layout={'inline'}
            form={form}
            
            style={{ maxWidth: 1200 }}
            initialValues={{ layout: selectedValues }}  // 添加默认值
          >

            <Form.Item name="timeRange">
              <RangePicker 
                style={{ width: '400px' }}
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
           
            <Form.Item name="layout">
              <Select
                mode="multiple"
                maxCount={MAX_COUNT}
                value={selectedValues}
                style={{ width: '400px' }}
                suffixIcon={suffix}
                placeholder="请选择"
                options={options.map((item) => ({
                  value: item.var,
                  label: item.title
                }))}
              />
            </Form.Item>
            <Form.Item>
              {/* <Button type="primary">Submit</Button> */}
              <Button type="primary" onClick={() => handleSearchClick()}>
                搜索
              </Button>
            </Form.Item>
          </Form>

        </div>

        {segmentedValue === '看板视图' ? (
          <>
            {!shep ? (
              <div id="line" style={{ width: '100%' }} />
            ) : (
              <Empty />
            )}
          </>
        ) : (
          <div>
            <TableList
              key={tableKey}  // 使用tableKey强制重新渲染
              dataSource={dataSource}
              number={number as any}
              value={selectedValues}
              options={options}
            />
            {/* 列表视图内容 */}
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoricalChart;