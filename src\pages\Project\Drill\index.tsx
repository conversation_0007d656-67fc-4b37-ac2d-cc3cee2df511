import { postRequest } from '@/services/api/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Breadcrumb, DatePicker, Input, message,Tag } from 'antd';
import React from 'react';
import { history } from 'umi';
const { RangePicker } = DatePicker;

// 定义表格数据类型
interface TableListItem {
  id: number;
  taskNumber: string;
  taskName: string;
  description: string;
  responsibleTeam: string;
  taskDate: string;
  startTime: string;
  endTime: string;
  constructionRequirements: string;
  constructionDrawings: string;
  taskPerson: string;
  taskRecipient: string;
  receiptDate: string;
  remark: string;
  state: number;
}
interface ApiResponse<T = any> {
  status: number;
  msg?: string;
  data?: T;
  total?: number;
}

const Drill: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();

  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
    {
      title: '计划任务ID',
      dataIndex: 'parentId',
      fixed: 'left',
      ellipsis: true,
      search: false,
      width: 150,
    },
    {
      title: '计划任务名称',
      dataIndex: 'parentName',
      ellipsis: true,
      search: false,
      width: 150,
    },
    {
      title: '钻机名称',
      dataIndex: 'drillName',
      ellipsis: true,
      search: false,
    },
    {
      title: '施工状态',
      dataIndex: 'status',
      ellipsis: true,
      search: false,
      width: 200,
      render: (text1,record) => {
          let color = 'green';
          let text = '下发任务';
          if (record.status === 0) {
            color = 'orange';
            text='下发任务';
          } else if (record.status === 1 || record.status === 2 || record.status === 3 || record.status === 4) {
            color = 'orange';
            text='施工中';
          } else if (record.status === 5) {
            color = 'red';
            text='终孔';
          } else if (record.status === 6) {
            color = 'green';
            text='已完成';
          } else if (record.status === 7) {
            color = 'default';
            text='废孔';
          }
          return <Tag color={color}>{text}</Tag>;
        },
      },
    {
      title: '孔号',
      dataIndex: 'holeNumber',

      ellipsis: true,
      search: false,
    },
    {
      title: '施工位置',
      dataIndex: 'constructionLocation',
      ellipsis: true,
      width: 120,
      search: false,
    },
    {
      title: '施工地点',
      dataIndex: 'position',
      ellipsis: true,
      width: 120,
      search: false,
    },
    {
      title: '孔深（m）',
      dataIndex: 'holeDepth',

      ellipsis: true,
      search: false,
    },
    {
      title: '实际孔深（m）',
      dataIndex: 'realHoleDepth',
      ellipsis: true,
      search: false,
    },
    {
      title: '孔径（mm）',
      dataIndex: 'holeDiameter',
      ellipsis: true,
      search: false,
    },
    {
      title: '开孔角度（°）',
      dataIndex: 'holeAngle',
      ellipsis: true,
      search: false,
    },
    {
      title: '方位（°）',
      dataIndex: 'direction',
      ellipsis: true,
      search: false,
    },
    {
      title: '开孔高度（m）',
      dataIndex: 'holeHeight',
      ellipsis: true,
      search: false,
    },
    {
      title: '见煤距离（m）',
      dataIndex: 'coalDistance',
      ellipsis: true,
      search: false,
    },
    {
      title: '见岩石距离（m）',
      dataIndex: 'rockDistance',
      ellipsis: true,
      search: false,
    },
    // {
    //   title: '扩孔起始距离',
    //   dataIndex: 'reamingStartDistance',
    //   ellipsis: true,
    //   search: false,
    //   width: 120,
    // },

    // {
    //   title: '打钻起始距离',
    //   dataIndex: 'drillingStartDistance',
    //   ellipsis: true,
    //   search: false,
    //   width: 120,
    // },
    // {
    //   title: '预计出煤量',
    //   dataIndex: 'estimatedCoalOutput',
    //   ellipsis: true,
    //   search: false,
    //   width: 120,
    // },
    {
      title: '开孔角度误差（°）',
      dataIndex: 'holeAngleError',
      ellipsis: true,
      search: false,
      width: 150,
    },
    {
      title: '开孔方位误差（°）',
      dataIndex: 'holeDirectionError',
      ellipsis: true,
      search: false,
      width: 150,
    },
    {
      title: '孔深误差（m）',
      dataIndex: 'holeDepthError',
      ellipsis: true,
      search: false,
      width: 150,
    },
    // {
    //   title: '开孔高度误差',
    //   dataIndex: 'holeHeightError',
    //   ellipsis: true,
    //   search: false,
    //   width: 120,
    // },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            history.push(`/project/drill/detail?id=${record.id}`);
          }}
        >
          详情
        </a>,
      ],
    },
    {
      title: '',
      dataIndex: 'keyWord',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        const label = item.dataIndex;
        const status = form.getFieldValue(label);
        const onchange = (value) => {
          form.setFieldsValue({ [label]: value });
        };
        return (
          <>
            <Input
              value={status}
              onChange={(e) => onchange(e.target.value)}
              placeholder="请输入钻机名称"
            />
          </>
        );
      },
    },

    {
      title: '',
      dataIndex: 'dateRange',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        const label = item.dataIndex;
        const status = form.getFieldValue(label);
        const onchange = (value) => {
          form.setFieldsValue({ [label]: value });
        };
        return (
          <>
            <RangePicker
              value={status}
              style={{ width: '300px' }}
              onChange={(value) => onchange(value)}
              picker="date"
              format="YYYY-MM-DD"
            />
          </>
        );
      },
    },
  ];

  return (
    <>
    <Breadcrumb
        items={[
          { title: '首页' },
          { title: '施工管理', },
          { title: '打孔管理' },
        ]}
      />
      {contextHolder}

            <div style={{height:'20px'}}>
            </div>
      <ProTable<TableListItem>
        headerTitle="打孔管理"
        actionRef={actionRef}
        // scroll={{ x: 2400 }}
        request={async (params, sorter, filter) => {
          const { current, pageSize, keyWord, dateRange, ...rest } = params;
          let postData = {
            page: current,
            perPage: pageSize,
            ...(keyWord ? { keyWord: keyWord } : {}),
            ...(dateRange ? { dateStart: dateRange?.[0], dateEnd: dateRange?.[1] } : {}),
          };
          const result = await postRequest('drill/get_ls', postData);
          const { data, status, msg } = result;
          let dataSource;
          let total;
          if (status === 0) {
            dataSource = data.items;
            total = data.total;
          } else {
            messageApi.open({
              type: 'error',
              content: msg,
            });
          }
          return Promise.resolve({
            data: dataSource,
            total: total,
            success: true,
          });
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 0,
          span: 4,
        }}
        // toolBarRender={() => [
        //   <Button
        //     type="primary"
        //     key="add"
        //     onClick={() => {
        //       history.push('/project/drill/add')
        //     }}
        //   >
        //     添加打孔计划
        //   </Button>
        // ]}
        scroll={{ x: 2400 }}
        columns={columns}
        //   options={false}
      />
    </>
  );
};

export default Drill;
