# 新任务看板页面

## 概述

基于原有任务看板页面创建的新版本，保留了核心的 5 个关键指标模块，并新增了 6 个数据可视化模块，使用简化的组件实现，专注于展示最重要的业务数据。

## 保留的核心指标模块

### 1. 总进尺

- **显示内容**: 总进尺数值（米）
- **附加信息**: 较昨日变化量
- **图标**: chizi.png
- **数据来源**: `data.footageSum` 和 `data.footageEd`

### 2. 运行钻机数

- **显示内容**: 当前运行的钻机数量
- **附加信息**: 运行率百分比
- **图标**: zuanji.png
- **数据来源**: `data.runSum` 和 `data.runRate`

### 3. 总任务数

- **显示内容**: 总任务数量
- **附加信息**: 任务完成率百分比
- **图标**: renwu.png
- **数据来源**: `data.taskSum` 和 `data.taskRate`

### 4. 异常事件

- **显示内容**: 异常事件数量（起）
- **附加信息**: 较昨日变化量
- **图标**: yichang.png
- **数据来源**: `data.abnormalSum` 和 `data.abnormalEd`

### 5. 设备开机数量

- **显示内容**: 设备开机数量（台）
- **附加信息**: 开机率百分比
- **图标**: operatingSum.png
- **数据来源**: `data.operatingSum` 和 `data.operatingRate`

## 技术实现

## 新增的 6 个数据可视化模块

### 1. 目标达成分析

- **显示内容**: 计划总进尺 vs 实际总进尺对比
- **附加信息**: 目标完成率 86.6% 和 目标完成率 -14.4%
- **展示方式**: 进度条 + 百分比文字显示

### 2. 设备投入产出比

- **显示内容**: 设备状态分布（运行/维修/停机）
- **附加信息**: 钻机利用率 80%，单机产效 519 米/台
- **展示方式**: 中空饼图 + 图例
- **状态数据**: 运行 24 台，维修 4 台，停机 2 台

### 3. 异常事件统计

- **显示内容**: 按类型统计异常事件
- **类型分类**: 液压系统、泵压系统、瓦斯突出、其他
- **展示方式**: 横向柱状图
- **数据范围**: 0-7 起事件

### 4. 钻机效率对比

- **显示内容**: 各钻机效率对比
- **分类标准**: A 类、B 类、C 类设备
- **展示方式**: 进度条显示效率百分比

### 5. 设备健康评分排行榜

- **显示内容**: 设备健康评分排序
- **排序方式**: 按评分从高到低排列
- **展示方式**: 排名 + 进度条显示评分

### 6. 班组效能对比

- **显示内容**: 班组任务完成情况
- **数据项**: 任务完成率、已完成任务数、总任务数、平均效率
- **展示方式**: 表格形式

### 文件结构

```
src/pages/taskDashboard/
├── index.tsx                    # 原任务看板页面（保留）
├── index.less                  # 原样式文件
├── simpleTaskDashboard.tsx     # 新任务看板页面（简化组件版本）
├── newTaskDashboard.less       # 新页面样式文件
└── README.md                   # 说明文档
```

### API 接口

- **接口地址**: `dataBoard/get_ls`
- **请求方式**: GET
- **返回数据**: 包含所有指标的统计数据

### 路由配置

- **新任务看板**: `/newTaskDashboard` - 使用简化组件实现

## 技术实现

### 卡片尺寸规范

- **宽度**: 402px
- **高度**: 425px
- 所有图表卡片统一使用此尺寸规范

### 数据可视化方案

- **目标达成分析**: Progress 组件 + 文字显示
- **设备投入产出比**: CSS conic-gradient 实现中空饼图
- **异常事件统计**: 自定义横向柱状图
- **其他模块**: Progress 组件 + Table 组件

## 特性

1. **响应式布局**: 支持不同屏幕尺寸的自适应显示
2. **实时数据**: 页面加载时自动获取最新数据
3. **错误处理**: 包含完善的错误处理机制
4. **样式一致**: 保持与原页面相同的视觉风格
5. **简化实现**: 使用原生组件，避免复杂图表库依赖
6. **标准尺寸**: 所有卡片统一 402x425px 尺寸

## 使用方法

1. 启动项目: `npm run start:dev`
2. 访问地址: `http://localhost:8000/newTaskDashboard`
3. 或通过左侧菜单导航到"新任务看板"

## 与原页面的区别

| 功能       | 原任务看板        | 新任务看板  |
| ---------- | ----------------- | ----------- |
| 核心指标   | ✅ 5 个模块       | ✅ 5 个模块 |
| 饼状图     | ✅ 孔任务完成情况 | ❌ 移除     |
| 柱状图     | ✅ 能源消耗统计   | ❌ 移除     |
| 折线图     | ✅ 设备运行时长   | ❌ 移除     |
| 数据表格   | ✅ 近期异常数据   | ❌ 移除     |
| 页面复杂度 | 高                | 低          |
| 加载速度   | 较慢              | 较快        |

## 维护说明

- 数据逻辑与原页面完全一致，确保数据准确性
- 样式文件独立，不会影响原页面
- 可根据需要进一步定制和扩展功能
