/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-04-03 15:07:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-08 09:15:38
 * @FilePath: \diy_tfl_pc\config\defaultSettings.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'realDark',
  // 拂晓蓝
  colorPrimary: '#1890ff',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: '瓦斯治理信息化系统',
  pwa: true,
  logo: 'https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/yunpingtai/2025-04-07/1ca2dc885612750355f148af62048cd8.png',
  iconfontUrl: '',
  token: {
    // 参见ts声明，demo 见文档，通过token 修改样式
    //https://procomponents.ant.design/components/layout#%E9%80%9A%E8%BF%87-token-%E4%BF%AE%E6%94%B9%E6%A0%B7%E5%BC%8F
  },
};

export default Settings;
