.container {
  display: flex;
  gap: 24px;
  padding: 20px;
  max-height: 100%;
  /* background-color: #1a1a1a; */
  /* min-height: 100vh; */
}

.videoSection {
  flex: 1;
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.videoPlayer {
  width: 100%;
  aspect-ratio: 16/9;
  background-color: #000;
}

.videoList {
  flex: 0 0 30%;
  padding: 20px;
  background: #1A1A1A;
  /* background-color: #000; */
  min-height: 100%;
}

.videoList h2 {
  color: #fff;
  margin-bottom: 20px;
  font-size: 18px;
}

.videoItem {
  padding: 16px;
  /* background-color: #0D1B2A; */
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.videoItem:hover {
  background-color: #152537;
  border-radius: 16px;
}

.selected {
  background-color: #152537;
  border-radius: 16px;
}

.videoInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.timeAndDate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-size: 16px;
  margin-bottom: 8px;
}

.date {
  color: #8B949E;
  font-size: 14px;
}

.title {
  color: #8B949E;
  font-size: 14px;
}

.listContainer {
  overflow-y: auto;
  height: calc(100% - 60px);
}

/* 自定义滚动条样式 */
.listContainer::-webkit-scrollbar {
  width: 6px;
}

.listContainer::-webkit-scrollbar-track {
  background: #0D1B2A;
}

.listContainer::-webkit-scrollbar-thumb {
  background: #2C3E50;
  border-radius: 3px;
}

.videoCurrentInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 0;
}

.videoName {
  color: #fff;
  font-size: 20px;
}

.videoTimeInfo {
    color: #9CA3AF;
  font-size: 14px;
}
/* 自定义时间轴样式 */
.customTimelineContainer {
  width: 100%;
  margin: 10px 0;
  padding: 5px 0;
}

.timeDisplay {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  color: #ccc;
}

.actualTime {
  margin-left: auto;
  color: #a0a0a0;
}

.timeline {
  position: relative;
  height: 10px;
  background: #444;
  border-radius: 5px;
  cursor: pointer;
}

.timelineBackground {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #444;
  border-radius: 5px;
}

.segmentMarker {
  position: absolute;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  top: 0;
}

.progressBar {
  position: absolute;
  height: 100%;
  background: #1890ff;
  border-radius: 5px;
  transition: width 0.1s linear;
}

.seekThumb {
  position: absolute;
  width: 14px;
  height: 14px;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* 播放控制按钮 */
.videoControls {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.playPauseButton {
  padding: 5px 5px;
  background: rgba(0, 0, 0, 0);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* .playPauseButton:hover {
  background: #40a9ff;
} */