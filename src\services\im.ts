import NIM from 'nim-web-sdk-ng';

let nimInstance = null;

export const initNIM = () => {
  console.log('初始化 NIM 实例');
  if (!nimInstance) {
    nimInstance = NIM.getInstance({
      appkey: '3e323596d68cb799f511fd101d4a338a',
      debugLevel: 'debug',
      apiVersion: 'v2'
    });
    console.log('NIM 实例已创建:', nimInstance);
  }
  return nimInstance;
};

export const getNIM = () => {
  console.log('获取 NIM 实例:', nimInstance);
  return nimInstance;
};

export const setNIM = (instance) => {
  console.log('设置 NIM 实例:', instance);
  nimInstance = instance;
};

// 添加销毁方法
export const destroyNIM = () => {
  if (nimInstance) {
    try {
      nimInstance.destroy();
      nimInstance = null;
    } catch (error) {
      console.error('销毁 NIM 实例失败:', error);
    }
  }
}; 