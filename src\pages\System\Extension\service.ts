/**
 * 分机号管理API服务
 */
import { getRequest, postRequest } from '@/services/api/api';
import { ExtensionItem, ExtensionFormData, ExtensionListParams, ApiResponse, ExtensionListResponse } from './types';

// 分机号管理API服务
export const extensionService = {
  /**
   * 获取分机号列表
   * @param params 查询参数
   * @returns Promise<ApiResponse<ExtensionListResponse>>
   */
  async getList(params: ExtensionListParams = {}): Promise<ApiResponse<ExtensionListResponse>> {
    try {
      const { page = 1, perPage = 10, account } = params;

      const requestParams: any = {
        page,
        perPage,
      };

      // 添加账号模糊查询参数
      if (account) {
        requestParams.account = account;
      }

      const response = await postRequest<ApiResponse<ExtensionListResponse>>('/extension/get_ls', requestParams);
      return response;
    } catch (error) {
      console.error('获取分机号列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取分机号详情
   * @param id 分机号ID
   * @returns Promise<ApiResponse<ExtensionItem>>
   */
  async getDetail(id: number): Promise<ApiResponse<ExtensionItem>> {
    try {
      const response = await getRequest<ApiResponse<ExtensionItem>>('/extension/get_info', { id });
      return response;
    } catch (error) {
      console.error('获取分机号详情失败:', error);
      throw error;
    }
  },

  /**
   * 添加分机号
   * @param formData 表单数据
   * @returns Promise<ApiResponse<ExtensionItem>>
   */
  async add(formData: ExtensionFormData): Promise<ApiResponse<ExtensionItem>> {
    try {
      const response = await postRequest<ApiResponse<ExtensionItem>>('/extension/post_add', formData);
      return response;
    } catch (error) {
      console.error('添加分机号失败:', error);
      throw error;
    }
  },

  /**
   * 修改分机号
   * @param id 分机号ID
   * @param formData 表单数据
   * @returns Promise<ApiResponse<ExtensionItem>>
   */
  async update(id: number, formData: ExtensionFormData): Promise<ApiResponse<ExtensionItem>> {
    try {
      const requestData = {
        id,
        ...formData,
      };
      const response = await postRequest<ApiResponse<ExtensionItem>>('/extension/post_modify', requestData);
      return response;
    } catch (error) {
      console.error('修改分机号失败:', error);
      throw error;
    }
  },

  /**
   * 删除分机号
   * @param id 分机号ID
   * @returns Promise<ApiResponse<null>>
   */
  async delete(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await postRequest<ApiResponse<null>>('/extension/post_del', { id });
      return response;
    } catch (error) {
      console.error('删除分机号失败:', error);
      throw error;
    }
  },
};
