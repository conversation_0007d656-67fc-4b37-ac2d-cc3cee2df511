import React from 'react';
import { ProForm, ProFormText, ProFormSelect, ProFormTextArea } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { ExtensionFormData } from '../types';

interface ExtensionFormProps {
  initialValues?: ExtensionFormData;
  onFinish: (values: ExtensionFormData) => Promise<void>;
  loading?: boolean;
}

const ExtensionForm: React.FC<ExtensionFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();

  const handleFinish = async (values: ExtensionFormData) => {
    try {
      await onFinish(values);
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  return (
    <ProForm
      form={form}
      layout="vertical"
      initialValues={initialValues}
      onFinish={handleFinish}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '重置',
        },
        submitButtonProps: {
          loading,
        },
      }}
    >
      <ProFormText
        name="account"
        label="账号"
        placeholder="请输入账号"
        rules={[
          { required: true, message: '请输入账号' },
          { min: 3, message: '账号至少3位' },
          { max: 20, message: '账号最多20位' },
          {
            pattern: /^[a-zA-Z0-9_]+$/,
            message: '账号只能包含字母、数字和下划线'
          },
        ]}
        fieldProps={{
          maxLength: 20,
        }}
      />

      <ProFormText.Password
        name="password"
        label="密码"
        placeholder="请输入密码"
        rules={[
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码至少6位' },
          { max: 20, message: '密码最多20位' },
          {
            pattern: /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]*$/,
            message: '密码只能包含字母、数字和特殊字符'
          },
        ]}
        fieldProps={{
          maxLength: 20,
        }}
      />

      <ProFormText
        name="bindCode"
        label="关联Code"
        placeholder="请输入关联Code（可选）"
        rules={[
          { max: 50, message: '关联Code最多50位' },
        ]}
        fieldProps={{
          maxLength: 50,
        }}
      />

      <ProFormSelect
        name="bindType"
        label="绑定类型"
        placeholder="请选择绑定类型（可选）"
        options={[
          { label: '设备', value: 1 },
          { label: '用户', value: 2 },
        ]}
      />

      <ProFormSelect
        name="status"
        label="状态"
        placeholder="请选择状态"
        options={[
          { label: '正常', value: 0 },
          { label: '禁用', value: 1 },
        ]}
        initialValue={0}
      />
    </ProForm>
  );
};

export default ExtensionForm;
