import React, { useState, useRef, useEffect } from 'react';
import { Layout, Flex, Button, Tree, message, Drawer, Form, TreeSelect, Modal, Popconfirm } from 'antd';
import type { DataNode } from 'antd/es/tree';
import type { GetProp, TreeSelectProps } from 'antd';
import { ApartmentOutlined, SlidersOutlined, PlusOutlined, EllipsisOutlined } from '@ant-design/icons';
import { ProTable, ProForm, ProFormText } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest, getRequest } from '@/services/api/api';

const { Sider, Content } = Layout;

interface DepartmentItem {
    id: string;
    name: string;
    parentId: string;
    number: number;
    hasChildren: boolean;
    children?: DepartmentItem[];
}

interface ApiResponse {
    data: DepartmentItem[];
    status: number;
    msg: string;
}

export type TableListItem = {
    id: number;
    account: string;
    name: string;
    position: string;
    bindIdcard: string;
    bind: '0' | '1';
    departmentName: string;
    departmentId: string;
    createdAt: string;
    updatedAt: string;
};

interface MemberApiResponse {
    data: {
        items: TableListItem[];
        total: number;
    };
    status: number;
    msg: string;
}

interface AddEmployeeResponse {
    status: number;
    msg: string;
    data: any;
}

type DefaultOptionType = GetProp<TreeSelectProps, 'treeData'>[number];

interface Props {
    onTabChange?: (tab: string) => void;
}

const Member: React.FC<Props> = ({ onTabChange }) => {
    const [treeData, setTreeData] = useState<DataNode[]>([]);
    const [selectedDept, setSelectedDept] = useState<string>();
    const [selectedDeptName, setSelectedDeptName] = useState<string>('');
    const [loading, setLoading] = useState(true);
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [addDeptDrawerVisible, setAddDeptDrawerVisible] = useState(false);
    const [editingEmployee, setEditingEmployee] = useState<TableListItem>();
    const [editDrawerVisible, setEditDrawerVisible] = useState(false);
    const actionRef = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm();
    const [editForm] = Form.useForm();
    const [deptForm] = Form.useForm();
    const [deptTreeData, setDeptTreeData] = useState<DefaultOptionType[]>([]);

    // 处理删除
    const handleDelete = async (id: number) => {
        try {
            const result = await getRequest('employee/post_del', { id }) as AddEmployeeResponse;
            if (result.status === 0) {
                messageApi.success('删除成功');
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '删除失败');
            }
        } catch (error) {
            messageApi.error('删除失败');
        }
    };

    // 处理编辑
    const handleEdit = async (values: any) => {
        try {
            // console.log('开始编辑，当前选中部门ID:', selectedDept);
            // console.log('当前部门名称:', selectedDeptName);
            // console.log('编辑前的人数:', selectedDeptName.match(/（(\d+)人）/)?.[1] || '0');
            
            const originalDeptId = editingEmployee?.departmentId;
            const targetDeptId = values.departmentId.value || values.departmentId;

            const result = await postRequest('employee/post_modify', {
                id: editingEmployee?.id,
                account: values.account,
                name: values.name,
                position: values.position,
                bindIdcard: values.bindIdcard || '',
                departmentId: targetDeptId,
                bind: values.bindIdcard ? '1' : '0'
            }) as AddEmployeeResponse;

            if (result.status === 0) {
                messageApi.success('编辑成功');
                setEditDrawerVisible(false);
                editForm.resetFields();
                
                // 从当前名称中提取部门名和人数
                const deptName = selectedDeptName.replace(/（.*?）/g, '');
                const currentNumber = parseInt(selectedDeptName.match(/（(\d+)人）/)?.[1] || '0');
                // console.log('提取的部门名称:', deptName);
                // console.log('提取的当前人数:', currentNumber);
                
                // 根据部门变化调整人数
                let newNumber = currentNumber;
                
                // 使用字符串比较而不是对象比较
                if (String(originalDeptId) === String(selectedDept) && String(targetDeptId) !== String(selectedDept)) {
                    // 如果是从当前部门移出
                    newNumber = Math.max(0, currentNumber - 1);
                } else if (String(originalDeptId) !== String(selectedDept) && String(targetDeptId) === String(selectedDept)) {
                    // 如果是移入当前部门
                    newNumber = currentNumber + 1;
                } else {
                    console.log('员工部门变化与当前显示部门无关，人数不变');
                }
                
                const updatedName = `${deptName}（${newNumber}人）`;
                // console.log('更新后的部门名称:', updatedName);
                // console.log('更新后的人数:', newNumber);
                setSelectedDeptName(updatedName);
                
                // 保存当前状态
                const currentDeptId = selectedDept;
                const currentExpandedKeys = [...expandedKeys];
                
                // 重新加载部门树以保证树上的显示也是正确的
                const rootData = await postRequest('department/get_ls', { parentId: 0 }) as ApiResponse;
                if (rootData.status === 0) {
                    console.log('重新加载部门树成功');
                    const newTreeData = transformToTreeData(rootData.data);
                    setTreeData(newTreeData);
                    
                    // 恢复展开状态
                    setExpandedKeys(currentExpandedKeys);
                    
                    // 恢复选中状态
                    setSelectedDept(currentDeptId);
                    
                    // 重新加载所有已展开的节点
                    for (const key of currentExpandedKeys) {
                        if (key.toString() !== '0') { // 跳过根节点
                            const childrenData = await clickDepartmentList(key.toString());
                            setTreeData(prev => updateTreeData(prev, key, childrenData));
                        }
                    }
                }
                
                // 重新加载成员列表
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '编辑失败');
            }
        } catch (error) {
            console.error('编辑失败:', error);
            messageApi.error('编辑失败');
        }
    };

    // 将部门数据转换为树形结构
    const transformToTreeData = (items: DepartmentItem[]): DataNode[] => {
        return items.map(item => ({
            title: `${item.name}（${item.number ? item.number : 0}人）`,
            key: item.id,
            isLeaf: !item.hasChildren,
        }));
    };

    // 处理树节点选择
    const onSelect = (selectedKeys: React.Key[], info: any) => {
        const deptId = selectedKeys[0]?.toString();
        setSelectedDept(deptId);
        // 设置选中的部门名称
        setSelectedDeptName(info.node.title?.toString() || '');
        // 刷新成员列表
        actionRef.current?.reload();
    };

    // 初始化加载部门列表
    const initDepartmentList = async () => {
        setLoading(true);
        try {
            const result = await postRequest('department/get_ls', { parentId: 0 }) as ApiResponse;
            if (result.status === 0) {
                const treeData = transformToTreeData(result.data);
                setTreeData(treeData);
                // 设置初始展开的节点
                setExpandedKeys(treeData.map(node => node.key));
                // 如果有数据，默认选中第一项
                if (result.data && result.data.length > 0) {
                    const firstDeptId = result.data[0].id;
                    const firstName = `${result.data[0].name}（${result.data[0].number?result.data[0].number:0}人）`;
                    setSelectedDept(firstDeptId);
                    setSelectedDeptName(firstName);
                }
            } else {
                messageApi.error(result.msg || '获取部门列表失败');
            }
        } catch (error) {
            messageApi.error('获取部门列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 点击后加载部门列表
    const clickDepartmentList = async (id: string) => {
        try {
            const result = await postRequest('department/get_ls', { parentId: id }) as ApiResponse;
            if (result.status === 0) {
                const treeData = transformToTreeData(result.data);
                return treeData;
            } else {
                messageApi.error(result.msg || '获取部门列表失败');
                return [];
            }
        } catch (error) {
            messageApi.error('获取部门列表失败');
            return [];
        }
    };

    // 更新树节点数据
    const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] => {
        return list.map((node) => {
            if (node.key === key) {
                // 将新加载的节点的key添加到expandedKeys中
                setExpandedKeys(prev => [...prev, ...children.map(child => child.key)]);
                return {
                    ...node,
                    children,
                };
            }
            if (node.children) {
                return {
                    ...node,
                    children: updateTreeData(node.children, key, children),
                };
            }
            return node;
        });
    };

    const onLoadData = ({ key }: any) =>
        new Promise<void>((resolve) => {
            clickDepartmentList(key).then(childrenData => {
                setTreeData(origin => updateTreeData(origin, key, childrenData));
                resolve();
            });
        });

    useEffect(() => {
        initDepartmentList();
    }, []);

    // 生成部门树节点
    const transformToDeptNode = (item: DepartmentItem): DefaultOptionType => {
        return {
            id: item.id,
            pId: item.parentId,
            value: item.id,
            title: item.name,
            isLeaf: !item.hasChildren,
        };
    };

    // 获取当前部门的父级部门链
    const getDepartmentChain = async (departmentId: string) => {
        const loadParentDepartment = async (id: string) => {
            try {
                const result = await postRequest('department/get_ls', { parentId: id }) as ApiResponse;
                if (result.status === 0 && result.data) {
                    const nodes = result.data.map(item => transformToDeptNode(item));
                    setDeptTreeData(origin => {
                        const newData = [...origin];
                        nodes.forEach(node => {
                            if (!newData.find(item => item.id === node.id)) {
                                newData.push(node);
                            }
                        });
                        return newData;
                    });

                    // 如果有父级部门，继续递归加载
                    const currentDept = result.data.find(item => item.id === departmentId);
                    if (currentDept && currentDept.parentId && currentDept.parentId !== '0') {
                        await loadParentDepartment(currentDept.parentId);
                    }
                }
            } catch (error) {
                messageApi.error('获取部门数据失败');
            }
        };

        // 从当前部门开始向上加载
        await loadParentDepartment('0');
    };

    // 初始化部门树数据
    const initDeptTreeData = async () => {
        const processTreeData = (data: any[]): any[] => {
            return data.map(item => {
                const node: any = {
                    id: item.key,
                    pId: item.parentId,
                    value: item.key,
                    title: item.title.replace(/（.*?）/g, ''),
                    isLeaf: item.isLeaf,
                };

                if (item.children) {
                    node.children = processTreeData(item.children);
                }

                return node;
            });
        };

        const processedTreeData = processTreeData(treeData);
        setDeptTreeData(processedTreeData);
    };

    // 加载子部门数据
    const onLoadDeptData: TreeSelectProps['loadData'] = ({ id }) =>
        new Promise((resolve) => {
            postRequest('department/get_ls', { parentId: id })
                .then((response: any) => {
                    const result = response as ApiResponse;
                    if (result.status === 0) {
                        const newNodes = result.data.map(item => transformToDeptNode(item));
                        console.log('测试2222222222222222222222222222', newNodes);

                        setDeptTreeData(origin => [...origin, ...newNodes]);
                    }
                    resolve(undefined);
                })
                .catch(() => {
                    messageApi.error('加载子部门失败');
                    resolve(undefined);
                });
        });

    const columns: ProColumns<TableListItem>[] = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: 80,
            search: false,
        },
        {
            title: '账号',
            dataIndex: 'account',
            width: 120,
        },
        {
            title: '姓名',
            dataIndex: 'name',
            width: 100,
        },
        {
            title: '职位',
            dataIndex: 'position',
            width: 120,
        },
        {
            title: '绑定卡号',
            dataIndex: 'bindIdcard',
            width: 120,
        },
        {
            title: '绑定状态',
            dataIndex: 'bind',
            width: 100,
            valueEnum: {
                0: { text: '未绑定', status: 'Default' },
                1: { text: '已绑定', status: 'Success' },
            },
        },
        {
            title: '所属部门',
            dataIndex: 'departmentName',
            width: 150,
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 150,
            valueType: 'dateTime',
        },
        {
            title: '修改时间',
            dataIndex: 'updatedAt',
            width: 150,
            valueType: 'dateTime',
        },
        {
            title: '操作',
            valueType: 'option',
            width: 120,
            fixed: 'right',
            render: (_, record) => [
                <a
                    key="edit"
                    onClick={async () => {
                        setEditingEmployee(record);
                        // 先获取部门链，确保父级部门数据存在
                        await getDepartmentChain(record.departmentId);
                        editForm.setFieldsValue({
                            account: record.account,
                            name: record.name,
                            position: record.position,
                            bindIdcard: record.bindIdcard,
                            departmentId: {
                                value: record.departmentId,
                                label: record.departmentName
                            }
                        });
                        setEditDrawerVisible(true);
                    }}
                >
                    编辑
                </a>,
                <a
                    key="delete"
                    onClick={() => {
                        Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除员工 ${record.name} 吗？`,
                            okText: '确认',
                            cancelText: '取消',
                            onOk: () => handleDelete(record.id)
                        });
                    }}
                >
                    删除
                </a>,
            ],
        },
    ];

    // 处理添加成员
    const handleAdd = async (values: any) => {
        try {
            const result = await postRequest('employee/post_add', {
                account: values.account,
                name: values.name,
                position: values.position,
                bindIdcard: values.bindIdcard || '',
                departmentId: values.departmentId.value || values.departmentId,
                bind: values.bindIdcard ? '1' : '0'
            }) as AddEmployeeResponse;

            if (result.status === 0) {
                messageApi.success('添加成功');
                setDrawerVisible(false);
                form.resetFields();
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '添加失败');
            }
        } catch (error) {
            messageApi.error('添加失败');
        }
    };

    // 处理添加子部门
    const handleAddDept = async (values: any) => {
        try {
            const result = await postRequest('department/post_add', {
                name: values.name,
                parentId: values.parentId || '0'
            }) as AddEmployeeResponse;

            if (result.status === 0) {
                messageApi.success('添加成功');
                setAddDeptDrawerVisible(false);
                deptForm.resetFields();

                // 重新加载父级节点数据
                if (values.parentId === '0') {
                    // 如果是根节点，重新加载整个树
                    initDepartmentList();
                } else {
                    // 获取父级节点的所有子节点
                    const parentData = await clickDepartmentList(values.parentId);
                    // 更新树数据
                    setTreeData(origin => updateTreeData(origin, values.parentId, parentData));
                }
            } else {
                messageApi.error(result.msg || '添加失败');
            }
        } catch (error) {
            messageApi.error('添加失败');
        }
    };

    // 处理展开/收起
    const onExpand = (newExpandedKeys: React.Key[]) => {
        setExpandedKeys(newExpandedKeys);
    };

    return (
        <Layout style={{ height: '100%' }}>
            {contextHolder}
            <Sider width={280} style={{ background: 'transparent', borderRight: '1px solid #313131' }}>
                <div style={{ padding: '16px' }}>
                    {/* <Input.Search placeholder="搜索部门" style={{ marginBottom: '16px' }} /> */}
                    <Flex gap="small" style={{ marginBottom: '16px' }}>
                        <Button
                            icon={<ApartmentOutlined />}
                            onClick={async () => {
                                initDeptTreeData()
                                deptForm.resetFields();
                                deptForm.setFieldsValue({
                                    parentId: selectedDept || '0'
                                });
                                setAddDeptDrawerVisible(true);
                            }}
                        >
                            添加子部门
                        </Button>
                        <Button
                            icon={<SlidersOutlined />}
                            onClick={() => onTabChange?.('department')}
                        >
                            部门管理
                        </Button>
                    </Flex>
                    <Tree
                        loadData={onLoadData}
                        treeData={treeData}
                        onSelect={onSelect}
                        selectedKeys={selectedDept ? [selectedDept] : []}
                        expandedKeys={expandedKeys}
                        onExpand={onExpand}
                        style={{
                            background: 'transparent',
                            fontSize: '14px',
                            width: '100%'
                        }}
                    />
                </div>
            </Sider>
            <Content style={{ padding: '16px' }}>
                {!loading && selectedDept && (
                    <ProTable<TableListItem>
                        columns={columns}
                        actionRef={actionRef}
                        request={async (params) => {
                            try {
                                const result = await postRequest('employee/get_ls', {
                                    ...params,
                                    parentId: selectedDept
                                }) as MemberApiResponse;
                                return {
                                    data: result.data.items || [],
                                    success: result.status === 0,
                                    total: result.data.total || 0,
                                };
                            } catch (error) {
                                return {
                                    data: [],
                                    success: false,
                                    total: 0,
                                };
                            }
                        }}
                        rowKey="id"
                        search={false}
                        pagination={{
                            pageSize: 10,
                        }}
                        dateFormatter="string"
                        headerTitle={`${selectedDeptName || '请选择部门'}`}
                        toolBarRender={() => [
                            <Button
                                key="add"
                                type="primary"
                                icon={<PlusOutlined />}
                                onClick={async () => {
                                    initDeptTreeData()
                                    setDrawerVisible(true);
                                }}
                            >
                                添加成员
                            </Button>,
                        ]}
                    />
                )}
                <Drawer
                    title="添加成员"
                    width={600}
                    open={drawerVisible}
                    onClose={() => {
                        setDrawerVisible(false);
                        form.resetFields();
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setDrawerVisible(false);
                            form.resetFields();
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setDrawerVisible(false);
                                form.resetFields();
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => form.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={form}
                        onFinish={handleAdd}
                        submitter={false}
                        initialValues={{
                            bind: '0'
                        }}
                    >
                        <ProFormText
                            name="account"
                            label="账号"
                            placeholder="请输入账号"
                            rules={[{ required: true, message: '请输入账号' },
                                { pattern: /^1\d{10}$/, message: '请输入11位手机号' }
                            ]}
                            
                        />
                        <ProFormText
                            name="name"
                            label="姓名"
                            placeholder="请输入姓名"
                            rules={[{ required: true, message: '请输入姓名' }]}
                        />
                        <ProFormText
                            name="position"
                            label="职位"
                            placeholder="请输入职位"
                            rules={[{ required: true, message: '请输入职位' }]}
                        />
                        <ProFormText
                            name="bindIdcard"
                            label="绑定卡号"
                            placeholder="请输入绑定卡号"
                            // rules={[{ required: true, message: '请输入卡号' }]}
                        />
                        <Form.Item
                            name="departmentId"
                            label="所属部门"
                            rules={[{ required: true, message: '请选择所属部门' }]}
                        >
                            <TreeSelect
                                treeDataSimpleMode
                                style={{ width: '100%' }}
                                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                placeholder="请选择所属部门"
                                loadData={onLoadDeptData}
                                treeData={deptTreeData}
                                showSearch
                                treeNodeFilterProp="title"
                                labelInValue
                            />
                        </Form.Item>
                    </ProForm>
                </Drawer>
                <Drawer
                    title="编辑成员"
                    width={600}
                    open={editDrawerVisible}
                    onClose={() => {
                        setEditDrawerVisible(false);
                        editForm.resetFields();
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setEditDrawerVisible(false);
                            editForm.resetFields();
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setEditDrawerVisible(false);
                                editForm.resetFields();
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => editForm.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={editForm}
                        onFinish={handleEdit}
                        submitter={false}
                    >
                        <ProFormText
                            name="account"
                            label="账号"
                            placeholder="请输入账号"
                            rules={[{ required: true, message: '请输入账号' },
                                { pattern: /^1\d{10}$/, message: '请输入11位手机号' }
                            ]}
                            disabled={!!editingEmployee?.id}
                        />
                        <ProFormText
                            name="name"
                            label="姓名"
                            placeholder="请输入姓名"
                            rules={[{ required: true, message: '请输入姓名' }]}
                        />
                        <ProFormText
                            name="position"
                            label="职位"
                            placeholder="请输入职位"
                            rules={[{ required: true, message: '请输入职位' }]}
                        />
                        <ProFormText
                            name="bindIdcard"
                            label="绑定卡号"
                            placeholder="请输入绑定卡号"
                        />
                        <Form.Item
                            name="departmentId"
                            label="所属部门"
                            rules={[{ required: true, message: '请选择所属部门' }]}
                        >
                            <TreeSelect
                                treeDataSimpleMode
                                style={{ width: '100%' }}
                                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                placeholder="请选择所属部门"
                                loadData={onLoadDeptData}
                                treeData={deptTreeData}
                                showSearch
                                treeNodeFilterProp="title"
                                labelInValue
                            />
                        </Form.Item>
                    </ProForm>
                </Drawer>
                {/* 添加子部门抽屉 */}
                <Drawer
                    title="添加子部门"
                    width={600}
                    open={addDeptDrawerVisible}
                    onClose={() => {
                        setAddDeptDrawerVisible(false);
                        deptForm.resetFields();
                    }}
                    destroyOnClose
                    closeIcon={false}
                    styles={{
                        header: {
                            borderBottom: 'none',
                        },
                        footer: {
                            borderTop: 'none',
                        }
                    }}
                    extra={
                        <Button type="text" onClick={() => {
                            setAddDeptDrawerVisible(false);
                            deptForm.resetFields();
                        }}>
                            ✕
                        </Button>
                    }
                    footer={
                        <Flex justify="flex-end" gap="small">
                            <Button onClick={() => {
                                setAddDeptDrawerVisible(false);
                                deptForm.resetFields();
                            }}>
                                取消
                            </Button>
                            <Button type="primary" onClick={() => deptForm.submit()}>
                                确认
                            </Button>
                        </Flex>
                    }
                >
                    <ProForm
                        form={deptForm}
                        onFinish={handleAddDept}
                        submitter={false}
                        initialValues={{
                            parentId: selectedDept || '0'
                        }}
                    >
                        <ProFormText
                            name="name"
                            label="部门名称"
                            placeholder="请输入部门名称"
                            rules={[{ required: true, message: '请输入部门名称' }]}
                        />
                        <Form.Item
                            name="parentId"
                            label="上级部门"
                            rules={[{ required: true, message: '请选择上级部门' }]}
                        >
                            <TreeSelect
                                treeDataSimpleMode
                                style={{ width: '100%' }}
                                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                placeholder="请选择上级部门"
                                loadData={onLoadDeptData}
                                treeData={deptTreeData}
                                showSearch
                                treeNodeFilterProp="title"
                            />
                        </Form.Item>
                    </ProForm>
                </Drawer>
            </Content>
        </Layout>
    );
};

export default Member; 