import { List } from 'antd';
import React, { useState, useEffect } from 'react';
import { Button, Modal, Form, Input, Statistic, Flex, message } from 'antd';
import type { FormProps } from 'antd';
import { getRequest, postRequest } from '@/services/api/api';
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';

type Unpacked<T> = T extends (infer U)[] ? U : T;

const passwordStrength = {
  2: <span className="strong">高</span>,
  1: <span className="medium">中</span>,
  0: <span className="weak">低</span>,
};

const SecurityView: React.FC = () => {
  const [form] = Form.useForm();
  const { Countdown } = Statistic;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [type, setType] = useState(0);
  const [title, setTilte] = useState('账户密码');
  const [isOperation, setIsOperation] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState([
    {
      title: '账户密码',
      description: (
        <>
          当前密码强度：
          {passwordStrength.medium}
        </>
      ),
      actions: [<a key="Modify" onClick={() => showModal(0)} >修改</a>],
    },
    {
      title: '手机号绑定',
      description: `未绑定手机号`,
      actions: [<a key="Modify" onClick={() => showModal(1)} >绑定</a>],
    },
    {
      title: '实名认证',
      description: `未实名`,
      actions: [<a key="Modify" onClick={() => showModal(2)}>绑定</a>],
    },
  ]);

  const getData = async () => {
    const result = await getRequest('personal/get_info');
    const { data, status, msg } = result
    if (status === 0) {
      // setCurrentUser(data)
      console.log('data', data);
      let hiddenPhone = ''
      let hiddenName = ''
      if (data.phoneNumber) {
        hiddenPhone = data.phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
      }
      if (data.authentication) {
        if (data.name.length > 2) {
          hiddenName = data.name.substring(0, 1) + '*'.repeat(data.name.length - 2) + data.name.substring(data.name.length - 1)
        } else {
          hiddenName = data.name.substring(0, 1) + '*'
        }
      }
      setCurrentUser([
        {
          title: '账户密码',
          description: (
            <>
              当前密码强度：
              {passwordStrength[data.passwordStrength]}
            </>
          ),
          actions: [<a key="Modify" onClick={() => showModal(0)} >修改</a>],
        },
        {
          title: '手机号绑定',
          description: `${data.bind ? `已绑定手机：${hiddenPhone}` : '未绑定手机号'}`,
          // description: `已绑定手机：138****8293`,
          actions: [<a key="Modify" onClick={() => showModal(1)} >{data.bind ? '换绑' : '绑定'}</a>],
        },
        {
          title: '实名认证',
          description: `${data.authentication ? `已实名：${hiddenName}` : '未认证'}`,
          actions: [<a key="Modify" onClick={() => showModal(2)} hidden={data.authentication}>绑定</a>],
          // actions: [<a key="Modify" onClick={() => showModal(2)}>绑定</a>],
        },
      ])
    } else {
      messageApi.open({
        type: 'error',
        content: msg,
      });
    }
  }

  const showModal = (index) => {
    setIsModalOpen(true);
    setType(index)
    if (index === 0) {
      setTilte('账户密码')
    } else if (index === 1) {
      setTilte('手机号绑定')
    } else if (index === 2) {
      setTilte('实名认证')
    }
  };

  /**
  * 校验手机号是否符合规则(主要针对中国大陆手机号码)
  *
  * @param { string } phoneNumber - 要校验的手机号码字符串
  * @returns { boolean } 如果手机号码格式正确则返回true，否则返回false
*/
  const validatePhoneNumber = (phoneNumber) => {
    if (!phoneNumber) {
      return false; // 如果手机号为空，则返回 false
    }

    // 1. 去除手机号中的空格和其他非数字字符
    const sanitizedPhoneNumber = phoneNumber.replace(/\D/g, '');

    // 2. 校验处理后的手机号长度
    // 大陆地区手机号通常是 11 位数字
    if (sanitizedPhoneNumber.length !== 11) {
      return false;
    }
    // 3. 大陆手机号通常以 1 开头
    if (!sanitizedPhoneNumber.startsWith('1')) {
      return false;
    }
    // 4. 校验手机号是否符合数字格式，如果前几步没问题，已经可以判定该手机号是否为正确的手机号码格式，这一步可以省略，但是可以提高严谨性
    // 正则表达式校验，1开头 后面跟10位数字 ([0-9]{10})，^为开头，$为结尾
    const phoneRegex = /^1[0-9]{10}$/;
    if (!phoneRegex.test(sanitizedPhoneNumber)) {
      return false;
    }
    return true; // 所有校验都通过，返回 true
  }

  const handleCancel = () => {
    setIsModalOpen(false);
    form.resetFields()
  };
  const getCaptcha = async () => {
    let data1 = form.getFieldsValue()
    if (!data1.phoneNumber) {
      messageApi.open({
        type: 'error',
        content: '请输入手机号!',
      });
    } else if (!validatePhoneNumber(data1.phoneNumber)) {
      messageApi.open({
        type: 'error',
        content: '请输入正确的手机号!',
      });
    } else {
      const result = await postRequest('sms/post_send_sms', { phoneNumber: data1.phoneNumber });
      const { data, status, msg } = result
      if (status === 0) {
        messageApi.open({
          type: 'success',
          content: '短信发送成功',
        });
        setIsOperation(false)
      } else {
        messageApi.open({
          type: 'error',
          content: msg,
        });
      }
    }
  };

  const encrypt = (txt, publicKey) => {
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey); // 设置公钥
    return encryptor.encrypt(txt); // 对数据进行加密
  }

  /**
 * 校验中国大陆居民身份证号码是否合法
 *
 * @param {string} idCardNumber - 身份证号码字符串
 * @returns {boolean} 如果身份证号码格式正确则返回 true，否则返回 false
*/
  const validateIDCardNumber = (idCardNumber) => {
    //1. 校验身份证号码是否为空
    if (!idCardNumber) {
      return false; // 身份证号码为空，返回 false
    }
    // 2. 清除号码前后空格
    const sanitizedIdCardNumber = idCardNumber.trim();
    // 3. 校验长度是否是18位
    if (sanitizedIdCardNumber.length !== 18) {
      return false; // 长度不是18位，返回false
    }
    // 4. 校验前17位是否为数字
    const reg = /^\d{17}/;
    if (!reg.test(sanitizedIdCardNumber.substring(0, 17))) {
      return false;
    }
    // 5. 校验最后一位校验码
    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodeMapping = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(sanitizedIdCardNumber[i]) * factor[i];
    }
    const remainder = sum % 11
    const checkCode = checkCodeMapping[remainder];
    if (sanitizedIdCardNumber[17].toUpperCase() !== checkCode) {
      return false
    }
    return true; // 如果所有校验都通过，则返回 true
  }

  const handleOk = async () => {
    // setIsModalOpen(false);
    setConfirmLoading(true)
    console.log('111111111111111', form.getFieldsValue());
    // console.log('111111111111111', form);
    let data = form.getFieldsValue()
    if (title === '账户密码') {
      if (!data.oldPassword) {
        messageApi.open({
          type: 'error',
          content: '请输入旧密码!',
        });
        setConfirmLoading(false)
      } else if (!data.newPassword) {
        messageApi.open({
          type: 'error',
          content: '请输入新密码!',
        });
        setConfirmLoading(false)
      } else {
        let publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBKPfRCrkJ367IiaaxUjR0xIyO68pMwPEVWL/OqI78dtFPPW+zQQ1wb7YGk+pMJV2oa64cC5xZCLzPNHV6LaZe4hIxlxrcGed3aB1cLLNz0ujj1OplHD8PA2Hwlcz1bOo6U7VsQA2tYaXS9xGlBGgqNoGD3KHtDNYWiApA8FyJYwIDAQAB'
        let data1 = form.getFieldsValue()
        let oldPassword = encrypt(data1.oldPassword, publicKey)
        let newPassword = encrypt(data1.newPassword, publicKey)
        const result = await postRequest('personal/post_update_password', { newPassword, oldPassword });
        const { data, status, msg } = result
        if (status === 0) {
          messageApi.open({
            type: 'success',
            content: '修改成功',
          });
          setConfirmLoading(false)
          handleCancel()
          getData()
        } else {
          messageApi.open({
            type: 'error',
            content: msg,
          });
          setConfirmLoading(false)
        }
      }
    } else if (title === '手机号绑定') {
      if (!data.phoneNumber) {
        messageApi.open({
          type: 'error',
          content: '请输入手机号!',
        });
        setConfirmLoading(false)
      } else if (!data.code) {
        messageApi.open({
          type: 'error',
          content: '请输入验证码!',
        });
        setConfirmLoading(false)
      } else {
        let data1 = form.getFieldsValue()
        const result = await postRequest('sms/post_bind_phone_number', { ...data1 });
        const { data, status, msg } = result
        if (status === 0) {
          messageApi.open({
            type: 'success',
            content: '修改成功',
          });
          setConfirmLoading(false)
          handleCancel()
          getData()
        } else {
          messageApi.open({
            type: 'error',
            content: msg,
          });
          setConfirmLoading(false)
        }
      }
    } else if (title === '实名认证') {
      if (!data.name) {
        messageApi.open({
          type: 'error',
          content: '请输入姓名!',
        });
        setConfirmLoading(false)
      } else if (!data.idNumber) {
        messageApi.open({
          type: 'error',
          content: '请输入身份证号!',
        });
        setConfirmLoading(false)
        // } else if (!validateIDCardNumber(data.idNumber)) {
        //   messageApi.open({
        //     type: 'error',
        //     content: '请输入正确的身份证号!',
        //   });
      } else {
        let data1 = form.getFieldsValue()
        const result = await postRequest('personal/post_authenticate', { ...data1 });
        const { data, status, msg } = result
        if (status === 0) {
          messageApi.open({
            type: 'success',
            content: '修改成功',
          });
          setConfirmLoading(false)
          handleCancel()
          getData()
        } else {
          messageApi.open({
            type: 'error',
            content: msg,
          });
          setConfirmLoading(false)
        }
      }
    }
  };

  // const getData = () => [
  //   {
  //     title: '账户密码',
  //     description: (
  //       <>
  //         当前密码强度：
  //         {passwordStrength.medium}
  //       </>
  //     ),
  //     actions: [<a key="Modify" onClick={() => showModal(0)} >修改</a>],
  //   },
  //   {
  //     title: '手机号绑定',
  //     description: `未绑定手机号`,
  //     // description: `已绑定手机：138****8293`,
  //     actions: [<a key="Modify" onClick={() => showModal(1)} >绑定</a>],
  //   },
  //   {
  //     title: '实名认证',
  //     description: `未实名`,
  //     actions: [<a key="Modify" onClick={() => showModal(2)}>绑定</a>],
  //   },
  // ];

  // const data = getData();

  useEffect(() => {
    getData()
  }, []);

  return (
    <>
      {contextHolder}
      <List
        itemLayout="horizontal"
        dataSource={currentUser}
        renderItem={(item) => (
          <List.Item actions={item.actions}>
            <List.Item.Meta title={item.title} description={item.description} />
          </List.Item>
        )}
      />
      <Modal title={title} open={isModalOpen} onOk={handleOk} onCancel={handleCancel} confirmLoading={confirmLoading}>
        {
          type === 0 ?
            <>
              <Form
                form={form}
                name="basic"
                // labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                // style={{ maxWidth: 600 }}
                // initialValues={{ remember: true }}
                // onFinish={onFinish}
                // onFinishFailed={onFinishFailed}
                autoComplete="off"
                layout="vertical"
                style={{ marginTop: '16px' }}
              >
                <Form.Item
                  label="旧密码"
                  name="oldPassword"
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="新密码"
                  name="newPassword"
                >
                  <Input />
                </Form.Item>
              </Form>
            </>
            : ''
        }
        {
          type === 1 ?
            <>
              <Form
                form={form}
                name="basic"
                // labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                // style={{ maxWidth: 600 }}
                // initialValues={{ remember: true }}
                // onFinish={onFinish}
                // onFinishFailed={onFinishFailed}
                autoComplete="off"
                layout="vertical"
                style={{ marginTop: '16px' }}
              >
                <Form.Item
                  label="新手机号"
                  name="phoneNumber"
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="验证码"
                  name="code"
                >
                  <Flex gap={10}>
                    <Input />
                    <Button type="primary" onClick={getCaptcha} disabled={!isOperation}>
                      {isOperation ?
                        '获取验证码' :
                        <Countdown
                          format="s 秒后重新发送"
                          value={Date.now() + 60 * 1000}
                          valueStyle={{ fontSize: '14px' }}
                          onFinish={() => { setIsOperation(true) }} />
                      }
                    </Button>
                  </Flex>
                </Form.Item>
              </Form>
            </>
            : ''
        }
        {
          type === 2 ?
            <>
              <Form
                form={form}
                name="basic"
                // labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                // style={{ maxWidth: 600 }}
                // initialValues={{ remember: true }}
                // onFinish={onFinish}
                // onFinishFailed={onFinishFailed}
                autoComplete="off"
                layout="vertical"
                style={{ marginTop: '16px' }}
              >
                <Form.Item
                  label="姓名"
                  name="name"
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  label="身份证号"
                  name="idNumber"
                >
                  <Input />
                </Form.Item>
              </Form>
            </>
            : ''
        }

      </Modal>
    </>
  );
};

export default SecurityView;
