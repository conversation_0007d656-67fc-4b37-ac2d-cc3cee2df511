import React, { useState, useEffect } from 'react';
import { Breadcrumb, message, Card, Row, Col, Tag, Result, Button, Spin } from 'antd';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';

const App: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const { location } = history;
    const [initialValues, setInitialValues] = useState({})
    const [msg, setMsg] = useState('')
    const [loading, setLoading] = useState<boolean>(false);
    const columns = [
        {
            title: '基本信息',
            items: [
                {
                    label: '提交人员名称',
                    dataIndex: 'submitUser',
                },
                {
                    label: '提交人员账号',
                    dataIndex: 'submitAccount',
                },
                {
                    label: '日期',
                    dataIndex: 'date',
                },
                {
                    label: '创建时间',
                    dataIndex: 'createdAt',
                },
                {
                    label: '修改时间',
                    dataIndex: 'updatedAt',
                },
            ]
        },
        {
            title: '施工信息',
            items: [
                {
                    label: '施工地点',
                    dataIndex: 'position',
                },
                {
                    label: '钻机编号',
                    dataIndex: 'drillNumber',
                },
                {
                    label: '钻孔类型',
                    dataIndex: 'drillingType',
                },
                {
                    label: '班次',
                    dataIndex: 'shift',
                },
                {
                    label: '开孔人员名称',
                    dataIndex: 'personnel',
                },
                {
                    label: '孔号',
                    dataIndex: 'holeNumber',
                },
                {
                    label: '是否需要轨迹',
                    dataIndex: 'ifTrajectoryRequired',
                    type: 'Select',
                    options: [
                        { label: '不需要', value: 0, color: 'blue' },
                        { label: '需要', value: 1, color: 'green' },
                    ],
                },
            ]
        },
        {
            title: '钻孔参数',
            items: [
                {
                    label: '孔深',
                    dataIndex: 'holeDepth',
                },
                {
                    label: '孔径',
                    dataIndex: 'holeDiameter',
                },
                {
                    label: '开孔角度',
                    dataIndex: 'holeAngle',
                },
                {
                    label: '方位',
                    dataIndex: 'direction',
                },
                {
                    label: '开孔高度',
                    dataIndex: 'holeHeight',
                },
                {
                    label: '见煤距离',
                    dataIndex: 'coalDistance',
                },
                {
                    label: '见岩距离',
                    dataIndex: 'rockDistance',
                },
                {
                    label: '扩孔起始距离',
                    dataIndex: 'reamingStartDistance',
                },
                {
                    label: '打钻起始距离',
                    dataIndex: 'drillingStartDistance',
                },
                {
                    label: '预计出煤量',
                    dataIndex: 'estimatedCoalOutput',
                },
            ]
        },
        {
            title: '误差数据',
            items: [
                {
                    label: '开孔角度误差',
                    dataIndex: 'holeAngleError',
                },
                {
                    label: '开孔方位误差',
                    dataIndex: 'holeDirectionError',
                },
                {
                    label: '开孔高度误差',
                    dataIndex: 'holeHeightError',
                },
                {
                    label: '孔深误差',
                    dataIndex: 'holeDepthError',
                },
            ]
        },
        {
            title: '调整后数据',
            items: [
                {
                    label: '开孔倾角',
                    dataIndex: 'startAngle',
                },
                {
                    label: '开孔高度',
                    dataIndex: 'startHight',
                },
                {
                    label: '方位角',
                    dataIndex: 'startAzimuth',
                },
            ]
        },
        {
            title: '钻后数据',
            items: [
                {
                    label: '开孔倾角',
                    dataIndex: 'finalInclination',
                },
                {
                    label: '开孔高度',
                    dataIndex: 'finalHoleHeight',
                },
                {
                    label: '方位角',
                    dataIndex: 'finalAzimuth',
                },
                {
                    label: '孔深',
                    dataIndex: 'finalHoleDepth',
                },
                {
                    label: '钻杆根数',
                    dataIndex: 'drillPipesNum',
                },
                {
                    label: '轨迹数据',
                    dataIndex: 'trajectory',
                },
            ]
        },
        {
            title: '审批信息',
            items: [
                {
                    label: '申请状态',
                    dataIndex: 'approveStatus',
                    type: 'Select',
                    options: [
                        { label: '待申请', value: 0, color: 'blue' },
                        { label: '同意', value: 1, color: 'green' },
                        { label: '不同意', value: 2, color: 'red' },
                    ],
                },
                {
                    label: '拒绝申请原因',
                    dataIndex: 'approveReason',
                },
                {
                    label: '开孔审批人员名称',
                    dataIndex: 'approveUser',
                },
                {
                    label: '开孔审批人员账号',
                    dataIndex: 'approveAccount',
                },
                {
                    label: '检查状态',
                    dataIndex: 'checkStatus',
                    type: 'Select',
                    options: [
                        { label: '待检查', value: 0, color: 'blue' },
                        { label: '合格', value: 1, color: 'green' },
                        { label: '不合格', value: 2, color: 'red' },
                    ],
                },
                {
                    label: '不合格原因',
                    dataIndex: 'checkReason',
                },
                {
                    label: '验孔审批人员名称',
                    dataIndex: 'checkUser',
                },
                {
                    label: '验孔审批人员账号',
                    dataIndex: 'checkAccount',
                },
                {
                    label: '完工申请',
                    dataIndex: 'status',
                    type: 'Select',
                    options: [
                        { label: '未申请', value: 0, color: 'blue' },
                        { label: '确认', value: 1, color: 'green' },
                        { label: '拒绝', value: 2, color: 'red' },
                    ],
                },
                {
                    label: '未完工原因',
                    dataIndex: 'reason',
                },
                {
                    label: '完工审批人员名称',
                    dataIndex: 'userName',
                },
                {
                    label: '完工审批人员账号',
                    dataIndex: 'userAccount',
                },
            ]
        },
    ]

    const getIDFromURLUsingSubstring = (url: string) => {
        const idParam = 'id=';
        const startIndex = url.indexOf(idParam);
        if (startIndex === -1) {
            return null
        }
        const valueStartIndex = startIndex + idParam.length;
        let valueEndIndex = url.indexOf('&', valueStartIndex);
        if (valueEndIndex === -1) {
            valueEndIndex = url.length;
        }
        return url.substring(valueStartIndex, valueEndIndex);
    }
    const getInfo = async (id) => {
        setLoading(true)
        const result = await getRequest('drill/get_info', { id });
        const { data, status, msg } = result
        if (status === 0) {
            setInitialValues(data)
            setLoading(false)
        } else {
            setMsg(msg)
            setLoading(false)
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
    }

    useEffect(() => {
        if (location.search) {
            const url = location.search;
            const idValue = getIDFromURLUsingSubstring(url);
            getInfo(idValue)
        }
    }, []);

    return (<>
        {contextHolder}
        <Breadcrumb
            items={[
                { title: '首页', },
                { title: '施工管理', },
                { title: '打孔管理', },
                { title: '打孔详情', },
            ]}
        />
        <Spin spinning={loading}>
            {msg ? <Result
                status="500"
                title="error"
                subTitle={msg}
                extra={<Button type="primary" onClick={() => { history.go(-1) }}>返回上一层</Button>}
            /> :
                <>
                    {columns.map((item) => {
                        return (
                            <Card bordered={false} key={item.title} style={{ marginTop: 24, fontSize: "20px", }}>
                                <Row gutter={[16, 28]} >
                                    <Col span={24}>
                                        <div style={{ color: '#adadad' }}>{item.title}</div>
                                    </Col>
                                    {item.items.map((it) => {
                                        return (
                                            <Col className="gutter-row" span={12} key={it.dataIndex}>
                                                <div style={{ color: '#7e7e7e', marginBottom: '14px' }}>{it.label}</div>
                                                {
                                                    it.type === 'Tag' ?
                                                        <Tag color="blue">
                                                            {initialValues[it.dataIndex] === 0 ? '0' : (initialValues[it.dataIndex] || '-')}
                                                        </Tag>
                                                        : ''
                                                }
                                                {it.type === 'Select' ?
                                                    <>
                                                        {it.options.map((option) => {
                                                            if (option.value === initialValues[it.dataIndex]) {
                                                                return <Tag key={option.value} color={option.color}>{option.label}</Tag>
                                                            }
                                                            return null;
                                                        })}
                                                        {initialValues[it.dataIndex] === undefined && <span>-</span>}
                                                    </>
                                                    : ''
                                                }
                                                {
                                                    !it.type ?
                                                        <div>
                                                            {it.addonBefore ? it.addonBefore : ""}
                                                            {initialValues[it.dataIndex] === 0 ? '0' : (initialValues[it.dataIndex] || '-')}
                                                        </div>
                                                        : ''
                                                }
                                            </Col>
                                        )
                                    })
                                    }
                                </Row>
                            </Card>
                        )
                    })}
                    <Card bordered={false} style={{ marginTop: 24, fontSize: "20px", }}>
                        <Row gutter={[16, 28]} >
                            <Col span={24}>
                                <div style={{ color: '#adadad' }}>备注</div>
                            </Col>
                            <Col span={24}>
                                {/* <div>{initialValues.remark}</div> */}
                            </Col>
                        </Row>
                    </Card>
                </>
            }
        </Spin>
    </>)
}

export default App;