import React, { useState } from 'react';
import * as XLSX from 'xlsx';

const ExcelEditor = () => {
  const [data, setData] = useState([]);
  const [editableData, setEditableData] = useState([]);

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      setData(json);
      setEditableData(json.map(row => row.map(cell => ({ value: cell, editing: false }))));
    };
    reader.readAsArrayBuffer(file);
  };

  const handleCellChange = (rowIndex, cellIndex, value) => {
    const newData = [...editableData];
    newData[rowIndex][cellIndex] = { value, editing: false };
    setEditableData(newData);
  };

  const handleCellFocus = (rowIndex, cellIndex) => {
    const newData = [...editableData];
    newData[rowIndex][cellIndex] = { ...newData[rowIndex][cellIndex], editing: true };
    setEditableData(newData);
  };

  const handleCellBlur = (rowIndex, cellIndex) => {
    const newData = [...editableData];
    newData[rowIndex][cellIndex] = { ...newData[rowIndex][cellIndex], editing: false };
    setEditableData(newData);
  };

  const handleDownload = () => {
    const modifiedData = editableData.map(row => row.map(cell => cell.value));
    const ws = XLSX.utils.json_to_sheet(modifiedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, 'modified_output.xlsx');
  };

  return (
    <div>
      <h1>Excel Editor</h1>
      <input type="file" onChange={handleFileChange} />
      <button onClick={handleDownload}>Download Modified File</button>
      <div style={{ marginTop: '20px' }}>
        <table>
          <thead>
            <tr>
              {data.length > 0 && data[0].map((_, idx) => (
                <th key={idx}>{`Column ${idx + 1}`}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {editableData.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex}>
                    {cell.editing ? (
                      <input
                        type="text"
                        value={cell.value}
                        onChange={(e) => handleCellChange(rowIndex, cellIndex, e.target.value)}
                        onBlur={() => handleCellBlur(rowIndex, cellIndex)}
                      />
                    ) : (
                      <span
                        onClick={() => handleCellFocus(rowIndex, cellIndex)}
                        style={{ cursor: 'pointer' }}
                      >
                        {cell.value}
                      </span>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ExcelEditor;
