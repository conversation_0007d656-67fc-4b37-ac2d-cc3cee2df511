/*
 * @Author: AI Assistant
 * @Date: 2025-01-02
 * @Description: 待办详情侧边栏组件
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    Drawer,
    Descriptions,
    Button,
    Modal,
    Image,
    Steps,
    Badge,
    Tag,
    Space,
    message,
    Card,
    Row,
    Col
} from 'antd';
import {
    DeleteOutlined
} from '@ant-design/icons';
import SignatureCanvas from 'react-signature-canvas';
import EzVideoPlayer from '../../Project/Construction/component/EzVideoPlayer';
import { postRequest, getRequest, uploadCaptureImage, uploadCaptureVideo, todoApproval } from '@/services/api/api';

// 萤石云直播API返回数据类型
interface LiveAddressResponse {
    status: number;
    msg: string;
    data: {
        id: string;
        url: string; // 直播流地址 (m3u8格式)
        expireTime: string;
    };
}

// API返回的原始数据类型
interface TodoApiResponse {
    id: number;
    title: string;
    submitter: string;
    account: string;
    status: number; // 0:待审批 1:已同意 2：已拒绝
    dynamicFields: string; // JSON字符串
    refuse: string | null;
    createdAt: string;
    updateAt: string;
    approvalAt: string | null;
    correlationId: number;
    approver: string;
    approverAccount: string;
    ids: string; // JSON字符串
    type: number; // 1-待办 2-通知
    todoType: number; // 0开孔申请 1验孔申请 2封孔提醒 等
    drillParams: string; // JSON字符串
    deviceCode: string; // 设备序列号
    imageUrls: string | null;
    videoUrls: string | null;
    signUrl: string | null; // 签名URL
}

// 钻孔参数类型
interface DrillParams {
    设计参数: {
        孔深: string;
        方位角: string;
        开孔角度: string;
        开孔高度: string;
        见岩距离: string;
        见煤距离: string;
    };
    实际参数: {
        孔深: string;
        方位角: string;
        开孔角度: string;
        开孔高度: string;
        见岩距离: string;
        见煤距离: string;
    };
    偏差值: {
        孔深: string;
        方位角: string;
        开孔角度: string;
        开孔高度: string;
        见岩距离: string;
        见煤距离: string;
    };
}

// 动态字段类型
interface DynamicField {
    title: string;
    value: string;
}

// 组件内部使用的数据类型
interface TodoDetailData {
    id: number;
    title: string;                      // 标题
    submitter: string;                  // 提交人
    drillName: string;                  // 钻机名称
    deviceCode: string;                 // 设备code（用于获取直播地址）
    holeNumber: string;                 // 孔号
    planHoleAngle: number;              // 计划开孔角度
    actualHoleAngle: number;            // 实际开孔角度
    planAzimuth: number;                // 计划开孔方位角
    actualAzimuth: number;              // 实际开孔方位角
    planHeight: number;                 // 计划开孔高度
    actualHeight: number;               // 实际开孔高度
    planCoalDistance: number;           // 计划见煤距离
    actualCoalDistance: number;         // 实际见煤距离
    planRockDistance: number;           // 计划见岩距离
    actualRockDistance: number;         // 实际见岩距离
    planDepth: number;                  // 计划孔深
    actualDepth: number;                // 实际孔深
    createTime: string;                 // 创建时间
    updateTime: string;                 // 修改时间
    images: string[];                   // 图片列表
    videos: string[];                   // 视频列表
    capturedImages: string[];           // 抓拍图片列表
    capturedVideos: string[];           // 抓拍视频列表
    approvalSteps: ApprovalStep[];      // 审批流程节点
    status: 'pending' | 'approved' | 'rejected'; // 状态
}

// 审批人信息类型
interface ApprovalUser {
    name: string;
    read: number; // 0-未读 1-已读
    account: string;
}

// 审批节点数据类型
interface ApprovalStep {
    id: number;
    stepName: string;                   // 节点名称
    approver: string;                   // 审批人
    status: 'pending' | 'approved' | 'rejected'; // 审批状态
    isRead: boolean;                    // 是否已读
    approvalTime?: string;              // 审批时间
    comment?: string;                   // 审批意见
    signature?: string;                 // 签字图片
    createTime?: string;                // 创建时间（用于计算等待时间）
    approvalUsers?: ApprovalUser[];     // 审批人列表（用于多人审批步骤）
}

interface TodoDetailProps {
    visible: boolean;
    onClose: () => void;
    todoId?: number;
    onApprovalSuccess?: () => void; // 新增：审批成功后的回调函数
}

const TodoDetail: React.FC<TodoDetailProps> = ({ visible, onClose, todoId, onApprovalSuccess }) => {
    const [loading, setLoading] = useState(false);
    const [todoData, setTodoData] = useState<TodoDetailData | null>(null);

    // 直播相关状态
    const [ezOpenUrl, setEzOpenUrl] = useState<string>('');
    const [liveLoading, setLiveLoading] = useState(false);
    const [liveError, setLiveError] = useState<string | null>(null);



    /**
     * 将base64字符串转换为File对象
     * @param base64String base64字符串 (包含data:image/jpeg;base64,前缀)
     * @param fileName 文件名
     * @returns File对象
     */
    const base64ToFile = (base64String: string, fileName: string): File => {
        // 移除data:image/jpeg;base64,前缀
        const base64Data = base64String.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        const mimeType = base64String.split(',')[0].split(':')[1].split(';')[0];

        return new File([byteArray], fileName, { type: mimeType });
    };

    // 获取萤石云直播地址
    const fetchLiveAddress = useCallback(async (deviceCode: string) => {
        try {
            setLiveLoading(true);
            setLiveError(null);
            setEzOpenUrl('');

            console.log('调用萤石云直播接口，设备code:', deviceCode);

            // 调用萤石云直播API (POST请求，deviceCode作为query参数)
            const response = await postRequest(`/api/ys/get_live_address?deviceCode=${deviceCode}`, {}) as LiveAddressResponse;
            console.log('萤石云直播API响应:', response);

            if (response && response.status === 0 && response.data?.url) {
                const liveUrl = response.data.url;
                console.log('获取到萤石云直播地址:', liveUrl);

                // 将m3u8 URL转换为ezopen协议URL
                const ezOpenLiveUrl = liveUrl.replace('https://open.ys7.com/v3/openlive/', 'ezopen://open.ys7.com/');
                console.log('转换后的ezopen直播URL:', ezOpenLiveUrl);

                setEzOpenUrl(ezOpenLiveUrl);
            } else {
                const errorMsg = response?.msg || '获取直播地址失败';
                throw new Error(errorMsg);
            }
        } catch (err) {
            console.error('获取萤石云直播地址失败:', err);
            setLiveError(err instanceof Error ? err.message : '获取直播地址失败');
        } finally {
            setLiveLoading(false);
        }
    }, []);

    // 萤石播放器抓拍回调 - 使用useCallback稳定函数引用
    const handleEzCapture = useCallback(async (type: 'image' | 'video', eventData: any) => {
        console.log('萤石播放器抓拍数据:', type, eventData);

        try {
            if (type === 'image') {
                // 处理萤石播放器抓拍图片数据
                // 数据格式: {eventType: 'capturePicture', code: 0, data: {fileName: '...', base64: 'data:image/jpeg;base64,...', currentOSD: ...}}

                if (eventData && eventData.data && eventData.data.base64) {
                    const imageUrl = eventData.data.base64;
                    const fileName = eventData.data.fileName || `capture_${Date.now()}.jpg`;

                    // 先添加到界面显示
                    setTodoData(prevData => {
                        if (!prevData) return prevData;
                        return {
                            ...prevData,
                            capturedImages: [...prevData.capturedImages, imageUrl]
                        };
                    });

                    message.success(`图片抓拍成功 (${fileName})`);

                    // 异步上传图片
                    try {
                        console.log('开始上传抓拍图片...');
                        const imageFile = base64ToFile(imageUrl, fileName);
                        const uploadResponse = await uploadCaptureImage(imageFile);
                        console.log('图片上传成功:', uploadResponse);

                        // 保存上传后的URL
                        if (uploadResponse && uploadResponse.data) {
                            // data字段直接是URL字符串
                            setUploadedImageUrls(prev => [...prev, uploadResponse.data]);
                        }

                        message.success('图片上传成功');
                    } catch (uploadError) {
                        console.error('图片上传失败:', uploadError);
                        message.error('图片上传失败，请稍后重试');
                    }
                } else {
                    console.warn('萤石播放器抓拍数据格式异常:', eventData);
                    message.error('图片抓拍失败，数据格式异常');
                }

            } else if (type === 'video') {
                // 处理萤石播放器录像数据
                // 数据格式: {eventType: 'stopSave', code: 0, data: {file: Blob, url: 'blob:...'}}

                if (eventData && eventData.data) {
                    let videoUrl = '';
                    let videoFile: File | null = null;

                    if (eventData.data.url && eventData.data.url.startsWith('blob:')) {
                        // 使用萤石播放器提供的blob URL
                        videoUrl = eventData.data.url;

                        // 如果有Blob文件，准备上传
                        if (eventData.data.file && eventData.data.file instanceof Blob) {
                            const fileName = `capture_video_${Date.now()}.mp4`;
                            videoFile = new File([eventData.data.file], fileName, { type: 'video/mp4' });
                        }
                    } else if (eventData.data.file && eventData.data.file instanceof Blob) {
                        // 如果有Blob文件，创建URL
                        videoUrl = URL.createObjectURL(eventData.data.file);
                        const fileName = `capture_video_${Date.now()}.mp4`;
                        videoFile = new File([eventData.data.file], fileName, { type: 'video/mp4' });
                    }

                    if (videoUrl) {
                        // 先添加到界面显示
                        setTodoData(prevData => {
                            if (!prevData) return prevData;
                            return {
                                ...prevData,
                                capturedVideos: [...prevData.capturedVideos, videoUrl]
                            };
                        });

                        message.success('视频录制成功');

                        // 异步上传视频
                        if (videoFile) {
                            try {
                                console.log('开始上传抓拍视频...');
                                const uploadResponse = await uploadCaptureVideo(videoFile);
                                console.log('视频上传成功:', uploadResponse);

                                // 保存上传后的URL
                                if (uploadResponse && uploadResponse.data) {
                                    // data字段直接是URL字符串
                                    setUploadedVideoUrls(prev => [...prev, uploadResponse.data]);
                                }

                                message.success('视频上传成功');
                            } catch (uploadError) {
                                console.error('视频上传失败:', uploadError);
                                message.error('视频上传失败，请稍后重试');
                            }
                        }
                    } else {
                        console.warn('萤石播放器录像数据格式异常:', eventData);
                        message.error('视频录制失败，数据格式异常');
                    }
                } else {
                    console.warn('萤石播放器录像数据为空:', eventData);
                    message.error('视频录制失败，未获取到数据');
                }
            }
        } catch (error) {
            console.error('处理萤石播放器抓拍数据失败:', error);
            message.error('抓拍处理失败，请重试');
        }
    }, []); // 空依赖数组，函数引用保持稳定

    // 计算等待时间
    const calculateWaitingTime = (createTime: string): string => {
        const now = new Date();
        const created = new Date(createTime);
        const diffMs = now.getTime() - created.getTime();

        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    };



    // 删除抓拍图片
    const handleDeleteImage = useCallback((index: number) => {
        setTodoData(prevData => {
            if (!prevData) return prevData;
            const newImages = [...prevData.capturedImages];
            newImages.splice(index, 1);
            return {
                ...prevData,
                capturedImages: newImages
            };
        });
        message.success('图片删除成功');
    }, []);

    // 删除抓拍视频
    const handleDeleteVideo = useCallback((index: number) => {
        setTodoData(prevData => {
            if (!prevData) return prevData;
            const newVideos = [...prevData.capturedVideos];
            // 如果是blob URL，需要释放内存
            const videoUrl = newVideos[index];
            if (videoUrl && videoUrl.startsWith('blob:')) {
                URL.revokeObjectURL(videoUrl);
            }
            newVideos.splice(index, 1);
            return {
                ...prevData,
                capturedVideos: newVideos
            };
        });
        message.success('视频删除成功');
    }, []);

    const [signatureModalVisible, setSignatureModalVisible] = useState(false);
    const [currentStepId, setCurrentStepId] = useState<number | null>(null);

    // 签名画板引用
    const signatureRef = useRef<SignatureCanvas>(null);

    // 上传的文件URL列表
    const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
    const [uploadedVideoUrls, setUploadedVideoUrls] = useState<string[]>([]);
    const [uploadedSignUrl, setUploadedSignUrl] = useState<string>('');
    // 签名显示状态
    const [currentSignature, setCurrentSignature] = useState<string>('');



    // 获取待办详情数据
    const fetchTodoDetail = async () => {
        if (!todoId) return;

        setLoading(true);
        try {
            console.log('开始获取待办详情，todoId:', todoId);

            // 调用真实的API接口
            const response = await getRequest(`/todo/get_info?id=${todoId}`) as {
                status: number;
                msg: string;
                data: TodoApiResponse;
            };
            console.log('待办详情API响应:', response);

            // 打印完整的响应数据供调试
            console.log('=== 待办详情完整数据 ===');
            console.log('响应状态:', response.status);
            console.log('响应消息:', response.msg);
            console.log('响应数据:', JSON.stringify(response.data, null, 2));
            console.log('========================');

            if (response.status === 0 && response.data) {
                const apiData = response.data;

                // 解析动态字段
                let dynamicFields: DynamicField[] = [];
                try {
                    dynamicFields = JSON.parse(apiData.dynamicFields);
                } catch (error) {
                    console.error('解析动态字段失败:', error);
                }

                // 解析钻孔参数
                let drillParams: DrillParams | null = null;
                try {
                    drillParams = JSON.parse(apiData.drillParams);
                } catch (error) {
                    console.error('解析钻孔参数失败:', error);
                }

                // 解析审批人信息
                let approvalUsers: ApprovalUser[] = [];
                try {
                    approvalUsers = JSON.parse(apiData.ids);
                    console.log('解析的审批人信息:', approvalUsers);
                } catch (error) {
                    console.error('解析审批人信息失败:', error);
                }

                // 从动态字段中提取信息
                const getFieldValue = (title: string): string => {
                    const field = dynamicFields.find(f => f.title === title);
                    return field ? field.value : '';
                };

                // 安全转换数值，处理空值和无效值
                const safeParseFloat = (value: string | null | undefined): number => {
                    if (!value || value.trim() === '' || value === 'null' || value === 'undefined') {
                        return NaN; // 返回NaN，在渲染时会被处理为空字符串
                    }
                    const parsed = parseFloat(value);
                    return isNaN(parsed) ? NaN : parsed;
                };

                // 转换为组件需要的数据格式
                const todoData: TodoDetailData = {
                    id: apiData.id,
                    title: apiData.title,
                    submitter: apiData.submitter,
                    drillName: getFieldValue('钻机名称') || '未知钻机',
                    deviceCode: apiData.deviceCode,
                    holeNumber: getFieldValue('孔号') || '未知孔号',
                    // 从钻孔参数中提取数据，使用安全转换函数处理空值
                    planHoleAngle: drillParams ? safeParseFloat(drillParams.设计参数.开孔角度) : NaN,
                    actualHoleAngle: drillParams ? safeParseFloat(drillParams.实际参数.开孔角度) : NaN,
                    planAzimuth: drillParams ? safeParseFloat(drillParams.设计参数.方位角) : NaN,
                    actualAzimuth: drillParams ? safeParseFloat(drillParams.实际参数.方位角) : NaN,
                    planHeight: drillParams ? safeParseFloat(drillParams.设计参数.开孔高度) : NaN,
                    actualHeight: drillParams ? safeParseFloat(drillParams.实际参数.开孔高度) : NaN,
                    planCoalDistance: drillParams ? safeParseFloat(drillParams.设计参数.见煤距离) : NaN,
                    actualCoalDistance: drillParams ? safeParseFloat(drillParams.实际参数.见煤距离) : NaN,
                    planRockDistance: drillParams ? safeParseFloat(drillParams.设计参数.见岩距离) : NaN,
                    actualRockDistance: drillParams ? safeParseFloat(drillParams.实际参数.见岩距离) : NaN,
                    planDepth: drillParams ? safeParseFloat(drillParams.设计参数.孔深) : NaN,
                    actualDepth: drillParams ? safeParseFloat(drillParams.实际参数.孔深) : NaN,
                    createTime: apiData.createdAt,
                    updateTime: apiData.updateAt,
                    images: apiData.imageUrls ? apiData.imageUrls.split(',').filter(url => url.trim()) : [],
                    videos: apiData.videoUrls ? apiData.videoUrls.split(',').filter(url => url.trim()) : [],
                    capturedImages: [],
                    capturedVideos: [],
                    // 根据API状态转换为组件状态
                    status: apiData.status === 0 ? 'pending' : apiData.status === 1 ? 'approved' : 'rejected',
                    // 根据真实数据构建审批流程
                    approvalSteps: [
                        // 提交申请步骤
                        {
                            id: 1,
                            stepName: '提交申请',
                            approver: apiData.submitter,
                            status: 'approved' as const, // 提交人步骤总是已完成
                            isRead: true,
                            approvalTime: apiData.createdAt,
                            comment: '提交申请',
                            createTime: apiData.createdAt,
                            approvalUsers: [] // 单个提交人
                        },
                        // 审批处理步骤 - 将所有审批人合并到一个步骤中
                        {
                            id: 2,
                            stepName: '审批处理',
                            approver: '', // 不使用单个审批人字段
                            status: (apiData.status === 0 ? 'pending' : apiData.status === 1 ? 'approved' : 'rejected') as 'pending' | 'approved' | 'rejected',
                            isRead: approvalUsers.some(user => user.read === 1), // 如果有人已读则标记为已读
                            approvalTime: apiData.status !== 0 ? (apiData.approvalAt || undefined) : undefined,
                            comment: apiData.status === 2 ? (apiData.refuse || '已拒绝') : apiData.status === 1 ? '审批通过' : '',
                            signature: '',
                            createTime: apiData.createdAt,
                            approvalUsers: approvalUsers // 存储所有审批人信息
                        },
                        // 流程完成步骤
                        {
                            id: 3,
                            stepName: '流程完成',
                            approver: '',
                            status: (apiData.status === 1 ? 'approved' : 'pending') as 'pending' | 'approved',
                            isRead: apiData.status === 1,
                            approvalTime: apiData.status === 1 ? (apiData.approvalAt || undefined) : undefined,
                            comment: apiData.status === 1 ? '流程已完成' : '',
                            signature: '',
                            createTime: apiData.createdAt,
                            approvalUsers: [] // 流程完成无审批人
                        }
                    ]
                };
                setTodoData(todoData);

                // 如果有签名URL，显示签名
                if (apiData.signUrl) {
                    setCurrentSignature(apiData.signUrl);
                }

                // 如果有设备code，获取直播地址
                if (todoData.deviceCode) {
                    fetchLiveAddress(todoData.deviceCode);
                }
            } else {
                console.error('API返回错误:', response.msg);
                message.error(response.msg || '获取待办详情失败');
            }
        } catch (error) {
            console.error('获取待办详情出错:', error);
            message.error('获取待办详情失败');
        } finally {
            setLoading(false);
        }
    };



    // 标记为已读 - 进入详情页时自动调用
    const markAsRead = async () => {
        if (!todoId) return;

        try {
            // 这里可以调用API来标记为已读
            // await postRequest('/todo/mark_read', { id: todoId });
            console.log('标记待办为已读:', todoId);

            // 更新本地状态
            if (todoData) {
                const updatedSteps = todoData.approvalSteps.map(step => ({
                    ...step,
                    isRead: true // 进入详情页后标记所有相关步骤为已读
                }));

                setTodoData({
                    ...todoData,
                    approvalSteps: updatedSteps
                });
            }
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    };

    // 审批操作
    const handleApproval = async (stepId: number, approved: boolean) => {
        setCurrentStepId(stepId);
        if (approved) {
            setSignatureModalVisible(true);
        } else {
            // 拒绝操作
            if (!todoData) {
                message.error('待办数据未加载');
                return;
            }

            try {
                console.log('开始调用拒绝审批接口...');
                const approvalParams = {
                    id: todoData.id,
                    action: 2, // 2：拒绝
                    refuse: '审批拒绝', // 拒绝原因
                    imageUrls: uploadedImageUrls,
                    videoUrls: uploadedVideoUrls,
                    signUrl: uploadedSignUrl || ''
                };

                const approvalResponse = await todoApproval(approvalParams);
                console.log('拒绝审批接口调用成功:', approvalResponse);

                // 更新本地审批状态
                updateApprovalStatus(stepId, 'rejected', '');

                message.success('审批拒绝成功');

                // 审批成功后关闭抽屉并刷新列表
                setTimeout(() => {
                    onClose();
                    // 调用回调函数刷新待办列表
                    if (onApprovalSuccess) {
                        onApprovalSuccess();
                    }
                }, 1500); // 延迟1.5秒关闭，让用户看到成功提示
            } catch (error) {
                console.error('拒绝审批操作失败:', error);
                message.error('拒绝审批操作失败，请重试');
            }
        }
    };

    // 更新审批状态
    const updateApprovalStatus = (stepId: number, status: 'approved' | 'rejected', signature: string) => {
        if (!todoData) return;

        const updatedSteps = todoData.approvalSteps.map(step =>
            step.id === stepId ? {
                ...step,
                status,
                isRead: true,
                approvalTime: new Date().toLocaleString(),
                comment: status === 'approved' ? '审批通过' : '审批拒绝',
                signature
            } : step
        );

        setTodoData({
            ...todoData,
            approvalSteps: updatedSteps
        });

        message.success(`${status === 'approved' ? '审批通过' : '审批拒绝'}成功`);
        setSignatureModalVisible(false);
        setCurrentStepId(null);
    };

    // 清空签名画板
    const clearSignature = () => {
        if (signatureRef.current) {
            signatureRef.current.clear();
        }
    };

    // 签字确认
    const handleSignatureConfirm = async () => {
        if (!signatureRef.current) {
            message.error('签名画板未初始化');
            return;
        }

        if (signatureRef.current.isEmpty()) {
            message.error('请先进行签名');
            return;
        }

        if (!todoData) {
            message.error('待办数据未加载');
            return;
        }

        try {
            // 获取签名的base64数据
            const signatureData = signatureRef.current.toDataURL();

            // 将base64签名转换为File对象
            const fileName = `signature_${Date.now()}.png`;
            const signatureFile = base64ToFile(signatureData, fileName);

            // 上传签名图片到服务器
            console.log('开始上传签名图片...');
            const uploadResponse = await uploadCaptureImage(signatureFile);
            console.log('签名图片上传成功:', uploadResponse);

            // 获取签名URL
            let signUrl = '';
            if (uploadResponse && uploadResponse.data) {
                // data字段直接是URL字符串
                signUrl = uploadResponse.data;
                setUploadedSignUrl(signUrl);
            }

            // 调用审批接口
            console.log('开始调用审批接口...');
            const approvalParams = {
                id: todoData.id,
                action: 1, // 1：同意
                imageUrls: uploadedImageUrls,
                videoUrls: uploadedVideoUrls,
                signUrl: signUrl
            };

            const approvalResponse = await todoApproval(approvalParams);
            console.log('审批接口调用成功:', approvalResponse);

            // 更新本地审批状态
            if (currentStepId) {
                updateApprovalStatus(currentStepId, 'approved', signatureData);
            }

            // 保存签名到右下角显示
            setCurrentSignature(signatureData);

            message.success('审批成功');

            // 审批成功后关闭抽屉并刷新列表
            setTimeout(() => {
                onClose();
                // 调用回调函数刷新待办列表
                if (onApprovalSuccess) {
                    onApprovalSuccess();
                }
            }, 1500); // 延迟1.5秒关闭，让用户看到成功提示
        } catch (error) {
            console.error('审批操作失败:', error);
            message.error('审批操作失败，请重试');
        }
    };

    useEffect(() => {
        if (visible && todoId) {
            fetchTodoDetail();
            // 进入详情页时自动标记为已读
            markAsRead();
        } else if (!visible) {
            // 关闭抽屉时清除签名显示
            setCurrentSignature('');
        }
    }, [visible, todoId]);

    // 渲染对比项
    const renderComparisonItem = (label: string, planValue: number, actualValue: number, unit: string) => {
        // 处理空值或NaN的情况
        const safePlanValue = (planValue === null || planValue === undefined || isNaN(planValue)) ? '' : planValue;
        const safeActualValue = (actualValue === null || actualValue === undefined || isNaN(actualValue)) ? '' : actualValue;

        // 计算偏差显示
        let deviationDisplay = '';
        let deviationColor = '#999';

        if (safePlanValue !== '' && safeActualValue !== '' && !isNaN(Number(safePlanValue)) && !isNaN(Number(safeActualValue))) {
            // 两个值都存在且为数字时，计算偏差
            const deviation = Number(safeActualValue) - Number(safePlanValue);
            deviationColor = Math.abs(deviation) > 2 ? '#ff4d4f' : Math.abs(deviation) > 1 ? '#fa8c16' : '#52c41a';
            deviationDisplay = `${deviation > 0 ? '+' : ''}${deviation.toFixed(1)}${unit}`;
        } else {
            // 有任一值为空时，偏差显示为空
            deviationDisplay = '';
        }

        return (
            <Descriptions.Item label={label} span={3}>
                <Space direction="vertical" size={0}>
                    <div>计划值: {safePlanValue}{safePlanValue !== '' ? unit : ''}</div>
                    <div>实际值: {safeActualValue}{safeActualValue !== '' ? unit : ''}</div>
                    <div style={{ color: deviationColor }}>
                        偏差: {deviationDisplay}
                    </div>
                </Space>
            </Descriptions.Item>
        );
    };



    return (
        <>
            <Drawer
                title="待办详情"
                placement="right"
                width={800}
                onClose={onClose}
                open={visible}
                loading={loading}
            >
                {todoData && (
                    <div style={{ padding: '0 8px' }}>
                        {/* 基本信息 */}
                        <Card
                            title="基本信息"
                            style={{ marginBottom: 16 }}
                            extra={
                                <div style={{
                                    fontSize: '16px',
                                    fontWeight: 'normal',
                                    lineHeight: '28px',
                                    color: todoData.status === 'approved' ? '#49AA19' :
                                        todoData.status === 'rejected' ? '#ff4d4f' : '#D89614'
                                }}>
                                    {todoData.status === 'approved' ? '已完成' :
                                        todoData.status === 'rejected' ? '已拒绝' : '审批中'}
                                </div>
                            }
                        >
                            <Descriptions column={2} size="small">
                                <Descriptions.Item label="标题" span={2}>{todoData.title}</Descriptions.Item>
                                <Descriptions.Item label="钻机名称">{todoData.drillName}</Descriptions.Item>
                                <Descriptions.Item label="孔号">{todoData.holeNumber}</Descriptions.Item>
                                <Descriptions.Item label="创建时间">{todoData.createTime}</Descriptions.Item>
                                <Descriptions.Item label="修改时间">{todoData.updateTime}</Descriptions.Item>
                            </Descriptions>
                        </Card>

                        {/* 施工参数对比 */}
                        <Card title="施工参数对比" style={{ marginBottom: 16 }}>
                            <Descriptions column={1} size="small" bordered>
                                {renderComparisonItem('开孔角度', todoData.planHoleAngle, todoData.actualHoleAngle, '°')}
                                {renderComparisonItem('开孔方位角', todoData.planAzimuth, todoData.actualAzimuth, '°')}
                                {renderComparisonItem('开孔高度', todoData.planHeight, todoData.actualHeight, 'm')}
                                {renderComparisonItem('见煤距离', todoData.planCoalDistance, todoData.actualCoalDistance, 'm')}
                                {renderComparisonItem('见岩距离', todoData.planRockDistance, todoData.actualRockDistance, 'm')}
                                {renderComparisonItem('孔深', todoData.planDepth, todoData.actualDepth, 'm')}
                            </Descriptions>
                        </Card>

                        {/* 摄像头抓拍 */}
                        <Card title="摄像头抓拍" style={{ marginBottom: 16 }}>
                            {liveLoading && (
                                <div style={{
                                    width: '100%',
                                    height: '300px',
                                    backgroundColor: '#f0f0f0',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '16px',
                                    color: '#666',
                                    borderRadius: '8px'
                                }}>
                                    正在获取直播地址...
                                </div>
                            )}

                            {liveError && !liveLoading && (
                                <div style={{
                                    width: '100%',
                                    height: '300px',
                                    backgroundColor: '#f5f5f5',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexDirection: 'column',
                                    fontSize: '16px',
                                    color: '#ff4d4f',
                                    borderRadius: '8px'
                                }}>
                                    <div>直播加载失败</div>
                                    <div style={{ fontSize: '14px', marginTop: '8px', color: '#999' }}>
                                        {liveError}
                                    </div>
                                </div>
                            )}

                            {!liveLoading && !liveError && ezOpenUrl && (
                                <EzVideoPlayer
                                    key={ezOpenUrl} // 强制重新创建组件
                                    ezOpenUrl={ezOpenUrl}
                                    width="100%"
                                    height="300px"
                                    style={{ borderRadius: '8px' }}
                                    onCapture={handleEzCapture}
                                    isLive={true} // 启用直播模式，隐藏日期选择器
                                />
                            )}

                            {!liveLoading && !liveError && !ezOpenUrl && (
                                <div style={{
                                    width: '100%',
                                    height: '300px',
                                    backgroundColor: '#f9f9f9',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '16px',
                                    color: '#999',
                                    borderRadius: '8px'
                                }}>
                                    暂无直播数据
                                </div>
                            )}
                            <div style={{ marginTop: 8, padding: '8px', backgroundColor: '#f6f8fa', borderRadius: '4px' }}>
                                <div style={{ color: '#666', fontSize: '14px', marginBottom: '4px' }}>
                                    <strong>使用说明：</strong>
                                </div>
                                <div style={{ color: '#666', fontSize: '12px', lineHeight: '1.5' }}>
                                    • 使用播放器右键菜单或工具栏进行抓拍图片和录像<br />
                                    • 抓拍的图片和视频会自动显示在下方对应区域<br />
                                    • 如果文件保存到本地，系统会提示文件路径，请手动上传
                                </div>
                            </div>
                        </Card>

                        {/* 抓拍图片 */}
                        <Card title="抓拍图片" style={{ marginBottom: 16 }}>
                            <Image.PreviewGroup>
                                <Row gutter={[8, 8]}>
                                    {todoData.images.map((img, index) => (
                                        <Col span={8} key={index}>
                                            <Image
                                                width="100%"
                                                height={80}
                                                src={img}
                                                style={{ objectFit: 'cover', borderRadius: 4 }}
                                            />
                                        </Col>
                                    ))}
                                    {todoData.capturedImages.map((img, index) => (
                                        <Col span={8} key={`captured-${index}`}>
                                            <div style={{ position: 'relative' }}>
                                                <Image
                                                    width="100%"
                                                    height={80}
                                                    src={img}
                                                    style={{
                                                        objectFit: 'cover',
                                                        borderRadius: 4,
                                                        border: '2px solid #1890ff'
                                                    }}
                                                />
                                                <Button
                                                    type="primary"
                                                    danger
                                                    size="small"
                                                    icon={<DeleteOutlined />}
                                                    style={{
                                                        position: 'absolute',
                                                        top: 4,
                                                        right: 4,
                                                        minWidth: 'auto',
                                                        width: 24,
                                                        height: 24,
                                                        padding: 0,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center'
                                                    }}
                                                    onClick={() => handleDeleteImage(index)}
                                                    title="删除图片"
                                                />
                                            </div>
                                        </Col>
                                    ))}
                                </Row>
                            </Image.PreviewGroup>
                        </Card>

                        {/* 抓拍视频 */}
                        <Card title="抓拍视频" style={{ marginBottom: 16 }}>
                            <Row gutter={[8, 8]}>
                                {todoData.videos.map((video, index) => (
                                    <Col span={12} key={index}>
                                        <video
                                            width="100%"
                                            height={120}
                                            controls
                                            style={{ borderRadius: 4 }}
                                        >
                                            <source src={video} type="video/mp4" />
                                            您的浏览器不支持视频播放
                                        </video>
                                    </Col>
                                ))}
                                {todoData.capturedVideos.map((video, index) => (
                                    <Col span={12} key={`captured-${index}`}>
                                        <div style={{ position: 'relative' }}>
                                            <video
                                                width="100%"
                                                height={120}
                                                controls
                                                style={{
                                                    borderRadius: 4,
                                                    border: '2px solid #1890ff'
                                                }}
                                            >
                                                <source src={video} type="video/mp4" />
                                                您的浏览器不支持视频播放
                                            </video>
                                            <Button
                                                type="primary"
                                                danger
                                                size="small"
                                                icon={<DeleteOutlined />}
                                                style={{
                                                    position: 'absolute',
                                                    top: 4,
                                                    right: 4,
                                                    minWidth: 'auto',
                                                    width: 24,
                                                    height: 24,
                                                    padding: 0,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center'
                                                }}
                                                onClick={() => handleDeleteVideo(index)}
                                                title="删除视频"
                                            />
                                        </div>
                                    </Col>
                                ))}
                            </Row>
                        </Card>

                        {/* 审批流程 */}
                        <Card title="流程" style={{ backgroundColor: '#1f1f1f', border: 'none' }}>
                            <style>
                                {`
                                    .custom-steps .ant-steps-item:not(:last-child) .ant-steps-item-content {
                                        min-height: 73px;
                                    }
                                    .custom-steps .ant-steps-item-tail {
                                        height: 73px;
                                    }
                                `}
                            </style>
                            <Steps
                                direction="vertical"
                                size="small"
                                className="custom-steps"
                                items={todoData.approvalSteps.map((step) => ({
                                    title: (
                                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                                <span style={{ color: '#fff', fontSize: '14px' }}>{step.stepName}</span>
                                                {/* 如果是审批处理步骤且有多个审批人，则显示审批人列表 */}
                                                {step.stepName === '审批处理' && step.approvalUsers && step.approvalUsers.length > 0 ? (
                                                    <div style={{
                                                        display: 'flex',
                                                        flexWrap: 'wrap',
                                                        gap: '8px',
                                                        alignItems: 'center',
                                                        marginLeft: '8px'
                                                    }}>
                                                        {step.approvalUsers.map((user, index) => (
                                                            <div key={index} style={{
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                gap: '4px'
                                                            }}>
                                                                <span style={{ fontSize: '12px', color: '#999' }}>{user.name}</span>
                                                                <span style={{
                                                                    fontSize: '12px',
                                                                    color: user.read === 1 ? '#49AA19' : '#D89614'
                                                                }}>
                                                                    {user.read === 1 ? '已读' : '未读'}
                                                                </span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    /* 单个审批人显示（提交申请步骤） */
                                                    step.approver && (
                                                        <span style={{ fontSize: '12px', color: '#999' }}>{step.approver}</span>
                                                    )
                                                )}
                                            </div>
                                            <div style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>
                                                {step.id === 1 && todoData.createTime}
                                                {step.stepName === '流程完成' && step.status === 'approved' && step.approvalTime}
                                            </div>
                                        </div>
                                    ),
                                    description: (
                                        <div>
                                            {step.stepName === '审批处理' && step.status === 'pending' && (
                                                <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
                                                    {step.approvalUsers && step.approvalUsers.length > 0 ? (
                                                        <span>
                                                            审批中，已等待{step.createTime ? calculateWaitingTime(step.createTime) : '1小时15分钟'}
                                                        </span>
                                                    ) : (
                                                        <span>
                                                            审批中，已等待{step.createTime ? calculateWaitingTime(step.createTime) : '1小时15分钟'}
                                                        </span>
                                                    )}
                                                </div>
                                            )}
                                            {step.stepName === '审批处理' && step.status === 'approved' && (
                                                <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
                                                    <span>已同意</span>
                                                </div>
                                            )}
                                            {step.stepName === '审批处理' && step.status === 'rejected' && (
                                                <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
                                                    <span>已拒绝</span>
                                                </div>
                                            )}
                                        </div>
                                    ),
                                    status: step.status === 'approved' ? 'finish' :
                                        step.status === 'rejected' ? 'error' :
                                            step.id === 2 ? 'process' : 'wait'
                                }))}
                            />
                            {/* 底部按钮 - 只在待审批状态时显示 */}
                            {todoData.status === 'pending' && (
                                <div style={{
                                    display: 'flex',
                                    justifyContent: 'flex-end',
                                    gap: '12px',
                                    marginTop: '24px',
                                    paddingTop: '16px',
                                    borderTop: '1px solid #333'
                                }}>
                                    <Button
                                        style={{
                                            backgroundColor: 'transparent',
                                            borderColor: '#666',
                                            color: '#999'
                                        }}
                                        onClick={() => handleApproval(2, false)}
                                    >
                                        拒绝
                                    </Button>
                                    <Button
                                        type="primary"
                                        style={{
                                            backgroundColor: '#1890ff',
                                            borderColor: '#1890ff'
                                        }}
                                        onClick={() => handleApproval(2, true)}
                                    >
                                        同意
                                    </Button>
                                </div>
                            )}
                        </Card>
                    </div>
                )}

                {/* 右下角签名显示 */}
                {currentSignature && (
                    <div style={{
                        position: 'fixed',
                        bottom: '20px',
                        right: '20px',
                        zIndex: 1000,
                        backgroundColor: '#fff',
                        border: '2px solid #d9d9d9',
                        borderRadius: '8px',
                        padding: '12px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                        maxWidth: '200px',
                        maxHeight: '120px'
                    }}>
                        <div style={{
                            fontSize: '12px',
                            color: '#666',
                            marginBottom: '8px',
                            textAlign: 'center',
                            fontWeight: 'bold'
                        }}>
                            电子签名
                        </div>
                        <img
                            src={currentSignature}
                            alt="电子签名"
                            style={{
                                width: '100%',
                                height: 'auto',
                                maxHeight: '80px',
                                objectFit: 'contain',
                                border: '1px solid #f0f0f0',
                                borderRadius: '4px'
                            }}
                        />
                        <div style={{
                            fontSize: '10px',
                            color: '#999',
                            textAlign: 'center',
                            marginTop: '4px'
                        }}>
                            {new Date().toLocaleString()}
                        </div>
                    </div>
                )}
            </Drawer>



            {/* 签字确认弹窗 */}
            <Modal
                title="电子签名"
                open={signatureModalVisible}
                onCancel={() => setSignatureModalVisible(false)}
                onOk={handleSignatureConfirm}
                width={600}
                footer={[
                    <Button key="clear" onClick={clearSignature}>
                        清空
                    </Button>,
                    <Button key="cancel" onClick={() => setSignatureModalVisible(false)}>
                        取消
                    </Button>,
                    <Button key="confirm" type="primary" onClick={handleSignatureConfirm}>
                        确认签名
                    </Button>
                ]}
            >
                <div style={{ textAlign: 'center' }}>
                    <div style={{ marginBottom: 16, color: '#666' }}>
                        请在下方画板中进行签名：
                    </div>
                    <div style={{
                        border: '2px dashed #d9d9d9',
                        borderRadius: '8px',
                        padding: '16px',
                        backgroundColor: '#fafafa'
                    }}>
                        <SignatureCanvas
                            ref={signatureRef}
                            canvasProps={{
                                width: 500,
                                height: 200,
                                style: {
                                    border: '1px solid #d9d9d9',
                                    borderRadius: '4px',
                                    backgroundColor: 'white'
                                }
                            }}
                            backgroundColor="white"
                            penColor="black"
                        />
                    </div>
                    <div style={{ marginTop: 8, color: '#999', fontSize: '12px' }}>
                        提示：可以用鼠标或触摸屏进行签名
                    </div>
                </div>
            </Modal>
        </>
    );
};

export default TodoDetail;
