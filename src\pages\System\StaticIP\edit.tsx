import React, { useState, useEffect } from 'react';
import { Card, message, Spin } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { history, useSearchParams } from '@umijs/max';
import { staticIPService } from './service';
import StaticIPForm from './components/StaticIPForm';
import { StaticIPFormData } from './types';

const EditStaticIP: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [initialValues, setInitialValues] = useState<StaticIPFormData>();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');

  // 获取静态IP详情
  const fetchStaticIPDetail = async () => {
    if (!id) {
      message.error('缺少静态IP ID');
      history.push('/system/staticip');
      return;
    }

    setDataLoading(true);
    try {
      const result = await staticIPService.getDetail(Number(id));
      if (result.status === 0 && result.data) {
        setInitialValues({
          ipAddress: result.data.ipAddress,
          deviceCode: result.data.deviceCode,
          status: result.data.status,
        });
      } else {
        message.error(result.msg || '获取静态IP详情失败');
        history.push('/system/staticip');
      }
    } catch (error) {
      message.error('获取静态IP详情失败');
      history.push('/system/staticip');
    } finally {
      setDataLoading(false);
    }
  };

  // 处理表单提交
  const handleFinish = async (values: StaticIPFormData) => {
    setLoading(true);
    try {
      const result = await staticIPService.update(Number(id!), values);
      if (result.status === 0) {
        message.success('修改静态IP成功');
        history.push('/system/staticip');
      } else {
        message.error(result.msg || '修改失败');
      }
    } catch (error) {
      message.error('修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 返回列表页
  const handleBack = () => {
    history.push('/system/staticip');
  };

  useEffect(() => {
    fetchStaticIPDetail();
  }, [id]);

  if (dataLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ArrowLeftOutlined
              style={{ cursor: 'pointer' }}
              onClick={handleBack}
            />
            <span>编辑静态IP</span>
          </div>
        }
      >
        <div style={{ maxWidth: '600px' }}>
          <StaticIPForm
            initialValues={initialValues}
            onFinish={handleFinish}
            loading={loading}
          />
        </div>
      </Card>
    </div>
  );
};

export default EditStaticIP;
