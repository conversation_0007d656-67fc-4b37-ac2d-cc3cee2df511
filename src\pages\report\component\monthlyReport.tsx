import React,{useState} from 'react';
import { message,Input,Flex,DatePicker} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest,getRequest } from '@/services/api/api';
import dayjs from 'dayjs';
// 定义表格数据类型
interface TableListItem {
  id: number;
  constructionNumber: string;
  constructionName: string;
  constructionUnit: string;
  constructionPerson: string;
  constructionCrews: string;
  results:number;
  startDate:string;
  endDate:string;
  actualStarttime:string;
  actualEndtime:string;
  constructionProgress:string;
  acceptancePersonnel:string;
  acceptanceDate:string;
  remark:string;
}
  
const Construction: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage(); 
  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
   
    {
      title: '人员',
      dataIndex: 'name',
      fixed: 'left',
      ellipsis: true,
      search: false,
    },
    {
      title: '有效工作',
      dataIndex: 'effectiveHour',
      ellipsis: true,
      search: false,
    },
    {
      title: '用电量',
      dataIndex: 'useElectricity',
      ellipsis: true,
      search: false,
    },
    {
        title: '用水量',
        dataIndex: 'useWater',
        ellipsis: true,
        search: false,
    },
    {
        title: '封孔深度',
        dataIndex: 'sealingDepth',
        ellipsis: true,
        search: false,
    },
    {
        title: '水泥量',
        dataIndex: 'cementContent',
        ellipsis: true,
        search: false,
    },
    {
        title: '累计钻孔米',
        dataIndex: 'footageNum',
        ellipsis: true,
        search: false,
    },
    {
        title: '退钻钻杆根数',
        dataIndex: 'drillPipeNum',
        ellipsis: true,
        search: false,
    },
    {
        title: '终孔数量',
        dataIndex: 'finishNum',
        ellipsis: true,
        search: false,
    },
    {
        title: '',
        dataIndex: 'keyWord',
        hideInTable: true,
        renderFormItem: (schema, config, form) => {
            const label = schema.dataIndex as string;
            const status = form.getFieldValue(label);
            if (!status) {
                const today = dayjs().format('YYYY-MM');
                form.setFieldsValue({ [label]: today });
            }
            const onchange = (value: any) => {
                const dateOnly = value ? dayjs(value).format('YYYY-MM') : null;
                form.setFieldsValue({ [label]: dateOnly });
            };
            return (
                <DatePicker
                    value={status ? dayjs(status) : dayjs()}
                    onChange={onchange}
                    format="YYYY-MM"
                     picker="month"
                    style={{ width: '100%' }}
                />
            );
        },
    },
  ];

  return (
    <>
    {contextHolder}
    <ProTable<TableListItem>
      headerTitle="月报表"
      actionRef={actionRef}
      rowKey="id"
      request={async (params, sorter, filter) => {
        const { current, pageSize,keyWord} = params;
                    let postData = {
                        page: current,
                        perPage: pageSize,
                    }
                    if (keyWord) {
                        postData.keyWord = keyWord
                      } else {
                        // 如果没有 keyWord，默认使用当天日期
                        postData.keyWord = dayjs().format('YYYY-MM')
                      }
        const result = await postRequest('report/get_month_report', postData);
        const { data, status, msg } = result
        let dataSource
        let total
        if (status === 0) {
            console.log('施工列表',data);
            
            dataSource = data.items
            total = data.total
        } else {
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
        return Promise.resolve({
            data: dataSource,
            total: total,
            success: true,
        });
    }}
      columns={columns}
    //   search={false}
      // scroll={{ x: 2400 }}
    //   options={false}
     
    />
   
    </>
  );
  
};

export default Construction;

