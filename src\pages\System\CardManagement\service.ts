/**
 * 卡片管理API服务
 */
import { getRequest, postRequest } from '@/services/api/api';
import { CardItem, CardFormData, CardListParams, ApiResponse, CardListResponse } from './types';

// 卡片管理API服务
export const cardService = {
  /**
   * 获取卡片列表
   * @param params 查询参数
   * @returns Promise<ApiResponse<CardListResponse>>
   */
  async getList(params: CardListParams = {}): Promise<ApiResponse<CardListResponse>> {
    try {
      const { page = 1, perPage = 10, cardNumber, cardStatus, employeeName } = params;

      const requestParams: any = {
        page,
        perPage,
      };

      // 添加卡片编号模糊查询参数
      if (cardNumber) {
        requestParams.cardNumber = cardNumber;
      }

      // 添加卡片状态筛选参数
      if (cardStatus !== undefined) {
        requestParams.cardStatus = cardStatus;
      }

      // 添加员工姓名模糊查询参数
      if (employeeName) {
        requestParams.employeeName = employeeName;
      }

      const response = await postRequest<ApiResponse<CardListResponse>>(
        '/card/get_ls',
        requestParams,
      );
      return response;
    } catch (error) {
      console.error('获取卡片列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取卡片详情
   * @param id 卡片ID
   * @returns Promise<ApiResponse<CardItem>>
   */
  async getDetail(id: number): Promise<ApiResponse<CardItem>> {
    try {
      const response = await getRequest<ApiResponse<CardItem>>('/card/get_info', { id });
      return response;
    } catch (error) {
      console.error('获取卡片详情失败:', error);
      throw error;
    }
  },

  /**
   * 添加卡片
   * @param formData 表单数据
   * @returns Promise<ApiResponse<CardItem>>
   */
  async add(formData: CardFormData): Promise<ApiResponse<CardItem>> {
    try {
      const response = await postRequest<ApiResponse<CardItem>>('/card/post_add', formData);
      return response;
    } catch (error) {
      console.error('添加卡片失败:', error);
      throw error;
    }
  },

  /**
   * 修改卡片
   * @param id 卡片ID
   * @param formData 表单数据
   * @returns Promise<ApiResponse<CardItem>>
   */
  async update(id: number, formData: CardFormData): Promise<ApiResponse<CardItem>> {
    try {
      const requestData = {
        id,
        ...formData,
      };
      const response = await postRequest<ApiResponse<CardItem>>('/card/post_modify', requestData);
      return response;
    } catch (error) {
      console.error('修改卡片失败:', error);
      throw error;
    }
  },

  /**
   * 删除卡片
   * @param id 卡片ID
   * @returns Promise<ApiResponse<null>>
   */
  async delete(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await postRequest<ApiResponse<null>>('/card/post_del', { id });
      return response;
    } catch (error) {
      console.error('删除卡片失败:', error);
      throw error;
    }
  },

  /**
   * 绑定员工
   * @param id 卡片ID
   * @param employeeData 员工信息
   * @returns Promise<ApiResponse<CardItem>>
   */
  async bindEmployee(
    id: number,
    employeeData: {
      employeeId: number;
      employeeName: string;
      employeeAccount: string;
    },
  ): Promise<ApiResponse<CardItem>> {
    try {
      const requestData = {
        id,
        ...employeeData,
      };
      const response = await postRequest<ApiResponse<CardItem>>('/card/post_bind', requestData);
      return response;
    } catch (error) {
      console.error('绑定员工失败:', error);
      throw error;
    }
  },

  /**
   * 解绑员工
   * @param id 卡片ID
   * @returns Promise<ApiResponse<CardItem>>
   */
  async unbindEmployee(id: number): Promise<ApiResponse<CardItem>> {
    try {
      const response = await postRequest<ApiResponse<CardItem>>('/card/post_unbind', { id });
      return response;
    } catch (error) {
      console.error('解绑员工失败:', error);
      throw error;
    }
  },
};
