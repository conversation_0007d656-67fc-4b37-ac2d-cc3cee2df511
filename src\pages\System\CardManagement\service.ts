/**
 * 卡片管理API服务 - 使用假数据
 */
import { CardItem, CardFormData, CardListParams, ApiResponse, CardListResponse } from './types';

// 假数据
const mockCards: CardItem[] = [
  {
    id: 1,
    cardNumber: 'CARD001',
    cardStatus: 1,
    employeeId: 1001,
    employeeName: '张三',
    employeeAccount: 'zhang<PERSON>',
    bindTime: '2024-01-15 09:30:00',
    unbindTime: undefined,
    bindOperatorAccount: 'admin',
    bindOperatorName: '管理员',
    remark: '正常使用中',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-15 09:30:00',
  },
  {
    id: 2,
    cardNumber: 'CARD002',
    cardStatus: 2,
    employeeId: 1002,
    employeeName: '李四',
    employeeAccount: 'lisi',
    bindTime: '2024-01-12 14:20:00',
    unbindTime: '2024-01-20 16:45:00',
    bindOperatorAccount: 'admin',
    bindOperatorName: '管理员',
    remark: '卡片丢失，已解绑',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-20 16:45:00',
  },
  {
    id: 3,
    cardNumber: 'CARD003',
    cardStatus: 0,
    employeeId: undefined,
    employeeName: undefined,
    employeeAccount: undefined,
    bindTime: undefined,
    unbindTime: undefined,
    bindOperatorAccount: undefined,
    bindOperatorName: undefined,
    remark: '备用卡片，暂未分配',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-10 08:00:00',
  },
  {
    id: 4,
    cardNumber: 'CARD004',
    cardStatus: 3,
    employeeId: 1003,
    employeeName: '王五',
    employeeAccount: 'wangwu',
    bindTime: '2024-01-18 11:15:00',
    unbindTime: undefined,
    bindOperatorAccount: 'admin',
    bindOperatorName: '管理员',
    remark: '卡片损坏，需要更换',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-18 11:15:00',
  },
  {
    id: 5,
    cardNumber: 'CARD005',
    cardStatus: 1,
    employeeId: 1004,
    employeeName: '赵六',
    employeeAccount: 'zhaoliu',
    bindTime: '2024-01-22 10:00:00',
    unbindTime: undefined,
    bindOperatorAccount: 'operator1',
    bindOperatorName: '操作员1',
    remark: '新员工卡片',
    corpId: 'CORP001',
    createdAt: '2024-01-20 08:00:00',
    updatedAt: '2024-01-22 10:00:00',
  },
];

let cardIdCounter = 6;

// 卡片管理API服务 - 使用假数据
export const cardService = {
  /**
   * 获取卡片列表
   * @param params 查询参数
   * @returns Promise<ApiResponse<CardListResponse>>
   */
  async getList(params: CardListParams = {}): Promise<ApiResponse<CardListResponse>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    try {
      const { page = 1, perPage = 10, cardNumber, cardStatus, employeeName } = params;

      let filteredCards = [...mockCards];

      // 卡片编号模糊查询
      if (cardNumber) {
        filteredCards = filteredCards.filter((card) =>
          card.cardNumber.toLowerCase().includes(cardNumber.toLowerCase()),
        );
      }

      // 卡片状态筛选
      if (cardStatus !== undefined) {
        filteredCards = filteredCards.filter((card) => card.cardStatus === cardStatus);
      }

      // 员工姓名模糊查询
      if (employeeName) {
        filteredCards = filteredCards.filter(
          (card) => card.employeeName && card.employeeName.includes(employeeName),
        );
      }

      const total = filteredCards.length;
      const startIndex = (page - 1) * perPage;
      const endIndex = startIndex + perPage;
      const items = filteredCards.slice(startIndex, endIndex);

      return {
        status: 0,
        msg: 'success',
        data: {
          items,
          total,
          page: parseInt(page.toString()),
        },
      };
    } catch (error) {
      console.error('获取卡片列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取卡片详情
   * @param id 卡片ID
   * @returns Promise<ApiResponse<CardItem>>
   */
  async getDetail(id: number): Promise<ApiResponse<CardItem>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 200));

    try {
      const card = mockCards.find((item) => item.id === id);

      if (card) {
        return {
          status: 0,
          msg: 'success',
          data: card,
        };
      } else {
        return {
          status: 1,
          msg: '卡片不存在',
          data: {} as CardItem,
        };
      }
    } catch (error) {
      console.error('获取卡片详情失败:', error);
      throw error;
    }
  },

  /**
   * 添加卡片
   * @param formData 表单数据
   * @returns Promise<ApiResponse<CardItem>>
   */
  async add(formData: CardFormData): Promise<ApiResponse<CardItem>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    try {
      // 检查卡片编号是否已存在
      const existingCard = mockCards.find((card) => card.cardNumber === formData.cardNumber);
      if (existingCard) {
        return {
          status: 1,
          msg: '卡片编号已存在',
          data: {} as CardItem,
        };
      }

      const newCard: CardItem = {
        id: cardIdCounter++,
        cardNumber: formData.cardNumber,
        cardStatus: formData.cardStatus || 1,
        employeeId: formData.employeeId,
        employeeName: formData.employeeName,
        employeeAccount: formData.employeeAccount,
        bindTime: formData.employeeId
          ? new Date().toLocaleString('zh-CN', { hour12: false })
          : undefined,
        unbindTime: undefined,
        bindOperatorAccount: formData.employeeId ? 'admin' : undefined,
        bindOperatorName: formData.employeeId ? '管理员' : undefined,
        remark: formData.remark,
        corpId: 'CORP001',
        createdAt: new Date().toLocaleString('zh-CN', { hour12: false }),
        updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
      };

      mockCards.push(newCard);

      return {
        status: 0,
        msg: '添加成功',
        data: newCard,
      };
    } catch (error) {
      console.error('添加卡片失败:', error);
      throw error;
    }
  },

  /**
   * 修改卡片
   * @param id 卡片ID
   * @param formData 表单数据
   * @returns Promise<ApiResponse<CardItem>>
   */
  async update(id: number, formData: CardFormData): Promise<ApiResponse<CardItem>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    try {
      const cardIndex = mockCards.findIndex((card) => card.id === id);
      if (cardIndex === -1) {
        return {
          status: 1,
          msg: '卡片不存在',
          data: {} as CardItem,
        };
      }

      // 检查卡片编号是否与其他卡片重复
      const existingCard = mockCards.find(
        (card) => card.cardNumber === formData.cardNumber && card.id !== id,
      );
      if (existingCard) {
        return {
          status: 1,
          msg: '卡片编号已存在',
          data: {} as CardItem,
        };
      }

      const originalCard = mockCards[cardIndex];
      const updatedCard: CardItem = {
        ...originalCard,
        cardNumber: formData.cardNumber,
        cardStatus: formData.cardStatus || originalCard.cardStatus,
        employeeId: formData.employeeId,
        employeeName: formData.employeeName,
        employeeAccount: formData.employeeAccount,
        remark: formData.remark,
        updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
      };

      // 如果员工信息发生变化，更新绑定时间
      if (
        formData.employeeId &&
        (!originalCard.employeeId || originalCard.employeeId !== formData.employeeId)
      ) {
        updatedCard.bindTime = new Date().toLocaleString('zh-CN', { hour12: false });
        updatedCard.bindOperatorAccount = 'admin';
        updatedCard.bindOperatorName = '管理员';
      } else if (!formData.employeeId && originalCard.employeeId) {
        updatedCard.unbindTime = new Date().toLocaleString('zh-CN', { hour12: false });
      }

      mockCards[cardIndex] = updatedCard;

      return {
        status: 0,
        msg: '修改成功',
        data: updatedCard,
      };
    } catch (error) {
      console.error('修改卡片失败:', error);
      throw error;
    }
  },

  /**
   * 删除卡片
   * @param id 卡片ID
   * @returns Promise<ApiResponse<null>>
   */
  async delete(id: number): Promise<ApiResponse<null>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    try {
      const cardIndex = mockCards.findIndex((card) => card.id === id);
      if (cardIndex === -1) {
        return {
          status: 1,
          msg: '卡片不存在',
          data: null,
        };
      }

      mockCards.splice(cardIndex, 1);

      return {
        status: 0,
        msg: '删除成功',
        data: null,
      };
    } catch (error) {
      console.error('删除卡片失败:', error);
      throw error;
    }
  },

  /**
   * 绑定员工
   * @param id 卡片ID
   * @param employeeData 员工信息
   * @returns Promise<ApiResponse<CardItem>>
   */
  async bindEmployee(
    id: number,
    employeeData: {
      employeeId: number;
      employeeName: string;
      employeeAccount: string;
    },
  ): Promise<ApiResponse<CardItem>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 400));

    try {
      const cardIndex = mockCards.findIndex((card) => card.id === id);
      if (cardIndex === -1) {
        return {
          status: 1,
          msg: '卡片不存在',
          data: {} as CardItem,
        };
      }

      const updatedCard: CardItem = {
        ...mockCards[cardIndex],
        employeeId: employeeData.employeeId,
        employeeName: employeeData.employeeName,
        employeeAccount: employeeData.employeeAccount,
        bindTime: new Date().toLocaleString('zh-CN', { hour12: false }),
        unbindTime: undefined,
        bindOperatorAccount: 'admin',
        bindOperatorName: '管理员',
        updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
      };

      mockCards[cardIndex] = updatedCard;

      return {
        status: 0,
        msg: '绑定成功',
        data: updatedCard,
      };
    } catch (error) {
      console.error('绑定员工失败:', error);
      throw error;
    }
  },

  /**
   * 解绑员工
   * @param id 卡片ID
   * @returns Promise<ApiResponse<CardItem>>
   */
  async unbindEmployee(id: number): Promise<ApiResponse<CardItem>> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 400));

    try {
      const cardIndex = mockCards.findIndex((card) => card.id === id);
      if (cardIndex === -1) {
        return {
          status: 1,
          msg: '卡片不存在',
          data: {} as CardItem,
        };
      }

      const updatedCard: CardItem = {
        ...mockCards[cardIndex],
        employeeId: undefined,
        employeeName: undefined,
        employeeAccount: undefined,
        unbindTime: new Date().toLocaleString('zh-CN', { hour12: false }),
        bindOperatorAccount: 'admin',
        bindOperatorName: '管理员',
        updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
      };

      mockCards[cardIndex] = updatedCard;

      return {
        status: 0,
        msg: '解绑成功',
        data: updatedCard,
      };
    } catch (error) {
      console.error('解绑员工失败:', error);
      throw error;
    }
  },
};
