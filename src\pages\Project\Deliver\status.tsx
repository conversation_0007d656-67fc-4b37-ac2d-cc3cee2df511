import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable,ProForm,ProFormCascader,ProFormSelect,ProFormText } from '@ant-design/pro-components';
import { Flex, Form, Input, message,Modal,Table,Upload,Button,Tag } from 'antd';
import { useRef, useState,useEffect } from 'react';
import { getRequest, postRequest } from '@/services/api/api';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import { EllipsisOutlined, InboxOutlined } from '@ant-design/icons';
import { history } from 'umi';

interface IoTPointProps {
  number: string;
}

type TableListItem = {
  id: number;
  title: string;
  var: string;
  type: number;
  createdAt: string;
  updatedAt: string;
};
interface PersonalItem {
  id: number;
  name: string;
  account: string;
  [key: string]: any;
}

interface PersonalResponse {
  data: PersonalItem[];
  status: number;
  msg: string;
}
export default ({ number }: IoTPointProps) => {
  const ref = useRef<ActionType>();
  const [form] = Form.useForm();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [queryForm, setQueryForm] = useState({
    keyWord: '',
  });
  const [title, setTitle] = useState('操作日志');

  const [currentRecord, setCurrentRecord] = useState(null);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [importLoading, setImportLoading] = useState(false);
    const [excelCols, setExcelCols] = useState([]);
    const [excelRows, setExcelRows] = useState([]);
    const [personalList, setPersonalList] = useState<{ label: string; value: number }[]>([]);
  const columns: ProColumns<TableListItem>[] = [
    {
      title: '钻机名称',
      dataIndex: 'drillName',
      ellipsis: true,
      width: 200,
      fixed: 'left',
      // widtwidth: 120,h: 120,
    },
    {
      title: '施工状态',
      dataIndex: 'status',
      ellipsis: true,
      width: 200,
      render: (text1,record) => {
          let color = 'green';
          let text = '下发任务';
          if (record.status === 0) {
            color = 'orange';
            text='下发任务';
          } else if (record.status === 1 || record.status === 2 || record.status === 3 || record.status === 4) {
            color = 'orange';
            text='施工中';
          } else if (record.status === 5) {
            color = 'red';
            text='终孔';
          } else if (record.status === 6) {
            color = 'green';
            text='已完成';
          } else if (record.status === 7) {
            color = 'default';
            text='废孔';
          }
          return <Tag color={color}>{text}</Tag>;
        },
      },
      // else if (record.status === 2) {
      //   color = 'red';
      //   text='施工过程';
      // } else if (record.status === 3) {
      //   color = 'red';
      //   text='退钻';
      // } else if (record.status === 4) {
      //   color = 'red';
      //   text='封孔';
      // valueEnum: {
      //   0: { text: '下发任务'},
      //   1: { text: '开孔申请'},
      //   2: { text: '施工过程 '},
      //   3: { text: '退钻'},
      //   4: { text: '封孔'},
      //   5: { text: '终孔'},
      //   6: { text: '连抽'},
      //   7: { text: '废孔'},
      // },
      // widtwidth: 120,h: 120,
    {
      title: '是否二次打孔',
      dataIndex: 'secondaryDrill',
      valueEnum: {
        0: { text: '否', status: 'Default' },
        1: { text: '是', status: 'Success' },
      },
      ellipsis: true,
      width: 120,
    },
    {
      title: '孔号',
      dataIndex: 'holeNumber',
      ellipsis: true,
      width: 120,
    },
    {
      title: '施工位置',
      dataIndex: 'constructionLocation',
      ellipsis: true,
      width: 120,
    },
    {
      title: '施工地点',
      dataIndex: 'position',
      ellipsis: true,
      width: 120,
    },
    {
      title: '孔深（m）',
      dataIndex: 'holeDepth',
      ellipsis: true,
      width: 120,
    },
    {
      title: '孔径（mm）',
      dataIndex: 'holeDiameter',
      ellipsis: true,
      width: 120,
    },
    {
      title: '开孔角度（°）',
      dataIndex: 'holeAngle',
      ellipsis: true,
      width: 120,
    },
    {
      title: '方位（°）',
      dataIndex: 'direction',
      ellipsis: true,
      width: 120,
    },
    {
      title: '开孔高度（m）',
      dataIndex: 'holeHeight',
      ellipsis: true,
      width: 120,
    },
    {
      title: '见煤距离（m）',
      dataIndex: 'coalDistance',
      ellipsis: true,
      width: 120,
    },
    {
      title: '见岩石距离（m）',
      dataIndex: 'rockDistance',
      ellipsis: true,
      width: 150,
    },
    // {
    //   title: '扩孔起始距离',
    //   dataIndex: 'reamingStartDistance',
    //   ellipsis: true,
    //   width: 120,
    // },
    // {
    //   title: '打钻起始距离',
    //   dataIndex: 'drillingStartDistance',
    //   ellipsis: true,
    //   width: 120,
    // },
    // {
    //   title: '预计出煤量',
    //   dataIndex: 'estimatedCoalOutput',
    //   ellipsis: true,
    //   width: 120,
    // },
    {
      title: '开孔角度误差（°）',
      dataIndex: 'holeAngleError',
      ellipsis: true,
      width: 150,
    },
    {
      title: '开孔方位误差（°）',
      dataIndex: 'holeDirectionError',
      ellipsis: true,
      width: 150,
    },
    {
      title: '孔深误差（m）',
      dataIndex: 'holeDepthError',
      ellipsis: true,
      search: false,
      width: 150,
    },
    // {
    //   title: '开孔高度误差',
    //   dataIndex: 'holeHeightError',
    //   ellipsis: true,
    //   width: 120,
    //   // fixed: 'right',
    // },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => [
          <a
              key="detail"
              onClick={() => {
                  form.setFieldsValue({
                      curatorId: number.curatorName,
                      curatorName: number.curatorName,
                      curatorAccount: number.curatorAccount,
                      ...(record.taskName && { taskName: record.taskName }),
                  });
                  setCurrentRecord(record);
                  setImportModalVisible(true);
                  setFileList([]);
                  setExcelCols([]);
                  setExcelRows([]);
              }}
          >
              调整
          </a>,
          <a
              key="detail"
              onClick={() => {
                  history.push(`/project/drill/detail?id=${record.id}`);
                  // history.push(`/project/Deliver/modify/?id=${record.id}`);
                  // history.push(`/project/Deliver/modify/?id=1`);
              }}
          >
              详情
          </a>,
      ],
  },
  ];
  const columns1 = [
    {
        title: '变更字段',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '变更前',
        dataIndex: 'age',
        key: 'age',
    },
    {
        title: '变更后',
        dataIndex: 'address',
        key: 'address',
    },
];
      // 获取人员列表
      const fetchPersonalList = async () => {
        try {
            const result = await getRequest('personal/get_all') as PersonalResponse;
            if (result.status === 0 && result.data) {
                const options = result.data.map((item: PersonalItem) => ({
                    label: item.name,
                    value: item.id,
                    account: item.account,
                }));
                setPersonalList(options);
            }
        } catch (error) {
            messageApi.error('获取人员列表失败');
        }
    };
    const handleExcelUpload = (file: File) => {
      ExcelRenderer(file, (error, response) => {
          if (error) {
              messageApi.error('文件解析失败');
              return;
          }

          setExcelCols(response.cols);
          setExcelRows(response.rows);
      });
  };

  const uploadProps: UploadProps = {
      onRemove: (file) => {
          const index = fileList.indexOf(file);
          const newFileList = fileList.slice();
          newFileList.splice(index, 1);
          setFileList(newFileList);
          setExcelCols([]);
          setExcelRows([]);
      },
      beforeUpload: (file) => {
          setFileList([...fileList, file]);
          handleExcelUpload(file);
          return false;
      },
      fileList,
      accept: '.xlsx,.xls',
      maxCount: 1
  };
  // 下载导入的模板
const downloadTemplate = () => {
  const fileUrl = './xlsx/drill.xlsx';
  
  // 显示加载状态
  setImportLoading(true);
  
  // 使用fetch获取文件内容
  fetch(fileUrl, {
      // 可以添加必要的headers，如果需要的话
      // headers: {
      //   'authorization': sessionStorage.getItem('token') || '',
      // },
  })
      .then(response => {
          if (!response.ok) {
              throw new Error('Network response was not ok');
          }
          return response.blob();
      })
      .then(blob => {
          // 创建blob URL
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = '钻孔设计模版.xlsx';
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url); // 释放URL对象
          document.body.removeChild(a);
      })
      .catch(error => {
          messageApi.error('下载模板失败');
          console.error('下载错误:', error);
      })
      .finally(() => {
          setImportLoading(false);
      });
}
      // 处理负责人选择
      const handlePersonalChange = (value: number, option: any) => {
        form.setFieldsValue({
            curatorId: value,
            curatorName: option.label,
            curatorAccount: option.account,
        });
    };
    useEffect(() => {
      if (importModalVisible) {
          fetchPersonalList();
      }
  }, [importModalVisible]);
  const getFullPath = (options: any[], selectedKeys: (string | number)[]) => {
    const result: string[] = [];
    let currentOptions = options;
    
    for (const key of selectedKeys) {
        const found = currentOptions.find(item => item.value === key);
        if (found) {
            result.push(found.label);
            currentOptions = found.children || [];
        }
    }
    
    return result;
};
  return (
    <>
      {contextHolder}
      <ProTable<TableListItem>
        actionRef={ref}
        scroll={{ x: 2400 }}
        columns={columns}
        request={async (params) => {
          const postData = {
            page: params.current,
            perPage: params.pageSize,
            parentId: number.id,
            ...(queryForm.keyWord ? { keyWord: queryForm.keyWord } : {}),
          };

          try {
            const result = await postRequest('drill/get_ls', postData);
            const { data, status, msg } = result as any;

            if (status === 0) {
              return {
                data: data.items,
                total: data.total,
                success: true,
              };
            }

            messageApi.error(msg || '获取数据失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch (error) {
            messageApi.error('请求失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        toolbar={{
          search: (
            <Flex>
              <Input
                style={{ width: '250px' }}
                allowClear
                value={queryForm.keyWord}
                onChange={(e) => {
                  setQueryForm({ ...queryForm, keyWord: e.target.value });
                  ref.current?.reload();
                }}
                onPressEnter={() => {
                  ref.current?.reload();
                }}
                placeholder="请输入钻机编号"
              />
            </Flex>
          ),
        }}
        rowKey="id"
        search={false}
      />
       <Modal
                title={currentRecord ? "调整任务" : "下发任务"}
                open={importModalVisible}
                footer={null}
                onCancel={() => {
                    setImportModalVisible(false);
                    setExcelCols([]);
                    setExcelRows([]);
                    setFileList([]);
                    setCurrentRecord(null);
                    form.resetFields();
                }}
                width={650}
            >
                <ProForm
                    form={form}
                    initialValues={currentRecord || {}}
                    submitter={{
                        render: (props) => {
                            return (
                                <div style={{ textAlign: 'right', marginTop: 16 }}>
                                    <Button
                                        style={{ marginRight: 8 }}
                                        onClick={() => {
                                            setImportModalVisible(false);
                                            setExcelCols([]);
                                            setExcelRows([]);
                                            setFileList([]);
                                            setCurrentRecord(null);
                                            form.resetFields();
                                        }}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        loading={importLoading}
                                        disabled={fileList.length === 0}
                                        onClick={() => props.submit()}
                                    >
                                        完成
                                    </Button>
                                </div>
                            );
                        },
                    }}
                    onFinish={async (values) => {
                      
                        setImportLoading(true);
                        try {
                            const api = 'drilltask/post_modify'
                            const result = await postRequest(api, {
                              id:number.id,
                              curatorName: values.curatorName,
                              curatorAccount: values.curatorAccount,
                          });
                            if (result.status === 0) {
                                const formData = new FormData();
                                formData.append('file', fileList[0]);
                                formData.append('id', result.data);
                                const res = await fetch(`${VITE_APP_BASE_URL}drilltask/post_import`, {
                                    method: 'POST',
                                    headers: {
                                        'authorization':sessionStorage.getItem('token'),
                                        'serviceType': '1'
                                    },
                                    body: formData
                                });
                                const resData = await res.json();
                                if (resData.status === 0) {
                                    messageApi.success(currentRecord ? '修改成功' : '添加成功');
                                    setImportModalVisible(false);
                                    setExcelCols([]);
                                    setExcelRows([]);
                                    setFileList([]);
                                    setCurrentRecord(null);
                                    form.resetFields();
                                    ref.current?.reload();
                                } else {
                                    messageApi.error(resData.msg || '添加失败');
                                }
                            } else {
                                messageApi.error(result.msg || '添加失败');
                            }
                        } catch (error) {
                            messageApi.error('添加失败');
                            console.error('Error:', error);
                        } finally {
                            setImportLoading(false);
                        }
                    }}
                >
                    {!currentRecord && (
                        // <ProFormText
                        //     name="taskName"
                        //     label="任务名称"
                        //     placeholder="请输入任务名称"
                        //     rules={[{ required: true, message: '请输入任务名称' }]}
                        // />
                        <ProFormCascader
                        name="taskName"
                        label="钻场名称"
                        placeholder="选择钻场"
                        request={async () => [
                            {
                              value: 'zhejiang',
                              label: 'Zhejiang',
                              children: [
                                {
                                  value: 'hangzhou',
                                  label: 'Hangzhou',
                                  children: [
                                    {
                                      value: 'xihu',
                                      label: 'West Lake',
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              value: 'jiangsu',
                              label: 'Jiangsu',
                              children: [
                                {
                                  value: 'nanjing',
                                  label: 'Nanjing',
                                  children: [
                                    {
                                      value: 'zhonghuamen',
                                      label: 'Zhong Hua Men',
                                    },
                                  ],
                                },
                              ],
                            },
                          ]}
                        rules={[{ required: true, message: '请选择钻场' }]}
                      />
                        // <ProFormCascader options={options} onChange={onChange} placeholder="选择钻场" rules={[{ required: true, message: '请选择钻场' }]} />
                    )}
                    <ProForm.Item
                        name="file"
                        label="上传工艺卡"
                        rules={[{ required: true, message: '请上传上传工艺卡' }]}
                        tooltip="注意：调整任务时请将无需调整的任务在文档中去掉再进行导入！"
                    >
                        <Upload.Dragger {...uploadProps}>
                            <p className="ant-upload-drag-icon">
                                <InboxOutlined />
                            </p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                            <p className="ant-upload-hint">支持 .xlsx, .xls 格式的文件</p>
                        </Upload.Dragger>
                    </ProForm.Item>
                    <div style={{display:"flex",justifyContent:"space-between"}}><span>钻孔设计模版.doc</span> <span><Button color="default" size="middle" onClick={downloadTemplate} variant="outlined">
            下载模板
          </Button>
</span> </div>
                    <ProFormSelect
                        name="curatorId"
                        label="负责人"
                        placeholder="请选择负责人"
                        options={personalList}
                        fieldProps={{
                            onChange: handlePersonalChange
                        }}
                        rules={[{ required: true, message: '请选择负责人' }]}
                    />
                    <ProFormText name="curatorName" hidden />
                    <ProFormText name="curatorAccount" hidden />
                </ProForm>
            </Modal>
            {/* <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal> */}
    </>
    
  );
};
