import { EllipsisOutlined, InboxOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
    ProForm,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Breadcrumb, Progress, Button, Select, Tag, Upload } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import axios from 'axios';
import type { GetProp, UploadFile, UploadProps } from 'antd';

const { RangePicker } = DatePicker;

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}


export default () => {
    const ref = useRef();
    const { Option } = Select;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryType, setQueryType] = useState('0');
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });
    const [importModalVisible, setImportModalVisible] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [importLoading, setImportLoading] = useState(false);
    const [excelCols, setExcelCols] = useState([]);
    const [excelRows, setExcelRows] = useState([]);

    const selectBefore = (
        <Select value={queryType} onChange={(value) => setQueryType(value)} >
            <Option value="0">单个查询</Option>
            <Option value="1">多个查询</Option>
        </Select>
    );


    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '打孔管理ID',
            dataIndex: 'id',
            search: false,
            width: 100,
            fixed: 'left',
        },
        {
            title: '日期',
            dataIndex: 'date',
            search: false,
            width: 100,
        },
        {
            title: '施工地点',
            dataIndex: 'position',
            search: false,
            width: 120,
        },
        {
            title: '钻机编号',
            dataIndex: 'drillNumber',
            search: false,
            width: 100,
        },
        {
            title: '钻孔类型',
            dataIndex: 'drillingType',
            search: false,
            width: 100,
        },
        {
            title: '班次',
            dataIndex: 'shift',
            search: false,
            width: 80,
        },
        {
            title: '开孔人员名称',
            dataIndex: 'personnel',
            search: false,
            width: 120,
        },
        {
            title: '孔号',
            dataIndex: 'holeNumber',
            search: false,
            width: 100,
        },
        {
            title: '孔深',
            dataIndex: 'holeDepth',
            search: false,
            width: 80,
        },
        {
            title: '孔径',
            dataIndex: 'holeDiameter',
            search: false,
            width: 80,
        },
        {
            title: '开孔角度',
            dataIndex: 'holeAngle',
            search: false,
            width: 100,
        },
        {
            title: '方位',
            dataIndex: 'direction',
            search: false,
            width: 80,
        },
        {
            title: '开孔高度',
            dataIndex: 'holeHeight',
            search: false,
            width: 100,
        },
        {
            title: '见煤距离',
            dataIndex: 'coalDistance',
            search: false,
            width: 100,
        },
        {
            title: '见岩距离',
            dataIndex: 'rockDistance',
            search: false,
            width: 100,
        },
        {
            title: '扩孔起始距离',
            dataIndex: 'reamingStartDistance',
            search: false,
            width: 120,
        },
        {
            title: '打钻起始距离',
            dataIndex: 'drillingStartDistance',
            search: false,
            width: 120,
        },
        {
            title: '预计出煤量',
            dataIndex: 'estimatedCoalOutput',
            search: false,
            width: 100,
        },
        {
            title: '开孔角度误差',
            dataIndex: 'holeAngleError',
            search: false,
            width: 120,
        },
        {
            title: '开孔方位误差',
            dataIndex: 'holeDirectionError',
            search: false,
            width: 120,
        },
        {
            title: '开孔高度误差',
            dataIndex: 'holeHeightError',
            search: false,
            width: 120,
        },
        {
            title: '孔深误差',
            dataIndex: 'holeDepthError',
            search: false,
            width: 100,
        },
        {
            title: '是否需要轨迹',
            dataIndex: 'ifTrajectoryRequired',
            search: false,
            width: 120,
            render: (text) => text === 1 ? '需要' : '不需要'
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            search: false,
            width: 160,
        },
        {
            title: '操作',
            dataIndex: 'option',
            valueType: 'option',
            width: 80,
            fixed: 'right',
            render: (text, record,) => [
                <a
                    key="config"
                    onClick={() => {
                        history.push(`/construct/hole/detail/?id=${record.id}`)
                    }}
                >
                    详情
                </a>,
            ],
        },
        {
            title: '',
            dataIndex: 'keyWord',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (
                    <Input
                        value={status}
                        onChange={(e) => onchange(e.target.value)}
                        placeholder='请输入钻机编号'
                    />
                );
            },
        },
        {
            title: '',
            dataIndex: 'dateRange',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (
                    <RangePicker
                        value={status}
                        onChange={(value) => onchange(value)}
                        placeholder={['开始时间', '结束时间']}
                        picker="date"
                        format="YYYY-MM-DD"
                    />
                );
            },
        },

    ];

    const columns1 = [
        {
            title: '变更字段',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '变更前',
            dataIndex: 'age',
            key: 'age',
        },
        {
            title: '变更后',
            dataIndex: 'address',
            key: 'address',
        },
    ];

    const data1 = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];

    const handleExcelUpload = (file: File) => {
        ExcelRenderer(file, (error, response) => {
            if (error) {
                messageApi.error('文件解析失败');
                return;
            }

            setExcelCols(response.cols);
            setExcelRows(response.rows);
        });
    };

    const uploadProps: UploadProps = {
        onRemove: (file) => {
            const index = fileList.indexOf(file);
            const newFileList = fileList.slice();
            newFileList.splice(index, 1);
            setFileList(newFileList);
            setExcelCols([]);
            setExcelRows([]);
        },
        beforeUpload: (file) => {
            setFileList([...fileList, file]);
            handleExcelUpload(file);
            return false;
        },
        fileList,
        accept: '.xlsx,.xls',
        maxCount: 1
    };

    const handleImport = async () => {
        setImportLoading(true);
        try {
            const formData = new FormData();
            formData.append('file', fileList[0] as any);
            formData.append('upload_type', 'local');
            formData.append('file_name', fileList[0].name);
            fetch(`${VITE_APP_BASE_URL}drilltask/post_import`, {
                method: 'POST',
                headers: {
                    // 'authorization': sessionStorage.getItem('token') || '',
                    'authorization': 'eyJhbGciOiJIUzI1NiJ9.eyJpbmZvIjoie1wiYWNjb3VudFwiOlwiMTUxMzczODQ0ODRcIixcImNvcnBJZFwiOlwidGZsTGM0cHZFTGtlRHM0OW1lWTY5alF5QlZYdTlUVWttd0NcIixcIm5hbWVcIjpcIumprOS_iuadsFwiLFwicHJvZmlsZVBpY1wiOlwiaHR0cHM6Ly93d3cuYmluZy5jb20vdGg_aWRcXHUwMDNkT0lQLnlwLUQtS0hJM2Uybk40ZU1CSmNFVkFBQUFBXFx1MDAyNndcXHUwMDNkOTlcXHUwMDI2aFxcdTAwM2QxMDBcXHUwMDI2Y1xcdTAwM2Q4XFx1MDAyNnJzXFx1MDAzZDFcXHUwMDI2cWx0XFx1MDAzZDkwXFx1MDAyNm9cXHUwMDNkNlxcdTAwMjZwaWRcXHUwMDNkMy4xXFx1MDAyNnJtXFx1MDAzZDJcIn0ifQ.T7YqVBJoycD1pxuD27HaX2fLm_pmxyG9TJX39ftX41I',
                    'serviceType': 2,
                },
                body: formData,
            })
                .then((res) => res.json())
                .then((res) => {
                    if (res.status === 0) {
                        messageApi.success('导入成功');
                        setImportModalVisible(false);
                        setExcelCols([]);
                        setExcelRows([]);
                        setFileList([]);
                        ref.current?.reload();
                    } else {
                        messageApi.error(res.msg || '导入失败');
                    }
                })
                .catch(() => {
                    messageApi.error('导入失败');
                })
                .finally(() => {
                });
        } catch (error) {
            messageApi.error('导入失败');
        } finally {
            setImportLoading(false);
        }
    };

    useEffect(() => {
    }, []);

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页' },
                    { title: '施工管理' },
                    { title: '打孔管理' },
                ]}
            />
            <ProTable<TableListItem>
                style={{ marginTop: '24px' }}
                headerTitle='打孔管理'
                actionRef={ref}
                columns={columns}
                scroll={{ x: 2600 }}
                request={async (params, sorter, filter) => {
                    const { current, pageSize, keyWord, dateRange, ...rest } = params;
                    let postData = {
                        page: current,
                        perPage: pageSize,
                        keyWord: keyWord,
                        startTime: dateRange?.[0],
                        endTime: dateRange?.[1],
                    }
                    const result = await postRequest('drill/get_ls', postData);
                    const { data, status, msg } = result
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                rowKey="key"
                toolBarRender={() => [
                    <Button
                        key="import"
                        onClick={() => setImportModalVisible(true)}
                        style={{ marginRight: 8 }}
                    >
                        批量导入
                    </Button>,
                    <Button
                        key="add"
                        type="primary"
                        onClick={() => history.push('/construct/hole/add')}
                    >
                        新建打孔计划
                    </Button>,
                ]}
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,

                    className: 'search-form'
                }}
                form={{
                    initialValues: {
                        sort: 0
                    }
                }}
            />

            <Modal
                title="批量导入"
                open={importModalVisible}
                footer={null}
                onCancel={() => {
                    setImportModalVisible(false);
                    setExcelCols([]);
                    setExcelRows([]);
                    setFileList([]);
                }}
                width={1000}
            >
                <ProForm
                    submitter={{
                        render: (props, dom) => {
                            return (
                                <div style={{ textAlign: 'right',marginTop:16 }}>
                                    <Button
                                        style={{ marginRight: 8 }}
                                        onClick={() => {
                                            setImportModalVisible(false);
                                            setExcelCols([]);
                                            setExcelRows([]);
                                            setFileList([]);
                                        }}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        loading={importLoading}
                                        disabled={fileList.length === 0}
                                        onClick={handleImport}
                                    >
                                        确定
                                    </Button>
                                </div>
                            );
                        },
                    }}
                >
                    <ProForm.Item
                        name="file"
                        label="上传文件"
                        rules={[{ required: true, message: '请上传文件' }]}
                    >
                        <Upload.Dragger
                            {...uploadProps}
                        >
                            <p className="ant-upload-drag-icon">
                                <InboxOutlined />
                            </p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                            <p className="ant-upload-hint">支持 .xlsx, .xls 格式的文件</p>
                        </Upload.Dragger>
                    </ProForm.Item>

                    {excelRows.length > 0 && (
                        <div style={{ marginTop: 16 }}>
                            <h3>数据预览</h3>
                            <div style={{
                                maxHeight: '400px',
                                maxWidth: '100%',
                                overflow: 'auto'
                            }}>
                                <div style={{
                                    minWidth: '1500px',
                                }}>
                                    <OutTable
                                        data={excelRows}
                                        columns={excelCols}
                                        tableClassName="preview-table"
                                        tableHeaderRowClass="preview-header"
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </ProForm>
            </Modal>

            <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal>
        </>
    );
};