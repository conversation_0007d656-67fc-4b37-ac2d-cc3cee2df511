# 实时作业监控 - 萤石云直播播放器集成

## 🎯 功能概述

将实时作业页面的监控功能从FLV播放器改为萤石云播放器，支持实时直播流播放。

## 🔧 技术实现

### 1. API接口变更

#### 新的直播接口
- **接口地址**: `/api/ys/get_live_address`
- **请求方式**: POST
- **参数方式**: Query参数 + POST请求体
- **参数**: `deviceCode` (string, 必需) - 设备code

#### 请求示例
```typescript
// POST /api/ys/get_live_address?deviceCode=your_device_code
// Body: {} (空对象)
```

#### 响应数据结构
```typescript
interface LiveAddressResponse {
  status: number;
  msg: string;
  data: {
    id: string;                    // 直播ID
    url: string;                   // 直播流地址 (m3u8格式)
    expireTime: string;            // 过期时间
  };
}
```

#### 响应示例
```json
{
  "status": 0,
  "msg": "request:ok",
  "data": {
    "id": "857925396681732096",
    "url": "https://open.ys7.com/v3/openlive/33010175992677797274:33010042991117112440_1_1.m3u8?expire=1750903754&id=857925396681732096&t=3863bc89ea3d60b721ef644d6063a4ad50f4b5e84716972359622b829beccf55&ev=100&devProto=gb28181",
    "expireTime": "2025-06-26 10:09:14"
  }
}
```

### 2. 组件架构变更

#### 原来的架构 (FLV播放器)
```
Monitor组件
    ↓
flv.js播放器
    ↓
FLV视频流
```

#### 新的架构 (萤石云播放器)
```
Monitor组件
    ↓
VideoErrorBoundary (错误边界)
    ↓
EzVideoPlayer (萤石云播放器)
    ↓
ezopen:// 协议直播流
```

### 3. 核心代码实现

#### 组件状态管理
```typescript
const Monitor: React.FC<{ data?: DeviceParams | null }> = ({ data }) => {
  // 萤石云直播URL状态
  const [ezOpenUrl, setEzOpenUrl] = useState<string>('');
  // 加载状态
  const [loading, setLoading] = useState(true);
  // 错误状态
  const [error, setError] = useState<string | null>(null);
  // 直播数据
  const [liveData, setLiveData] = useState<LiveAddressResponse['data'] | null>(null);
```

#### 获取直播地址
```typescript
const fetchLiveAddress = async () => {
  try {
    setLoading(true);
    setError(null);

    if (!data?.deviceCode) {
      throw new Error('缺少设备code参数');
    }

    // 调用萤石云直播API
    const response = await postRequest(
      `/api/ys/get_live_address?deviceCode=${data.deviceCode}`, 
      {}
    ) as LiveAddressResponse;

    if (response && response.status === 0 && response.data?.url) {
      const liveUrl = response.data.url;
      
      // 将m3u8 URL转换为ezopen协议URL
      const ezOpenLiveUrl = liveUrl.replace(
        'https://open.ys7.com/v3/openlive/', 
        'ezopen://open.ys7.com/'
      );
      
      setEzOpenUrl(ezOpenLiveUrl);
      setLiveData(response.data);
    } else {
      throw new Error(response?.msg || '获取直播地址失败');
    }
  } catch (err) {
    setError(err instanceof Error ? err.message : '获取直播地址失败');
  } finally {
    setLoading(false);
  }
};
```

#### URL协议转换
```typescript
// 原始m3u8 URL
const m3u8Url = "https://open.ys7.com/v3/openlive/33010175992677797274:33010042991117112440_1_1.m3u8?...";

// 转换为ezopen协议
const ezOpenUrl = m3u8Url.replace('https://open.ys7.com/v3/openlive/', 'ezopen://open.ys7.com/');
// 结果: "ezopen://open.ys7.com/33010175992677797274:33010042991117112440_1_1.m3u8?..."
```

#### 播放器渲染
```typescript
{data && !loading && !error && ezOpenUrl && (
  <VideoErrorBoundary>
    <EzVideoPlayer
      key={ezOpenUrl} // 强制重新创建组件
      ezOpenUrl={ezOpenUrl}
      width="100%"
      height="580px"
      style={{
        borderRadius: '8px',
        overflow: 'hidden'
      }}
      onCapture={handleEzCapture}
    />
  </VideoErrorBoundary>
)}
```

### 4. 数据类型定义

#### 设备参数类型
```typescript
interface DeviceParams {
  deviceCode: string; // 设备code (必需)
}
```

#### API响应类型
```typescript
interface LiveAddressResponse {
  status: number;
  msg: string;
  data: {
    id: string;           // 直播ID
    url: string;          // 直播流地址 (m3u8格式)
    expireTime: string;   // 过期时间
  };
}
```

## 🎯 功能特点

### 1. 实时直播
- ✅ **真正的直播流**: 使用萤石云的实时直播服务
- ✅ **低延迟**: 萤石云优化的直播协议
- ✅ **高稳定性**: 专业的视频流服务

### 2. 错误处理
- ✅ **多层错误保护**: VideoErrorBoundary + EzVideoPlayer内部错误处理
- ✅ **友好错误提示**: 显示具体的错误原因
- ✅ **自动重试机制**: 播放器内置重试逻辑

### 3. 用户体验
- ✅ **加载状态**: 显示"正在获取直播地址..."
- ✅ **直播信息**: 显示直播ID、过期时间、在线状态
- ✅ **响应式设计**: 自适应不同屏幕尺寸

### 4. 开发友好
- ✅ **详细日志**: 完整的调试信息
- ✅ **类型安全**: 完整的TypeScript类型定义
- ✅ **组件复用**: 复用现有的EzVideoPlayer组件

## 🔄 使用流程

### 1. 用户操作流程
```
用户选择设备 → 传入deviceCode → 调用直播API → 获取m3u8 URL → 转换为ezopen协议 → 萤石云播放器播放
```

### 2. 错误处理流程
```
API调用失败 → 显示错误信息
播放器初始化失败 → VideoErrorBoundary捕获 → 显示友好错误页面
网络中断 → 播放器自动重试
```

## 📋 接口对比

### 原来的接口 (FLV)
```typescript
// 参数复杂，需要多个字段
interface DeviceParams {
  deviceSerial: string;
  channelNo?: number;
  protocol?: number;
}

// 返回FLV流地址
{
  status: 0,
  data: {
    url: "rtmp://xxx.flv" // FLV格式
  }
}
```

### 新的接口 (萤石云直播)
```typescript
// 参数简化，只需要deviceCode
interface DeviceParams {
  deviceCode: string;
}

// 返回完整的直播信息
{
  status: 0,
  data: {
    id: "857925396681732096",
    url: "https://open.ys7.com/v3/openlive/xxx.m3u8", // m3u8格式
    expireTime: "2025-06-26 10:09:14"
  }
}
```

## 🧪 测试场景

### 1. 正常流程测试
- 选择有效设备 → 应该正常显示直播画面
- 直播信息显示 → 应该显示直播ID、过期时间等

### 2. 错误场景测试
- 无效deviceCode → 应该显示"获取直播地址失败"
- 网络异常 → 应该显示网络错误提示
- 设备离线 → 应该显示设备不可用提示

### 3. 边界情况测试
- 快速切换设备 → 不应该出现播放器冲突
- 长时间观看 → 检查内存使用是否正常
- 页面刷新 → 应该能正常重新加载

## 📁 相关文件

- `src/pages/Home/Operation/components/monitor.tsx` - 主要修改文件
- `src/pages/Project/Construction/component/EzVideoPlayer.tsx` - 复用的播放器组件
- `src/pages/Project/Construction/component/VideoErrorBoundary.tsx` - 复用的错误边界
- `docs/live-streaming-ezplayer-integration.md` - 本文档

## 🎉 总结

通过这次改造，实时作业监控功能获得了：

- 🎯 **更好的直播体验**: 萤石云专业的直播服务
- 🎯 **更简单的接口**: 只需要deviceCode一个参数
- 🎯 **更稳定的播放**: 复用经过验证的EzVideoPlayer组件
- 🎯 **更好的错误处理**: 多层错误保护机制

现在用户可以通过萤石云播放器观看高质量的实时监控直播了！🚀
