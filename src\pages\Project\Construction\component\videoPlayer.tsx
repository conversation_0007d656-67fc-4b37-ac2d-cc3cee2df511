/*
 * @Author: AI Assistant
 * @Date: 2025-01-02
 * @Description: 视频播放组件 - 重构版本，移除注释代码，只保留萤石云播放器功能
 */
import React, { useState, useEffect } from 'react';
import styles from './VideoPlayer.module.css';
import { getRequest, postRequest } from '@/services/api/api';
import { message } from 'antd';
import EzVideoPlayer from './EzVideoPlayer';
import VideoErrorBoundary from './VideoErrorBoundary';

// 开发环境下引入测试工具
if (process.env.NODE_ENV === 'development') {
  import('@/utils/videoDetailApiTest');
}

// 新的视频数据接口（基于API返回）
interface DrillVideoData {
  id: number;
  taskId: number;
  title: string;
  videoUrl: string;
  startTime: string;
  endTime: string;
  stageStatus: number;
  createdAt: string;
  updatedAt: string;
  status: number;
}

// API响应接口
interface VideoListResponse {
  status: number;
  msg?: string;
  data?: DrillVideoData[];
}

// 任务录像地址API响应接口
interface TaskRecordUrlResponse {
  status: number;
  msg?: string;
  data?: string; // 直接返回视频URL字符串
}

// 视频详情API响应接口 (EycServDrillVideo)
interface VideoDetailResponse {
  status: number;
  msg?: string;
  data?: {
    id: number;                    // 主键ID
    taskId: number;               // 关联的钻孔任务ID
    title: string;                // 视频标题
    videoUrl: string;             // 视频URL
    startTime: string;            // 视频开始时间
    endTime: string;              // 视频结束时间
    stageStatus: number;          // 视频所属阶段 (0-7)
    createdAt: string;            // 创建时间
    updatedAt: string;            // 更新时间
    status: number;               // 状态 0-正常 1-删除
  };
}

// 阶段状态映射
const STAGE_STATUS_MAP: Record<number, string> = {
  0: '下发任务',
  1: '开孔申请',
  2: '施工过程',
  3: '退钻',
  4: '封孔',
  5: '终孔',
  6: '连抽',
  7: '废孔'
};

// 显示用的视频时间段接口
interface VideoTimeSlot {
  id: number;
  startTime: string;
  endTime: string;
  title: string;
}

interface VideoPlayerProps {
  number: number;
  gbsId?: string;
  deviceCode?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ number, gbsId, deviceCode }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [videoList, setVideoList] = useState<VideoTimeSlot[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<number | null>(null);
  const [ezOpenUrl, setEzOpenUrl] = useState<string>('');
  const [recordTimeStr, setRecordTimeStr] = useState('');
  const [taskRecordUrl, setTaskRecordUrl] = useState<string>(''); // 任务录像地址
  const [currentVideoDetail, setCurrentVideoDetail] = useState<any>(null); // 当前播放的视频详情

  // 获取任务录像地址
  const fetchTaskRecordUrl = async () => {
    try {
      // {{ AURA: Add - 调用获取任务录像地址接口 }}
      const response: TaskRecordUrlResponse = await postRequest('/api/ys/get_record_url', {
        parentId: number // 使用任务ID作为parentId
      });

      console.log('获取任务录像地址响应:', response);

      if (response.status === 0 && response.data) {
        const recordUrl = response.data;
        setTaskRecordUrl(recordUrl);
        setEzOpenUrl(recordUrl); // 设置为默认播放地址
        setCurrentVideoDetail(null); // 清空视频详情，表示播放任务录像
        console.log('✅ 任务录像地址获取成功:', recordUrl);
        console.log('✅ 已设置ezOpenUrl为:', recordUrl);
      } else {
        console.warn('获取任务录像地址失败:', response.msg);
        messageApi.warning(response.msg || '获取任务录像地址失败');
      }
    } catch (error) {
      console.error('获取任务录像地址出错:', error);
      messageApi.error('获取任务录像地址失败，请稍后重试');
    }
  };

  // 处理视频选择 - 使用视频详情接口
  const handleVideoSelect = async (videoId: number) => {
    setSelectedVideo(videoId);

    try {
      // {{ AURA: Modify - 使用视频详情接口获取单个视频的详细信息 }}
      console.log('🎬 点击视频列表项，videoId:', videoId);

      const response: VideoDetailResponse = await getRequest(`/api/drill/video/detail/${videoId}`);

      console.log('🎬 视频详情接口响应:', response);

      if (response.status === 0 && response.data) {
        const videoDetail = response.data;

        console.log('🎬 获取到视频详情:', {
          id: videoDetail.id,
          title: videoDetail.title,
          videoUrl: videoDetail.videoUrl,
          startTime: videoDetail.startTime,
          endTime: videoDetail.endTime,
          stageStatus: videoDetail.stageStatus,
          stageText: STAGE_STATUS_MAP[videoDetail.stageStatus] || '未知阶段'
        });

        if (videoDetail.videoUrl) {
          setEzOpenUrl(videoDetail.videoUrl);
          setRecordTimeStr(videoDetail.startTime);
          setCurrentVideoDetail(videoDetail); // 保存当前视频详情
          console.log('✅ 已切换到视频片段:', videoDetail.title);
          console.log('✅ 视频URL:', videoDetail.videoUrl);
        } else {
          messageApi.warning('该视频暂无播放地址');
        }
      } else {
        messageApi.error(response.msg || '获取视频详情失败');
      }
    } catch (error) {
      console.error('❌ 获取视频详情失败:', error);
      messageApi.error('获取视频详情失败，请稍后重试');
    }
  };

  // 获取视频列表
  useEffect(() => {
    const fetchVideoList = async () => {
      try {
        // {{ AURA-X: Modify - 使用新的视频列表API接口. Approval: 寸止(ID:pending). }}
        const res: VideoListResponse = await getRequest(`/api/drill/video/list/${number}`);

        if (res.status === 0 && res.data) {
          // 转换API数据为显示格式
          const slots: VideoTimeSlot[] = res.data.map(item => ({
            id: item.id,
            startTime: item.startTime,
            endTime: item.endTime,
            title: item.title
          }));

          setVideoList(slots);

          // {{ AURA: Modify - 移除默认选择第一个视频的逻辑，让用户手动选择 }}
          // 不再默认选择第一个视频，用户需要手动点击选择
        } else {
          messageApi.error(res.msg || '获取视频列表失败');
        }
      } catch (error) {
        console.error('Error fetching video list:', error);
        messageApi.error('获取视频列表失败，请稍后重试');
      }
    };

    if (number) {
      // {{ AURA: Modify - 页面加载时同时获取任务录像地址和视频列表 }}
      fetchTaskRecordUrl(); // 获取任务录像地址
      fetchVideoList();      // 获取视频列表
    }
  }, [number]);

  return (
    <div className={styles.container}>
      {contextHolder}

      {/* 左侧视频播放区域 */}
      <div className={styles.videoSection}>
        {ezOpenUrl ? (
          <VideoErrorBoundary>
            <EzVideoPlayer
              key={ezOpenUrl} // 强制重新创建组件
              ezOpenUrl={ezOpenUrl}
              width="100%"
              height="550px"
              className={styles.videoPlayer}
              style={{
                borderRadius: '8px',
                overflow: 'hidden'
              }}
            />
          </VideoErrorBoundary>
        ) : (
          <div
            className={styles.videoPlayer}
            style={{
              width: '100%',
              height: '550px',
              borderRadius: '8px',
              backgroundColor: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999',
              fontSize: '16px'
            }}
          >
            请选择视频进行播放
          </div>
        )}

        {/* 视频信息展示 */}
        <div className={styles.videoCurrentInfo}>
          <div className={styles.videoName}>{deviceCode || '设备信息'}</div>
          <div className={styles.videoTimeInfo}>
            {currentVideoDetail ? (
              <div>
                <div>当前播放: {currentVideoDetail.title}</div>
                <div>播放时间: {currentVideoDetail.startTime} — {currentVideoDetail.endTime}</div>
                <div>阶段: {STAGE_STATUS_MAP[currentVideoDetail.stageStatus] || '未知阶段'}</div>
              </div>
            ) : taskRecordUrl && ezOpenUrl === taskRecordUrl ? (
              <span>当前播放: 任务录像</span>
            ) : recordTimeStr ? (
              <span>当前播放时间: {recordTimeStr}</span>
            ) : null}
          </div>
        </div>
      </div>

      {/* 右侧视频列表 */}
      <div className={styles.videoList}>
        <h2>视频回放片段列表</h2>
        <div
          className={styles.listContainer}
          style={{
            maxHeight: '550px',
            overflowY: 'auto',
            scrollbarWidth: 'thin',
            paddingRight: '5px'
          }}
        >
          {videoList.map((item) => (
            <div
              key={item.id}
              className={`${styles.videoItem} ${selectedVideo === item.id ? styles.selected : ''}`}
              onClick={() => handleVideoSelect(item.id)}
            >
              <div className={styles.timeAndDate}>
                <span>{item.startTime} — {item.endTime}</span>
              </div>
              <div className={styles.title}>{item.title}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
