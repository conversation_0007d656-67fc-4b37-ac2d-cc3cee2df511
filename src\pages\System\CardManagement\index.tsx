import React, { useRef } from 'react';
import { Button, message, Modal, Tag, Space } from 'antd';
import { ProTable, ActionType, ProColumns } from '@ant-design/pro-components';
import { PlusOutlined, EditOutlined, DeleteOutlined, UserAddOutlined, UserDeleteOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { cardService } from './service';
import { CardItem, CardStatusLabels, CardStatusColors } from './types';

const CardManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();

  // 删除卡片
  const handleDelete = async (record: CardItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除卡片 "${record.cardNumber}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const result = await cardService.delete(record.id);
          if (result.status === 0) {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            message.error(result.msg || '删除失败');
          }
        } catch (error) {
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 解绑员工
  const handleUnbind = async (record: CardItem) => {
    Modal.confirm({
      title: '确认解绑',
      content: `确定要解绑卡片 "${record.cardNumber}" 与员工 "${record.employeeName}" 的绑定关系吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const result = await cardService.unbindEmployee(record.id);
          if (result.status === 0) {
            message.success('解绑成功');
            actionRef.current?.reload();
          } else {
            message.error(result.msg || '解绑失败');
          }
        } catch (error) {
          message.error('解绑失败，请重试');
        }
      },
    });
  };

  // 跳转到编辑页面
  const handleEdit = (record: CardItem) => {
    history.push(`/system/cardmanagement/edit?id=${record.id}`);
  };

  // 跳转到添加页面
  const handleAdd = () => {
    history.push('/system/cardmanagement/add');
  };

  // 表格列定义
  const columns: ProColumns<CardItem>[] = [
    {
      title: '卡片编号',
      dataIndex: 'cardNumber',
      key: 'cardNumber',
      width: 150,
      copyable: true,
    },
    {
      title: '卡片状态',
      dataIndex: 'cardStatus',
      key: 'cardStatus',
      width: 100,
      valueType: 'select',
      valueEnum: {
        0: { text: '禁用', status: 'Default' },
        1: { text: '启用', status: 'Success' },
        2: { text: '丢失', status: 'Warning' },
        3: { text: '损坏', status: 'Error' },
      },
      render: (_, record) => (
        <Tag color={CardStatusColors[record.cardStatus as keyof typeof CardStatusColors]}>
          {CardStatusLabels[record.cardStatus as keyof typeof CardStatusLabels]}
        </Tag>
      ),
    },
    {
      title: '绑定员工',
      dataIndex: 'employeeName',
      key: 'employeeName',
      width: 120,
      render: (_, record) => record.employeeName || '-',
    },
    {
      title: '员工账号',
      dataIndex: 'employeeAccount',
      key: 'employeeAccount',
      width: 120,
      search: false,
      render: (_, record) => record.employeeAccount || '-',
    },
    {
      title: '绑定时间',
      dataIndex: 'bindTime',
      key: 'bindTime',
      width: 160,
      search: false,
      valueType: 'dateTime',
      render: (_, record) => record.bindTime || '-',
    },
    {
      title: '操作员',
      dataIndex: 'bindOperatorName',
      key: 'bindOperatorName',
      width: 100,
      search: false,
      render: (_, record) => record.bindOperatorName || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      search: false,
      render: (_, record) => {
        const actions = [
          <Button
            key="edit"
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>,
        ];

        // 如果已绑定员工，显示解绑按钮
        if (record.employeeId) {
          actions.push(
            <Button
              key="unbind"
              type="link"
              size="small"
              icon={<UserDeleteOutlined />}
              onClick={() => handleUnbind(record)}
            >
              解绑
            </Button>
          );
        }

        actions.push(
          <Button
            key="delete"
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        );

        return <Space size="small">{actions}</Space>;
      },
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <ProTable<CardItem>
        headerTitle="卡片列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加卡片
          </Button>,
        ]}
        columns={columns}
        request={async (params) => {
          try {
            const { current, pageSize, cardNumber, cardStatus, employeeName, ...rest } = params;
            const requestParams = {
              page: current,
              perPage: pageSize,
              ...(cardNumber && { cardNumber }),
              ...(cardStatus !== undefined && { cardStatus }),
              ...(employeeName && { employeeName }),
              ...rest,
            };

            const result = await cardService.getList(requestParams);

            if (result.status === 0 && result.data) {
              return {
                data: result.data.items || [],
                success: true,
                total: result.data.total || 0,
              };
            }
            return {
              data: [],
              success: false,
              total: 0,
            };
          } catch (error) {
            console.error('获取卡片列表失败:', error);
            message.error('获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </div>
  );
};

export default CardManagement;
