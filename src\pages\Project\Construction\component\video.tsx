/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-25 09:06:23
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-25 09:57:19
 * @FilePath: \diy_tfl_pc\src\pages\Project\Construction\component\video.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useEffect, useRef } from 'react';
import styles from './VideoPlayer.module.css';
import { getRequest, postRequest } from '@/services/api/api';
import { message, Card } from 'antd';
// import { Radio } from 'antd'; // 注释掉，不再需要播放模式选择
import axios from 'axios';
import EzVideoPlayer from './EzVideoPlayer';

interface VideoTimeSlot {
  id: number;
  startTime: string;
  endTime: string;
  title: string;
  date: string;
}

// 标准播放器相关的接口和函数 - 注释掉，只使用萤石云播放器
/*
interface VideoSegmentInfo {
  url: string;
  startTime: Date;
  duration: number; // in milliseconds
  endTime: Date;
}

// 提取匹配视频 URL 与 seek 时间的函数
function findMatchingIndexAndSeek(targetTime: string, segments: VideoSegmentInfo[]) {
  const targetDate = new Date(targetTime).getTime();
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];
    if (targetDate >= segment.startTime.getTime() && targetDate < segment.endTime.getTime()) {
      const offsetSeconds = Math.floor((targetDate - segment.startTime.getTime()) / 1000);
      return { index: i, offsetSeconds };
    }
  }
  return { index: -1, offsetSeconds: 0 };
}

// 从URL中解析视频开始时间和持续时长
function parseVideoInfo(url: string): VideoSegmentInfo | null {
  const match = url.match(/\/(\d{14})-(\d+)\.mp4(?:\?|$)/);
  if (!match) return null;

  const startStr = match[1];
  const durationMilliseconds = parseInt(match[2], 10);

  try {
    const startDate = new Date(
      parseInt(startStr.substring(0, 4), 10),
      parseInt(startStr.substring(4, 6), 10) - 1,
      parseInt(startStr.substring(6, 8), 10),
      parseInt(startStr.substring(8, 10), 10),
      parseInt(startStr.substring(10, 12), 10),
      parseInt(startStr.substring(12, 14), 10),
    );

    const endDate = new Date(startDate.getTime() + durationMilliseconds);

    return {
      url,
      startTime: startDate,
      duration: durationMilliseconds,
      endTime: endDate
    };
  } catch (e) {
    console.error("Failed to parse video info from URL:", url, e);
    return null;
  }
}
*/
// 标准播放器自定义时间轴组件 - 注释掉，只使用萤石云播放器
/*
// 添加自定义时间轴组件
interface CustomTimelineProps {
  totalDuration: number; // 总时长（秒）
  currentTime: number; // 当前播放时间（秒）
  onSeek: (seekTimeInSeconds: number) => void; // 跳转回调
  segments: {
    duration: number;
    startTimeOffset: number; // 该片段在整体时间轴中的起始偏移（秒）
  }[]; // 各个片段信息
  overallStartTime: Date | null; // 整体开始时间
  videoRef: React.RefObject<HTMLVideoElement>; // Add videoRef prop
  isPlaying: boolean; // Add isPlaying state to sync play/pause button
  onPlayPauseClick: () => void; // Add handler for play/pause button
}
*/

// 标准播放器自定义时间轴组件 - 注释掉，只使用萤石云播放器
/*
// 自定义时间轴组件
const CustomTimeline: React.FC<CustomTimelineProps> = ({
  totalDuration,
  currentTime,
  onSeek,
  segments,
  overallStartTime,
  videoRef,
  isPlaying,
  onPlayPauseClick
}) => {
  const timelineRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  // 格式化时间显示（秒 -> HH:MM:SS）
  const formatTime = (timeInSeconds: number): string => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // 处理时间轴上的点击
  const handleTimelineClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const clickPosition = e.clientX - rect.left;
    const percentage = clickPosition / rect.width;
    const seekTime = percentage * totalDuration;

    onSeek(seekTime);
  };

  // 处理拖动开始
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);

    // 阻止默认事件，防止拖动时选中文本
    e.preventDefault();
  };

  // 处理拖动中的移动
  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!timelineRef.current) return;

      const rect = timelineRef.current.getBoundingClientRect();
      let position = e.clientX - rect.left;

      // 确保在时间轴范围内
      position = Math.max(0, Math.min(position, rect.width));

      const percentage = position / rect.width;
      const seekTime = percentage * totalDuration;

      // 更新拖动时的视觉反馈
      if (thumbRef.current) {
        thumbRef.current.style.left = `${percentage * 100}%`;
      }

      // 这里可以选择实时更新视频时间，或等到鼠标松开时再更新
      // 实时更新会更流畅，但可能会频繁触发视频seeked事件
      onSeek(seekTime);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, totalDuration, onSeek]);

  return (
    <div className={styles.customTimelineContainer}>
      <div className={styles.timeDisplay}>
        // Use the passed props instead of directly accessing videoNodeRef
        <button
          className={styles.playPauseButton}
          onClick={onPlayPauseClick}
        >
          <img
            src={!isPlaying ? require('@/assets/stop.png') : require('@/assets/play.png')}
            style={{ width: '14px', height: '20px' }}
          />
        </button>
        <span>{formatTime(currentTime)}/{formatTime(totalDuration)}</span>
      </div >

  <div
    ref={timelineRef}
    className={styles.timeline}
    onClick={handleTimelineClick}
  >
    // 总进度条背景
<div className={styles.timelineBackground}></div>

// 片段标记
{
  segments.map((segment, index) => (
    <div
      key={index}
      className={styles.segmentMarker}
      style={{
        left: `${(segment.startTimeOffset / totalDuration) * 100}%`,
        width: `${(segment.duration / totalDuration) * 100}%`
      }}
      title={`片段 ${index + 1}: ${formatTime(segment.startTimeOffset)} - ${formatTime(segment.startTimeOffset + segment.duration)}`}
    />
  ))
}

// 当前播放进度
<div
  className={styles.progressBar}
  style={{ width: `${(currentTime / totalDuration) * 100}%` }}
></div>

// 进度滑块 - 添加鼠标事件
<div
  ref={thumbRef}
  className={`${styles.seekThumb} ${isDragging ? styles.dragging : ''}`}
  style={{ left: `${(currentTime / totalDuration) * 100}%` }}
  onMouseDown={handleMouseDown}
></div>
  </div >
    </div >
  );
};
*/

const VideoPlayer: React.FC = (number) => {
  // Get current date in yyyy-mm-dd format
  const getCurrentDate = (): string => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const [messageApi, contextHolder] = message.useMessage();
  const [videoList, setVideoList] = useState<VideoTimeSlot[]>([]);
  const [selectedVideo, setSelectedVideo] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // 视频播放模式 - 注释掉，只使用萤石云播放器
  // const [playMode, setPlayMode] = useState<'standard' | 'ezopen'>('standard');
  const [ezOpenUrl, setEzOpenUrl] = useState<string>('');

  // 标准播放器相关状态 - 注释掉，只使用萤石云播放器
  // const [videoSegments, setVideoSegments] = useState<string[]>([]); // 原始视频URL列表
  // const [videoSegmentInfos, setVideoSegmentInfos] = useState<VideoSegmentInfo[]>([]); // 带时间信息的视频片段列表
  // const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0); // 当前播放的片段索引
  // const [seekTime, setSeekTime] = useState(0); // 当前片段中的播放位置（秒）
  const [recordTimeStr, setRecordTimeStr] = useState(''); // 请求的录像时间点
  // const [overallStartTime, setOverallStartTime] = useState<Date | null>(null); // 整体视频开始时间
  // const [overallEndTime, setOverallEndTime] = useState<Date | null>(null); // 整体视频结束时间
  // const [currentPlayTime, setCurrentPlayTime] = useState<Date | null>(null); // 当前播放位置的实际时间

  // const videoNodeRef = useRef<HTMLVideoElement>(null);

  const pageSize = 5; // 每页显示的数量

  // 标准播放器统一时间轴相关状态 - 注释掉，只使用萤石云播放器
  // const [totalDuration, setTotalDuration] = useState<number>(0); // 所有片段总时长（秒）
  // const [currentOverallTime, setCurrentOverallTime] = useState<number>(0); // 当前在整体时间轴上的播放位置（秒）
  // const [segmentTimeMapping, setSegmentTimeMapping] = useState<{
  //   duration: number;
  //   startTimeOffset: number; // 该片段在整体时间轴中的起始偏移（秒）
  // }[]>([]);

  // 标准播放器相关函数 - 注释掉，只使用萤石云播放器
  /*
  // 处理视频URLs，提取开始时间和持续时间信息
  const processVideoUrls = (urls: string[]) => {
    const segmentInfos: VideoSegmentInfo[] = [];

    for (const url of urls) {
      const info = parseVideoInfo(url);
      if (info) {
        segmentInfos.push(info);
      }
    }

    // 按开始时间排序
    segmentInfos.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());

    if (segmentInfos.length > 0) {
      setOverallStartTime(segmentInfos[0].startTime);
      setOverallEndTime(segmentInfos[segmentInfos.length - 1].endTime);
    }

    return segmentInfos;
  };
  */

  // Function to fetch video recording based on selected time slot
  const fetchVideoRecording = async (startTime: string, endTime: string, date: string) => {
    try {
      // Format the datetime strings as required by the API (yyyy-MM-dd HH:mm:ss)
      const timeStart = `${date} ${startTime}`;
      const timeEnd = `${date} ${endTime}`;

      // 设置当前选中的时间
      setRecordTimeStr(timeStart);

      const response = await postRequest('gbs/get_record', {
        // timeStart,
        // timeEnd,
        "timeStart": "2025-05-21 00:00:00",
        "timeEnd": "2025-05-21 02:00:00",
        keyWord: number.gbsId
      });

      if (response.status === 0 && response.data) {
        // Get the URL from the response
        const videoUrlFromResponse = response.data.url || '';

        // 只使用萤石云播放器，检查是否为 ezopen 协议
        if (videoUrlFromResponse.startsWith('ezopen://')) {
          // 萤石云协议，直接设置 ezopen URL
          setEzOpenUrl(videoUrlFromResponse);
          // setPlayMode('ezopen'); // 注释掉，不再需要设置播放模式
        } else if (videoUrlFromResponse) {
          // 标准视频协议 - 注释掉，只使用萤石云播放器
          /*
          try {
            const videoResponse = await axios.get(videoUrlFromResponse, {});
            if (videoResponse && videoResponse.data && videoResponse.data.items) {
              const urls = videoResponse.data.items;
              // Store raw URLs
              setVideoSegments(urls);
              // Process and store video info
              const segmentInfos = processVideoUrls(urls);
              setVideoSegmentInfos(segmentInfos);
              setPlayMode('standard');
            } else {
              messageApi.error('获取视频内容失败');
            }
          } catch (videoError) {
            console.error('Error fetching video content:', videoError);
            messageApi.error('获取视频内容失败，请稍后重试');
          }
          */
          messageApi.warning('当前只支持萤石云 ezopen:// 协议视频播放');
        } else {
          messageApi.error('视频URL不存在');
        }
      } else {
        messageApi.error(response.msg || '获取视频失败');
      }
    } catch (error) {
      console.error('Error fetching video recording:', error);
      messageApi.error('获取视频失败，请稍后重试');
    }
  };

  // Handle video item selection
  const handleVideoSelect = (videoId: number) => {
    setSelectedVideo(videoId);

    const selectedItem = videoList.find(item => item.id === videoId);
    if (selectedItem) {
      fetchVideoRecording(selectedItem.startTime, selectedItem.endTime, selectedItem.date);
    }
  };

  // 标准播放器相关的useEffect和函数 - 注释掉，只使用萤石云播放器
  /*
  // 根据 recordTimeStr 和视频信息计算匹配的视频和 seek 时间
  useEffect(() => {
    if (!recordTimeStr || videoSegmentInfos.length === 0) return;

    const { index, offsetSeconds } = findMatchingIndexAndSeek(
      recordTimeStr,
      videoSegmentInfos
    );

    if (index === -1) {
      console.log('没有找到匹配的视频');
      // 如果没有找到匹配的视频，使用第一个片段
      if (videoSegmentInfos.length > 0) {
        setCurrentSegmentIndex(0);
        setSeekTime(0);
        setCurrentPlayTime(videoSegmentInfos[0].startTime);
      }
    } else {
      setCurrentSegmentIndex(index);
      setSeekTime(offsetSeconds);
      // 设置当前播放位置的实际时间
      const currentTime = new Date(videoSegmentInfos[index].startTime.getTime() + offsetSeconds * 1000);
      setCurrentPlayTime(currentTime);
    }
  }, [recordTimeStr, videoSegmentInfos]);

  // 更新 video 标签的 src、seek 并播放，同时监听播放错误
  useEffect(() => {
    const videoElement = videoNodeRef.current;
    if (!videoElement || videoSegments.length === 0 || !videoSegments[currentSegmentIndex])
      return;

    videoElement.src = videoSegments[currentSegmentIndex];
    videoElement.load();

    const onLoadedMetadata = () => {
      if (seekTime > 0) {
        videoElement.currentTime = seekTime;
      }
      videoElement.play().catch((err) => {
        console.error('播放错误:', err);
      });
    };

    const onError = () => {
      // 检查 video.error，若错误码对应 NotAllowed 或 NotSupported，则尝试下一个片段
      const err = videoElement.error;
      if (err) {
        // 错误码参考：MEDIA_ERR_ABORTED=1, MEDIA_ERR_NETWORK=2, MEDIA_ERR_DECODE=3, MEDIA_ERR_SRC_NOT_SUPPORTED=4
        if (err.code === 3 || err.code === 4) {
          console.error('Video 播放错误:', err);
          // 尝试切换到下一个片段
          if (currentSegmentIndex < videoSegments.length - 1) {
            setCurrentSegmentIndex(prevIndex => prevIndex + 1);
            setSeekTime(0);
            if (videoSegmentInfos[currentSegmentIndex + 1]) {
              setCurrentPlayTime(videoSegmentInfos[currentSegmentIndex + 1].startTime);
            }
          }
        }
      }
    };

    videoElement.addEventListener('loadedmetadata', onLoadedMetadata, { once: true });
    videoElement.addEventListener('error', onError);

    return () => {
      videoElement.removeEventListener('loadedmetadata', onLoadedMetadata);
      videoElement.removeEventListener('error', onError);
    };
  }, [currentSegmentIndex, seekTime, videoSegments, videoSegmentInfos]);

  // 播放进度监听：更新当前播放时间并在视频播放完毕后自动切换到下一个
  useEffect(() => {
    const videoElement = videoNodeRef.current;
    if (!videoElement || videoSegmentInfos.length === 0 || currentSegmentIndex >= videoSegmentInfos.length) return;

    const currentSegmentInfo = videoSegmentInfos[currentSegmentIndex];

    const onTimeUpdate = () => {
      // 更新当前播放位置的实际时间
      const currentSeconds = videoElement.currentTime;
      const realTime = new Date(currentSegmentInfo.startTime.getTime() + currentSeconds * 1000);
      setCurrentPlayTime(realTime);

      // 检查是否需要切换到下一个片段
      if (videoElement.currentTime >= videoElement.duration - 0.5) {
        if (currentSegmentIndex < videoSegments.length - 1) {
          setCurrentSegmentIndex(prevIndex => prevIndex + 1);
          setSeekTime(0);
          if (videoSegmentInfos[currentSegmentIndex + 1]) {
            setCurrentPlayTime(videoSegmentInfos[currentSegmentIndex + 1].startTime);
          }
        } else {
          messageApi.info('已经播放完所有视频');
        }
      }
    };

    videoElement.addEventListener('timeupdate', onTimeUpdate);
    return () => videoElement.removeEventListener('timeupdate', onTimeUpdate);
  }, [videoSegmentInfos, currentSegmentIndex, videoSegments.length]);

  // 格式化时间显示
  const formatDateTime = (date: Date | null): string => {
    if (!date) return '';
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
  };

  useEffect(() => {
    console.log(number, 'number');
    // 请求接口
    const report = async () => {
      const res = await getRequest('report/get_finish_report', {
        taskId: number.number
      });
      console.log(res, 'res');
      if (res.status === 0) {
        if (res.data && res.data.startTime && res.data.endTime) {
          const startDateTime = new Date(res.data.startTime);
          const endDateTime = new Date(res.data.endTime);

          const slots: VideoTimeSlot[] = [];
          let id = 1;

          let currentSlotStart = new Date(startDateTime);

          while (currentSlotStart < endDateTime) {
            let currentSlotEnd = new Date(currentSlotStart);
            currentSlotEnd.setHours(currentSlotEnd.getHours() + 3);

            if (currentSlotEnd > endDateTime) {
              currentSlotEnd = new Date(endDateTime);
            }

            const slotYear = currentSlotStart.getFullYear();
            const slotMonth = String(currentSlotStart.getMonth() + 1).padStart(2, '0');
            const slotDay = String(currentSlotStart.getDate()).padStart(2, '0');
            const slotDateString = `${slotYear}-${slotMonth}-${slotDay}`;

            const formattedStartTime = `${String(currentSlotStart.getHours()).padStart(2, '0')}:${String(currentSlotStart.getMinutes()).padStart(2, '0')}:${String(currentSlotStart.getSeconds()).padStart(2, '0')}`;
            const formattedEndTime = `${String(currentSlotEnd.getHours()).padStart(2, '0')}:${String(currentSlotEnd.getMinutes()).padStart(2, '0')}:${String(currentSlotEnd.getSeconds()).padStart(2, '0')}`;

            slots.push({
              id: id++,
              startTime: formattedStartTime,
              endTime: formattedEndTime,
              title: `施工记录片段 ${id - 1}`,
              date: slotDateString
            });

            currentSlotStart = currentSlotEnd;
          }

          setVideoList(slots);

          // Select the first video by default and fetch it
          if (slots.length > 0) {
            setSelectedVideo(1);
            fetchVideoRecording(slots[0].startTime, slots[0].endTime, slots[0].date);
          }
        }
      } else {
        messageApi.open({
          type: 'error',
          content: res.msg,
        });
      }
    };
    report();
  }, [number]);

  // 计算总页数
  const totalPages = Math.ceil(videoList.length / pageSize);

  // 获取当前页的数据
  const getCurrentPageData = () => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return videoList.slice(start, end);
  };

  // 页码改变处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 计算所有视频片段的总时长和时间映射
  useEffect(() => {
    if (!videoSegmentInfos.length) return;

    let totalSeconds = 0;
    const mappings = videoSegmentInfos.map(segment => {
      const durationInSeconds = segment.duration / 1000; // 毫秒转秒
      const mapping = {
        duration: durationInSeconds,
        startTimeOffset: totalSeconds
      };
      totalSeconds += durationInSeconds;
      return mapping;
    });

    setSegmentTimeMapping(mappings);
    setTotalDuration(totalSeconds);
  }, [videoSegmentInfos]);

  // 更新当前在整体时间轴上的播放位置
  useEffect(() => {
    if (!videoNodeRef.current || !segmentTimeMapping.length || currentSegmentIndex >= segmentTimeMapping.length) return;

    const currentSegmentOffset = segmentTimeMapping[currentSegmentIndex].startTimeOffset;
    const currentTimeInSegment = videoNodeRef.current.currentTime;
    setCurrentOverallTime(currentSegmentOffset + currentTimeInSegment);

    const onTimeUpdate = () => {
      if (!videoNodeRef.current) return;
      const newOverallTime = segmentTimeMapping[currentSegmentIndex].startTimeOffset + videoNodeRef.current.currentTime;
      setCurrentOverallTime(newOverallTime);
    };

    videoNodeRef.current.addEventListener('timeupdate', onTimeUpdate);
    return () => {
      videoNodeRef.current?.removeEventListener('timeupdate', onTimeUpdate);
    };
  }, [currentSegmentIndex, segmentTimeMapping]);

  // 跳转到指定的整体时间点
  const handleSeek = (seekTimeInSeconds: number) => {
    // 找出该时间点对应的视频片段
    for (let i = 0; i < segmentTimeMapping.length; i++) {
      const segment = segmentTimeMapping[i];
      const segmentEndTime = segment.startTimeOffset + segment.duration;

      if (seekTimeInSeconds >= segment.startTimeOffset && seekTimeInSeconds < segmentEndTime) {
        // 找到了对应片段
        const timeInSegment = seekTimeInSeconds - segment.startTimeOffset;

        // 如果是当前片段，直接调整时间
        if (i === currentSegmentIndex && videoNodeRef.current) {
          videoNodeRef.current.currentTime = timeInSegment;
        } else {
          // 切换到新片段并设置时间
          setCurrentSegmentIndex(i);
          setSeekTime(timeInSegment);

          // 如果有时间信息，更新当前播放时间
          if (videoSegmentInfos[i]) {
            const actualTime = new Date(videoSegmentInfos[i].startTime.getTime() + timeInSegment * 1000);
            setCurrentPlayTime(actualTime);
          }
        }

        break;
      }
    }
  };

  // Add a state to track if video is playing
  const [isPlaying, setIsPlaying] = useState(false);

  // Add play/pause toggle handler
  const handlePlayPauseToggle = () => {
    if (videoNodeRef.current) {
      if (videoNodeRef.current.paused) {
        videoNodeRef.current.play()
          .then(() => setIsPlaying(true))
          .catch(err => console.error('Error playing video:', err));
      } else {
        videoNodeRef.current.pause();
        setIsPlaying(false);
      }
    }
  };

  // Update video play state when it changes
  useEffect(() => {
    const videoElement = videoNodeRef.current;
    if (!videoElement) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);
    videoElement.addEventListener('ended', handleEnded);

    return () => {
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, []);
  */

  return (
    <div className={styles.container}>
      {contextHolder}
      {/* 左侧视频播放区域 */}
      <div className={styles.videoSection}>
        {/* 播放模式选择 - 注释掉，只使用萤石云播放器 */}
        {/* <Card size="small" style={{ marginBottom: '16px' }}>
          <Radio.Group
            value={playMode}
            onChange={(e) => setPlayMode(e.target.value)}
            style={{ marginBottom: '8px' }}
          >
            <Radio.Button value="standard">标准播放器</Radio.Button>
            <Radio.Button value="ezopen">萤石云播放器</Radio.Button>
          </Radio.Group>
          {playMode === 'ezopen' && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              萤石云协议: ezopen://open.ys7.com/33010175992677797274:33010042991117112440/1.rec?begin=20250701000000&end=20250702235959
            </div>
          )}
        </Card> */}

        {/* 只使用萤石云播放器 - 标准播放器已注释 */}
        {/* 标准播放器部分 - 已注释
        {playMode === 'standard' ? (
          <>
            <video
              ref={videoNodeRef}
              controls={false} // 关闭默认控制栏，使用自定义控制
              className={styles.videoPlayer}
              autoPlay
              preload="auto"
            >
              <source type="video/mp4" />
              您的浏览器不支持视频播放
            </video>

            // 自定义时间轴
        {videoSegmentInfos.length > 0 && (
          <CustomTimeline
            totalDuration={totalDuration}
            currentTime={currentOverallTime}
            onSeek={handleSeek}
            segments={segmentTimeMapping}
            overallStartTime={overallStartTime}
            videoRef={videoNodeRef}
            isPlaying={isPlaying}
            onPlayPauseClick={handlePlayPauseToggle}
          />
        )}
      </>
      ) : (
        */}

        {/* 萤石云播放器 - 使用测试 URL */}
        <EzVideoPlayer
          ezOpenUrl="ezopen://open.ys7.com/33010175992677797274:33010042991117112440/1.rec?begin=20250701000000&end=20250702235959"
          width="100%"
          height="550px"
          className={styles.videoPlayer}
          style={{
            borderRadius: '8px',
            overflow: 'hidden'
          }}
        />

        {/* 标准播放器条件结束 - 已注释
        )}
        */}

        {/* 基本播放控制按钮 */}
        {/* <div className={styles.videoControls}> */}
        {/* <button 
            onClick={() => videoNodeRef.current?.paused ? videoNodeRef.current?.play() : videoNodeRef.current?.pause()}
            className={styles.playPauseButton}
          >
            {videoNodeRef.current?.paused ? '播放' : '暂停'}
          </button> */}

        {/* 其他控制按钮可按需添加 */}
        {/* </div> */}

        {/* 视频下方信息展示区 */}
        <div className={styles.videoCurrentInfo}>
          <div className={styles.videoName}>{number.deviceCode}</div>
          <div className={styles.videoTimeInfo}>
            {/* 显示整体录像时间范围 */}
            {/* {overallStartTime && overallEndTime && (
              <span>整体时间: {formatDateTime(overallStartTime)} — {formatDateTime(overallEndTime)}</span>
            )} */}
            {/* 显示当前播放位置的实际时间 */}
            {/* {currentPlayTime && (
              <div>当前播放时间: {formatDateTime(currentPlayTime)}</div>
            )} */}
          </div>
        </div>
      </div >

      {/* 右侧视频列表 */}
      < div className={styles.videoList} >
        <h2>视频回放片段列表</h2>
        <div
          className={styles.listContainer}
          style={{
            maxHeight: '550px',
            overflowY: 'auto',
            scrollbarWidth: 'thin',
            paddingRight: '5px'
          }}
        >
          {videoList.map((item) => (
            <div
              key={item.id}
              className={`${styles.videoItem} ${selectedVideo === item.id ? styles.selected : ''}`}
              onClick={() => handleVideoSelect(item.id)}
            >
              <div className={styles.timeAndDate}>
                <span>{item.startTime} — {item.endTime}</span>
                <span className={styles.date}>{item.date}</span>
              </div>
              <div className={styles.title}>{item.title}</div>
            </div>
          ))}
        </div>

        {/* 分页控件 */}
      </div >
    </div >
  );
};

export default VideoPlayer;
