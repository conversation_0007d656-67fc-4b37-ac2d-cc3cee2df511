/*
 * @Author: AI Assistant
 * @Date: 2025-01-01 00:00:00
 * @Description: 三维图表组件，使用 Three.js 实现
 */

import React, { useEffect, useRef, useState } from 'react';
import { Card, Button, Space, Slider, Switch, message } from 'antd';
import { FullscreenOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

// 定义数据点类型
interface DataPoint {
  x: number;
  y: number;
  z: number;
}

interface ThreeDChartProps {
  data?: DataPoint[];
  title?: string;
}

const ThreeDChart: React.FC<ThreeDChartProps> = ({ 
  data = [
    {"x": 0, "y": 0, "z": 0}, 
    {"x": -0.38, "y": 4.58, "z": 1.96}, 
    {"x": -0.76, "y": 9.16, "z": 3.92}, 
    {"x": -1.14, "y": 13.74, "z": 5.88}, 
    {"x": -1.52, "y": 18.32, "z": 7.84}, 
    {"x": -1.9, "y": 22.9, "z": 9.8}, 
    {"x": -2.28, "y": 27.***************, "z": 11.760000000000002}, 
    {"x": -2.****************, "y": 32.059999999999995, "z": 13.720000000000002}, 
    {"x": -3.0399999999999996, "y": 36.63999999999999, "z": 15.680000000000003}, 
    {"x": -3.****************, "y": 41.21999999999999, "z": 17.640000000000004}, 
    {"x": -3.7999999999999994, "y": 45.79999999999999, "z": 19.600000000000005}, 
    {"x": -4.18, "y": 50.37999999999999, "z": 21.56000000000001}, 
    {"x": -4.56, "y": 54.95999999999999, "z": 23.520000000000007}, 
    {"x": -4.9399999999999995, "y": 59.539999999999985, "z": 25.480000000000008}, 
    {"x": -5.319999999999999, "y": 64.11999999999999, "z": 27.44000000000001}, 
    {"x": -5.699999999999999, "y": 68.69999999999999, "z": 29.40000000000001}, 
    {"x": -6.079999999999999, "y": 73.27999999999999, "z": 31.36000000000001}, 
    {"x": -6.459999999999999, "y": 77.85999999999999, "z": 33.32000000000001}, 
    {"x": -6.839999999999999, "y": 82.43999999999998, "z": 35.28000000000001}, 
    {"x": -7.219999999999999, "y": 87.01999999999998, "z": 37.24000000000001}
  ],
  title = "三维轨迹图"
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const controlsRef = useRef<OrbitControls>();
  const frameRef = useRef<number>();
  
  const [isRotating, setIsRotating] = useState(true);
  const [rotationSpeed, setRotationSpeed] = useState(0.01);
  const [showAxes, setShowAxes] = useState(true);
  const [showGrid, setShowGrid] = useState(true);

  // 初始化 Three.js 场景
  const initThreeJS = () => {
    if (!mountRef.current) return;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1a1a1a); // 深色背景
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(50, 50, 50);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // 添加到 DOM
    mountRef.current.appendChild(renderer.domElement);

    // 添加鼠标控制
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.enableZoom = true;
    controls.enableRotate = true;
    controls.enablePan = true;
    controlsRef.current = controls;

    // 添加光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // 添加坐标轴
    if (showAxes) {
      const axesHelper = new THREE.AxesHelper(20);
      scene.add(axesHelper);
    }

    // 添加网格
    if (showGrid) {
      const gridHelper = new THREE.GridHelper(100, 20, 0x444444, 0x222222);
      scene.add(gridHelper);
    }

    // 创建数据点和连线
    createDataVisualization(scene);

    // 开始渲染循环
    animate();
  };

  // 创建数据可视化
  const createDataVisualization = (scene: THREE.Scene) => {
    // 创建材质
    const pointMaterial = new THREE.MeshPhongMaterial({
      color: 0x00ff88,
      shininess: 100,
      transparent: true,
      opacity: 0.8
    });

    const lineMaterial = new THREE.LineBasicMaterial({
      color: 0x0088ff,
      linewidth: 3,
      transparent: true,
      opacity: 0.7
    });

    // 创建起点特殊标记
    const startPointMaterial = new THREE.MeshPhongMaterial({
      color: 0xff0000,
      shininess: 100
    });

    // 创建终点特殊标记
    const endPointMaterial = new THREE.MeshPhongMaterial({
      color: 0x0000ff,
      shininess: 100
    });

    // 创建几何体用于连线
    const lineGeometry = new THREE.BufferGeometry();
    const linePoints: THREE.Vector3[] = [];

    // 计算缩放因子
    const scale = 3;

    // 遍历数据点
    data.forEach((point, index) => {
      // 选择材质
      let material = pointMaterial;
      let radius = 0.3;

      if (index === 0) {
        material = startPointMaterial;
        radius = 0.5; // 起点更大
      } else if (index === data.length - 1) {
        material = endPointMaterial;
        radius = 0.5; // 终点更大
      }

      // 创建球体表示数据点
      const sphereGeometry = new THREE.SphereGeometry(radius, 16, 16);
      const sphere = new THREE.Mesh(sphereGeometry, material);

      // 设置位置（调整坐标映射）
      sphere.position.set(point.x * scale, point.z * scale, point.y * scale);
      sphere.castShadow = true;
      sphere.receiveShadow = true;

      // 添加到场景
      scene.add(sphere);

      // 添加点到连线数组
      linePoints.push(new THREE.Vector3(point.x * scale, point.z * scale, point.y * scale));

      // 添加序号标签（每5个点显示一个）
      if (index % 5 === 0 || index === 0 || index === data.length - 1) {
        // 创建文本精灵
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (context) {
          canvas.width = 64;
          canvas.height = 32;
          context.fillStyle = '#ffffff';
          context.font = '16px Arial';
          context.textAlign = 'center';
          context.fillText(`${index}`, 32, 20);

          const texture = new THREE.CanvasTexture(canvas);
          const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
          const sprite = new THREE.Sprite(spriteMaterial);
          sprite.position.set(point.x * scale, point.z * scale + 2, point.y * scale);
          sprite.scale.set(2, 1, 1);
          scene.add(sprite);
        }
      }
    });

    // 创建连线
    lineGeometry.setFromPoints(linePoints);
    const line = new THREE.Line(lineGeometry, lineMaterial);
    scene.add(line);

    // 添加轨迹管道效果（可选）
    if (linePoints.length > 1) {
      const curve = new THREE.CatmullRomCurve3(linePoints);
      const tubeGeometry = new THREE.TubeGeometry(curve, 64, 0.1, 8, false);
      const tubeMaterial = new THREE.MeshPhongMaterial({
        color: 0x00aaff,
        transparent: true,
        opacity: 0.3
      });
      const tube = new THREE.Mesh(tubeGeometry, tubeMaterial);
      scene.add(tube);
    }
  };

  // 动画循环
  const animate = () => {
    frameRef.current = requestAnimationFrame(animate);

    // 更新控制器
    if (controlsRef.current) {
      controlsRef.current.update();
    }

    if (isRotating && cameraRef.current && sceneRef.current && !controlsRef.current?.enabled) {
      // 只有在控制器未激活时才自动旋转
      const time = Date.now() * rotationSpeed;
      const radius = 80;
      cameraRef.current.position.x = Math.cos(time) * radius;
      cameraRef.current.position.z = Math.sin(time) * radius;
      cameraRef.current.lookAt(0, 0, 0);
    }

    if (rendererRef.current && sceneRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (!mountRef.current || !cameraRef.current || !rendererRef.current) return;

    const width = mountRef.current.clientWidth;
    const height = mountRef.current.clientHeight;

    cameraRef.current.aspect = width / height;
    cameraRef.current.updateProjectionMatrix();
    rendererRef.current.setSize(width, height);
  };

  // 重置视图
  const resetView = () => {
    if (cameraRef.current) {
      cameraRef.current.position.set(50, 50, 50);
      cameraRef.current.lookAt(0, 0, 0);
    }
  };

  // 导出图片
  const exportImage = () => {
    if (rendererRef.current) {
      const link = document.createElement('a');
      link.download = '3d-chart.png';
      link.href = rendererRef.current.domElement.toDataURL();
      link.click();
    }
  };

  // 组件挂载时初始化
  useEffect(() => {
    initThreeJS();
    window.addEventListener('resize', handleResize);

    return () => {
      // 清理资源
      if (frameRef.current) {
        cancelAnimationFrame(frameRef.current);
      }
      if (rendererRef.current && mountRef.current) {
        mountRef.current.removeChild(rendererRef.current.domElement);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 当设置改变时重新初始化
  useEffect(() => {
    if (sceneRef.current) {
      // 清空场景
      while (sceneRef.current.children.length > 0) {
        sceneRef.current.remove(sceneRef.current.children[0]);
      }
      
      // 重新添加光源和辅助元素
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
      sceneRef.current.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(50, 50, 50);
      directionalLight.castShadow = true;
      sceneRef.current.add(directionalLight);

      if (showAxes) {
        const axesHelper = new THREE.AxesHelper(20);
        sceneRef.current.add(axesHelper);
      }

      if (showGrid) {
        const gridHelper = new THREE.GridHelper(100, 20, 0x444444, 0x222222);
        sceneRef.current.add(gridHelper);
      }

      createDataVisualization(sceneRef.current);
    }
  }, [showAxes, showGrid, data]);

  return (
    <Card 
      title={title}
      extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={resetView}
            size="small"
          >
            重置视图
          </Button>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={exportImage}
            size="small"
          >
            导出图片
          </Button>
          <Button 
            icon={<FullscreenOutlined />} 
            size="small"
          >
            全屏
          </Button>
        </Space>
      }
    >
      {/* 控制面板 */}
      <div style={{ marginBottom: 16 }}>
        <Space wrap>
          <span>自动旋转:</span>
          <Switch 
            checked={isRotating} 
            onChange={setIsRotating}
            size="small"
          />
          
          <span>旋转速度:</span>
          <Slider
            min={0.001}
            max={0.05}
            step={0.001}
            value={rotationSpeed}
            onChange={setRotationSpeed}
            style={{ width: 100 }}
          />
          
          <span>显示坐标轴:</span>
          <Switch 
            checked={showAxes} 
            onChange={setShowAxes}
            size="small"
          />
          
          <span>显示网格:</span>
          <Switch 
            checked={showGrid} 
            onChange={setShowGrid}
            size="small"
          />
        </Space>
      </div>

      {/* 三维图容器 */}
      <div 
        ref={mountRef} 
        style={{ 
          width: '100%', 
          height: '600px', 
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden'
        }} 
      />
      
      {/* 说明信息 */}
      <div style={{ marginTop: 16, fontSize: '12px', color: '#666' }}>
        <p>🖱️ <strong>鼠标操作</strong>：左键旋转、右键平移、滚轮缩放</p>
        <p>🎯 <strong>数据点</strong>：红色(起点)、绿色(中间点)、蓝色(终点)</p>
        <p>📊 <strong>坐标轴</strong>：X轴(红)、Y轴(绿)、Z轴(蓝)</p>
        <p>📈 <strong>轨迹</strong>：蓝色线条连接，半透明管道显示路径</p>
        <p>🏷️ <strong>标签</strong>：显示关键点的序号（每5个点一个）</p>
      </div>
    </Card>
  );
};

export default ThreeDChart;
