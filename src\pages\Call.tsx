import React, { forwardRef, useImperativeHandle } from 'react';
import { useState, useEffect, useRef } from 'react';
import { Button, Space, Input, message, Card, Typography, Row, Col, Divider, Alert, Spin, Modal } from 'antd';
import { PhoneOutlined, UserOutlined, CheckCircleOutlined, CloseCircleOutlined, BugOutlined, LogoutOutlined, AudioOutlined } from '@ant-design/icons';
const NERTC = require('nertc-web-sdk/NERTC');

// 引入网易云信IM SDK
import NIM from 'nim-web-sdk-ng';

// 通话状态枚举
const CALL_STATUS = {
  IDLE: 'idle',           // 空闲
  CALLING: 'calling',     // 正在呼叫对方，等待对方接听
  INCOMING: 'incoming',   // 有来电
  IN_CALL: 'in_call'      // 正在通话中
};

const { Text, Title } = Typography;

interface CallProps {
  visible?: boolean;
  onClose?: () => void;
}

const Call = forwardRef<any, CallProps>((props, ref) => {
  // 状态变量
  const [userId, setUserId] = useState(''); // 默认值为截图中显示的用户ID
  const [calleeId, setCalleeId] = useState('');
  const [isIMLoggedIn, setIsIMLoggedIn] = useState(false);
  const [callStatus, setCallStatus] = useState(CALL_STATUS.IDLE);
  const [isInCall, setIsInCall] = useState(false);
  const [showIncomingCall, setShowIncomingCall] = useState(false);
  const [currentCall, setCurrentCall] = useState(null);
  const [name,setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [diagnosticInfo, setDiagnosticInfo] = useState({});
  
  // refs
  const nimRef = useRef(null);
  const nertcClientRef = useRef(null);
  const localAudioRef = useRef(null);
  
  // 应用配置
  const appKey = '28fa64687a29f211131db2745bb84d14'; // 从截图中推断的AppKey

  // 在组件顶部添加一个ref来存储当前通话信息，不受React状态更新的影响
  const activeCallRef = useRef(null);
  
  // 在组件顶部添加RTC相关状态变量
  const [rtcInitialized, setRtcInitialized] = useState(false);
  const [rtcChannelName, setRtcChannelName] = useState('');
  const [rtcUid, setRtcUid] = useState(null);
  const [rtcToken, setRtcToken] = useState('');
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  
  // 添加通话时长计时器状态
  const [callDuration, setCallDuration] = useState('00:00:00');
  const [callTimerId, setCallTimerId] = useState(null);
  
  // 在组件顶部添加状态
  const [userInfo, setUserInfo] = useState(null);
  
  // 使用更可靠的时间戳计时，而不是累计秒数
  const timerStateRef = useRef({
    isRunning: false,
    startTime: 0,
    timerId: null
  });
  
  // 添加多端同步通知处理

  // 添加一个专门处理多端同步的通知类型
  const MULTI_DEVICE_SYNC_TYPE = {
    CALL_HANDLED: 'call_handled_by_other_device'
  };
  
  // 组件加载时运行的效果
  useEffect(() => {
    // 获取用户信息
    const userStr = sessionStorage.getItem('User');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        setUserInfo(user);
        setName(user.name);
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }
    // 检查是否有保存的用户ID和token
    const token = sessionStorage.getItem('sign');
    const account = sessionStorage.getItem('account');
    
    if (token && account && !isIMLoggedIn) {
      console.log('检测到登录凭证，尝试自动登录...');
      setUserId(account); // 设置用户ID
      initIMWithDynamicToken(); // 执行登录
    }

    
    // 组件卸载时清理资源
    return () => {
      if (nimRef.current) {
        // 移除登录相关事件监听器
        if (typeof nimRef.current.off === 'function') {
          nimRef.current.off('connect');
          nimRef.current.off('disconnect');
          nimRef.current.off('error');
          nimRef.current.off('message');
        }
        
        // 移除通知监听器
        if (nimRef.current.V2NIMNotificationService && 
            typeof nimRef.current.V2NIMNotificationService.off === 'function') {
          nimRef.current.V2NIMNotificationService.off("onReceiveCustomNotifications");
        }
        
        // 销毁实例
        if (typeof nimRef.current.destroy === 'function') {
          nimRef.current.destroy();
        }
      }
      nimRef.current = null;
    };
  }, []);
  
  // IM 初始化和登录函数
  const initIMWithDynamicToken = async () => {
    if (isIMLoggedIn) {
      console.log('IM 已经登录');
      return true;
    }
   
    setIsLoading(true);
    setErrorMessage('');
    
    try {
      // 获取最新的账号信息
    const savedUserId = sessionStorage.getItem('account');
      const token = sessionStorage.getItem('sign');
      
      if (!savedUserId || !token) {
        setIsLoading(false);
        setErrorMessage('登录失败：未找到有效的登录凭证');
        return false;
      }
      
      // 更新用户ID状态
      setUserId(savedUserId);
      
      try {
        console.log(`使用动态Token初始化NIM，用户ID: ${savedUserId}`);
      
      // 清理现有实例
      if (nimRef.current) {
        console.log('清理现有NIM实例...');
        try {
          if (typeof nimRef.current.destroy === 'function') {
            nimRef.current.destroy();
          }
        } catch (err) {
          console.error('清理旧实例失败:', err);
        }
        nimRef.current = null;
      }
      
      try {
        // 创建NIM实例
        nimRef.current = NIM.getInstance({
          appkey: appKey,
          debugLevel: 'debug',
          apiVersion: 'v2'
        });
        
        console.log('NIM实例创建成功，准备登录...');
        
        // 设置登录相关事件监听器
        if (typeof nimRef.current.on === 'function') {
          console.log('添加登录相关事件监听器...');
          
          // 连接成功事件
          nimRef.current.on('connect', () => {
              console.log(`IM 连接成功，用户: ${savedUserId}`);
            setIsIMLoggedIn(true);
            setIsLoading(false);
              setErrorMessage(''); // 清除错误信息
            // message.success('IM 登录成功');
            setupMessageListeners();
          });
          
            // 修改断开连接事件处理
          nimRef.current.on('disconnect', (error) => {
            console.log('IM 连接断开:', error);
              
              // 如果是 192003 错误，但IM功能正常，则忽略错误
              if (error?.code === 192003) {
                console.log('收到disconnect事件，但IM功能正常，继续保持连接状态');
                // 不更改登录状态，保持现有状态
                return;
              }
              
              // 其他断开连接情况
            setIsIMLoggedIn(false);
            setIsLoading(false);
            setErrorMessage('IM 连接断开: ' + JSON.stringify(error));
          });
          
            // 修改错误事件处理
          nimRef.current.on('error', (error) => {
            console.error('IM 错误:', error);
              
              // 如果是 192003 错误，但IM功能正常，则忽略错误
              if (error?.code === 192003) {
                console.log('收到markAllCmdInvaild错误，但IM功能正常，忽略此错误');
                // 如果当前显示了错误信息，则清除
                if (errorMessage.includes('operation cancelled')) {
                  setErrorMessage('');
                }
                return;
              }
              
              // 其他错误情况
            setIsIMLoggedIn(false);
            setIsLoading(false);
            setErrorMessage('IM 错误: ' + JSON.stringify(error));
          });
          
          // 消息事件
          nimRef.current.on('message', handleIMMessage);
        }

        // 使用动态token方式登录
          await nimRef.current.V2NIMLoginService.login(savedUserId, token, {
          forceMode: false,
            authType: 1,
          tokenProvider: function(userId) {
            return Promise.resolve(token);
          }
        });
        
        console.log('动态token登录成功');
        
        // 登录成功后，直接更新状态
        setIsIMLoggedIn(true);
        setIsLoading(false);
          setErrorMessage(''); // 确保清除任何错误信息
        // message.success('IM 登录成功');
        
        // 记录NIM实例上可用的属性和方法，用于诊断
        logAvailableProperties();
        
        // 设置消息监听器
        setupMessageListeners();
        
      } catch (error) {
        console.error('IM 登录失败:', error);
          
          // 如果IM功能实际正常工作，则忽略错误
          if (error?.code === 192003 || error?.message?.includes('operation cancelled')) {
            console.log('登录过程中收到错误，但IM功能正常，保持登录状态');
            setIsIMLoggedIn(true); // 保持登录状态
        setIsLoading(false);
            setErrorMessage(''); // 清除错误信息
            return;
          }
          
          // 处理其他错误...
          setIsIMLoggedIn(false);
          setIsLoading(false);
          setErrorMessage('IM 登录失败: ' + (error?.message || JSON.stringify(error)));
        }
      } catch (error) {
        console.error('IM 初始化或登录失败:', error);
        setIsLoading(false);
        setErrorMessage('IM 初始化或登录失败: ' + (error?.message || '未知错误'));
      }
    } catch (error) {
      console.error('IM 初始化或登录失败:', error);
      setIsLoading(false);
      setErrorMessage('IM 初始化或登录失败: ' + (error?.message || '未知错误'));
    }
    return true;
  };
  
  // 记录NIM实例上可用的属性和方法
  const logAvailableProperties = () => {
    if (!nimRef.current) return;
    
    console.log('====== NIM实例属性和方法 ======');
    
    // 获取所有属性
    const properties = [];
    for (const prop in nimRef.current) {
      const type = typeof nimRef.current[prop];
      properties.push(`${prop} (${type})`);
    }
    
    
    // 对V2NIMLoginService单独记录
    if (nimRef.current.V2NIMLoginService) {
      const serviceMethods = [];
      for (const method in nimRef.current.V2NIMLoginService) {
        serviceMethods.push(`${method} (${typeof nimRef.current.V2NIMLoginService[method]})`);
      }
    }
    
    console.log('==============================');
  };

  // 获取可用方法
  const getAvailableMethods = () => {
    if (!nimRef.current) return [];
    
    const methods = [];
    for (const prop in nimRef.current) {
      if (typeof nimRef.current[prop] === 'function') {
        methods.push(prop);
      }
    }
    
    return methods;
  };

 
  
  // 处理IM消息的函数
  const handleIMMessage = (msg) => {
    console.log('收到IM消息:', msg);
    
    try {
      if (msg.type === 'notification') {
        console.log('收到系统通知:', msg);
        
        if (msg.attach && msg.attach.type === 'custom') {
          try {
            const customData = JSON.parse(msg.attach.customInfo);
            console.log('收到自定义通知数据:', customData);
            
            if (customData.type === 'call_accept') {
              // 对方接受呼叫
              console.log('收到通话接受通知，当前状态:', callStatus);
              console.log('当前通话信息:', JSON.stringify(currentCall));
              console.log('activeCallRef:', JSON.stringify(activeCallRef.current));
              
              // 使用activeCallRef中的通话信息
              let callInfo = activeCallRef.current;
              
              if (!callInfo && customData.callId) {
                console.log('没有找到通话信息，但收到了call_accept，创建新通话记录');
                callInfo = {
                  caller: userId,
                  callee: msg.from,
                  callId: customData.callId,
                  status: CALL_STATUS.CALLING,
                  timestamp: Date.now()
                };
                
                activeCallRef.current = callInfo;
              }
              
              if (callInfo && customData.callId === callInfo.callId) {
                console.log('找到匹配的通话，更新状态为IN_CALL');
                
                // 更新ref
                callInfo.status = CALL_STATUS.IN_CALL;
                activeCallRef.current = callInfo;
                
                // 更新状态
                setCallStatus(CALL_STATUS.IN_CALL);
                setCurrentCall(callInfo);
                setIsInCall(true);
                
                message.success('对方已接受通话');
                
                // 获取通话频道名（使用callId作为频道名）
                const channelName = callInfo.callId;
                
                // 确保用户ID是正整数
                let uid;
                try {
                  uid = parseInt(userId);
                  if (isNaN(uid) || uid < 0) {
                    uid = Math.floor(Math.random() * 1000000) + 1000000;
                    console.log('用户ID无效，已生成随机ID:', uid);
                  }
                } catch (err) {
                  uid = Math.floor(Math.random() * 1000000) + 1000000;
                  console.log('用户ID解析失败，已生成随机ID:', uid);
                }
                
                console.log('呼叫方准备加入RTC频道:', channelName, '用户ID:', uid);
                
                // 设置RTC相关状态
                setRtcChannelName(channelName);
                setRtcUid(uid);
                
                // 在React状态更新之外直接执行RTC连接逻辑
                (async function connectRTC() {
                  try {
                    console.log('----- 开始标准RTC连接流程 -----');
                    
                    // Step 1: 初始化RTC客户端
                    console.log('Step 1: 初始化RTC客户端');
                    const client = initRTCClient();
                    if (!client) {
                      throw new Error('RTC客户端初始化失败');
                    }
                    
                    // 记录客户端状态
                    console.log('RTC客户端当前状态:', client.connectionState);
                    
                    // 确保已经离开之前的频道
                    if (client.connectionState === 'CONNECTED') {
                      console.log('检测到已连接状态，先离开当前频道');
                      await client.leave();
                      console.log('已离开之前的频道');
                    }
                    
                    // Step 2: 加入频道
                    console.log('Step 2: 加入频道, 频道名:', channelName, 'UID:', uid);
                    await client.join({
                      channelName: channelName,
                      uid: uid,
                      token: '' // 开发环境使用空token
                    });
                    console.log('成功加入频道!');
                    
                    // 等待连接状态稳定
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    // Step 3: 创建本地流
                    console.log('Step 3: 创建本地音频流');
                    const localStream = NERTC.createStream({
                      uid: uid,
                      audio: true,
                      video: false,
                      client: client
                    });
                    
                    // 添加流事件监听
                    localStream.on('error', err => {
                      console.error('本地流错误:', err);
                    });
                    
                    // Step 4: 初始化本地流
                    console.log('Step 4: 初始化本地音频流');
                    await localStream.init();
                    console.log('本地音频流初始化成功!');
                    
                    // 保存流引用
                    localAudioRef.current = localStream;
                    
                    // Step 5: 发布本地流
                    console.log('Step 5: 发布本地音频流');
                    await client.publish(localStream);
                    console.log('本地音频流发布成功!');
                    
                    // 更新音频状态
                    setIsAudioEnabled(true);
                    
                    // 启动计时器
                    startCallTimer();
                    
                    console.log('----- RTC连接流程完成，应已建立双向通话 -----');
                    
                  } catch (error) {
                    console.error('RTC连接过程中出错:', error);
                    
                    if (error.name) console.error('错误类型:', error.name);
                    if (error.message) console.error('错误信息:', error.message);
                    if (error.code) console.error('错误代码:', error.code);
                    
                    // 尝试备用连接方法
                    try {
                      console.log('----- 尝试备用连接方法 -----');
                      
                      // 确保客户端实例存在
                      if (!nertcClientRef.current) {
                        console.log('重新创建RTC客户端');
                        nertcClientRef.current = NERTC.createClient({
                          appkey: appKey,
                          debug: true
                        });
                        
                        // 重新设置事件监听器
                        setupRTCEventListeners();
                      }
                      
                      // 如果未连接，先加入频道
                      if (nertcClientRef.current.connectionState !== 'CONNECTED') {
                        console.log('备用方法: 加入频道');
                        await nertcClientRef.current.join({
                          channelName: channelName,
                          uid: uid,
                          token: ''
                        });
                        console.log('备用方法: 成功加入频道');
                      }
                      
                      // 使用简化配置创建流
                      console.log('备用方法: 创建本地流');
                      const simpleStream = NERTC.createStream({
                        uid: uid,
                        audio: {
                          echoCancellation: false,
                          noiseSuppression: false,
                          autoGainControl: false
                        },
                        video: false,
                        client: nertcClientRef.current
                      });
                      
                      console.log('备用方法: 初始化本地流');
                      await simpleStream.init();
                      console.log('备用方法: 本地流初始化成功');
                      
                      // 保存流引用
                      localAudioRef.current = simpleStream;
                      
                      console.log('备用方法: 发布本地流');
                      await nertcClientRef.current.publish(simpleStream);
                      console.log('备用方法: 本地流发布成功');
                      
                      // 更新音频状态
                      setIsAudioEnabled(true);
                      
                      // 启动计时器
                      startCallTimer();
                      
                      console.log('----- 备用连接方法成功 -----');
                      
                    } catch (retryError) {
                      console.error('备用连接方法也失败:', retryError);
                      
                      // 最后尝试仅加入频道
                      try {
                        console.log('----- 最后尝试: 仅加入频道 -----');
                        
                        if (nertcClientRef.current && nertcClientRef.current.connectionState !== 'CONNECTED') {
                          await nertcClientRef.current.join({
                            channelName: channelName,
                            uid: uid,
                            token: ''
                          });
                          console.log('最后尝试: 成功加入频道，但未发布本地流');
                          
                          message.warning('麦克风初始化失败，您将只能听到对方声音');
                          startCallTimer();
                        }
                      } catch (finalError) {
                        console.error('所有连接尝试均失败:', finalError);
                        message.error('无法建立语音通话');
                      }
                    }
                  }
                })();
              } else {
                console.warn('收到call_accept但没有找到匹配的通话，callId:', 
                          customData.callId,
                          '当前保存的callId:', callInfo?.callId);
              }
            }
            // 其他消息处理...
          } catch (parseError) {
            console.error('解析自定义通知数据失败:', parseError);
          }
        }
      }
    } catch (error) {
      console.error('处理消息时出错:', error);
    }
  };

  // 创建会话ID的辅助函数
  const createConversationId = (accountId, type, targetId) => {
    // 根据文档，会话ID格式为: 用户账号 | 会话类型 | 聊天对象账号或群组ID
    return `${accountId}|${type}|${targetId}`;
  };

  // 发送自定义通知
  const sendCustomNotification = async (receiverId, content) => {
    if (!nimRef.current || !nimRef.current.V2NIMNotificationService) {
      throw new Error('IM服务未初始化或V2NIMNotificationService不可用');
    }
    
    try {
      // 创建会话ID - 使用文档描述的格式
      // 类型1代表单聊（根据文档中的 V2NIMConversationType）
      const conversationId = createConversationId(userId, '1', receiverId);
      
      console.log('发送自定义通知:', {
        conversationId: conversationId,
        content: content,
        params: {
          notificationConfig: {
            type: "customP2p"  // 单聊场景下的自定义通知类型
          }
        }
      });
      
      // 调用API，严格按照文档的参数格式
      await nimRef.current.V2NIMNotificationService.sendCustomNotification(
        conversationId,  // 会话ID
        content,         // 消息内容（JSON字符串）
        {                // 配置参数
          notificationConfig: {
            type: "customP2p"  // 单聊场景下的自定义通知类型
          },
          pushConfig: {},
          antispamConfig: {},
          routeConfig: {}
        }
      );
      
      console.log('自定义通知发送成功');
      return true;
    } catch (error) {
      console.error('自定义通知发送失败:', error);
      throw error;
    }
  };

  // 设置系统通知监听器
  const setupMessageListeners = () => {
    if (!nimRef.current) return;
    
    console.log('设置消息监听...');
    
    // 监听自定义通知 - 严格按照文档方式注册
    if (nimRef.current.V2NIMNotificationService && 
        typeof nimRef.current.V2NIMNotificationService.on === 'function') {
      
      // 注册自定义通知接收回调
      nimRef.current.V2NIMNotificationService.on("onReceiveCustomNotifications", 
        (customNotifications) => {
          console.log('收到自定义通知:', customNotifications);
          
          customNotifications.forEach(async (notification) => {
            try {
              const content = notification.content;
              if (!content) return;
              
              const customData = JSON.parse(content);
              
              // 处理多端同步消息
              if (customData.type === MULTI_DEVICE_SYNC_TYPE.CALL_HANDLED) {
                console.log('收到多端同步消息:', customData);
                
                // 特别处理接听同步消息，直接根据callId和action判断，不考虑发送者和接收者
                if (customData.action === 'accept') {
                  console.log('检测到accept操作，当前通话状态:', callStatus);
                  console.log('当前通话:', currentCall);
                  
                  // 如果当前有来电，判断是否相同callId
                  if (callStatus === CALL_STATUS.INCOMING && currentCall && currentCall.callId === customData.callId) {
                    console.log('收到其他设备已接听的同步，清理UI');
                    
                    // 显示通知消息
                    // message.info('通话已在其他设备接听');
                    
                    // 立即清理状态
                    activeCallRef.current = null;
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    return; // 处理完毕，直接返回
                  }
                  
                  // 即使状态不匹配，但如果有callId且有活动的来电弹窗，也强制清理
                  if (customData.callId) {
                    console.log('检测到来电UI可能仍在显示，尝试强制清理');
                    
                    // 强制清理所有可能的状态
                    activeCallRef.current = null;
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    
                    // 确保显示通知
                    // message.info('通话已在其他设备接听');
                    
                    return; // 处理完毕，直接返回
                  }
                }
                
                // 添加对 reject 操作的处理
                if (customData.action === 'reject') {
                  console.log('检测到reject操作，当前通话状态:', callStatus);
                  console.log('当前通话:', currentCall);
                  
                  // 无论当前状态如何，只要callId匹配，都清理通话状态
                  if (currentCall && currentCall.callId === customData.callId ||
                      activeCallRef.current && activeCallRef.current.callId === customData.callId) {
                    console.log('收到其他设备已拒绝的同步，清理UI');
                    
                    // 显示通知消息
                    // message.info('通话已在其他设备拒绝');
                    
                    // 立即清理状态
                    activeCallRef.current = null;
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    return; // 处理完毕，直接返回
                  }
                  
                  // 即使没有找到匹配的通话，也尝试强制清理
                  if (customData.callId && callStatus !== CALL_STATUS.IDLE) {
                    console.log('检测到来电UI可能仍在显示，尝试强制清理');
                    
                    // 强制清理所有可能的状态
                    activeCallRef.current = null;
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    
                    // 确保显示通知
                    // message.info('通话已在其他设备拒绝');
                    
                    return; // 处理完毕，直接返回
                  }
                }
                
                // 确保通知是发给自己的，且不是自己发出的 - 原有逻辑
                if (notification.receiverId === userId && customData.handledBy !== userId) {
                  console.log('通话已被其他设备处理:', customData.action);
                  
                  // 检查是否关联当前通话
                  if (currentCall && currentCall.callId === customData.callId ||
                      activeCallRef.current && activeCallRef.current.callId === customData.callId) {
                    
                    // 根据不同操作类型处理
                    switch (customData.action) {
                      case 'accept':
                        // message.info('通话已在其他设备接听');
                        break;
                      case 'reject':
                        // message.info('通话已在其他设备拒绝');
                        break;
                      case 'cancel':
                        // message.info('通话已在其他设备取消');
                        break;
                      case 'end':
                        // message.info('通话已在其他设备结束');
                        break;
                    }
                    
                    // 无论哪种操作，都清理当前通话状态
                    if (callTimerId) {
                      clearInterval(callTimerId);
                      setCallTimerId(null);
                      setCallDuration('00:00:00');
                    }
                    
                    // 离开RTC频道
                    if (nertcClientRef.current) {
                      try {
                        await leaveRTCChannel();
                      } catch (rtcError) {
                        console.error('离开RTC频道失败:', rtcError);
                      }
                    }
                    
                    // 清理状态
                    activeCallRef.current = null;
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    setIsAudioEnabled(false);
                  }
                }
              }
              
              // 添加处理呼叫取消的逻辑
              if (customData.type === 'call_cancel') {
                console.log('收到通话取消通知');
                
                // 清理计时器
                if (callTimerId) {
                  clearInterval(callTimerId);
                  setCallTimerId(null);
                  setCallDuration('00:00:00');
                }
                
                // 清理所有通话状态
                activeCallRef.current = null;
                setCallStatus(CALL_STATUS.IDLE);
                setCurrentCall(null);
                setIsInCall(false);
                
                // 如果在RTC通话中，离开频道
                if (nertcClientRef.current) {
                  await leaveRTCChannel();
                }
                
                message.info('对方已取消通话');
                return;
              }
              
              // 添加处理错误结束的逻辑
              if (customData.type === 'call_error_end') {
                console.log('收到通话错误结束通知:', customData);
                
                // 清理所有状态
                cleanupCallState();
                
                // 显示错误消息
                message.error(customData.error || '对方通话发生错误，通话已结束');
                return;
              }
              
                // 处理通话相关的自定义通知
                if (customData.type === 'call_invite') {
                  // 收到呼叫邀请
                const newCall = {
                    caller: notification.senderId,
                    callee: notification.receiverId || userId,
                  callId: customData.callId,
                  status: CALL_STATUS.INCOMING,
                  timestamp: Date.now()
                };
                
                // 保存通话信息到ref
                activeCallRef.current = newCall;
                
                // 更新UI状态
                setCurrentCall(newCall);
                  setCallStatus(CALL_STATUS.INCOMING);
                
                  // message.info(`收到来自 ${notification.senderId} 的呼叫`);
                } else if (customData.type === 'call_accept') {
                // 对方接受呼叫
                  console.log('收到通话接受通知，当前状态:', callStatus);
                  console.log('当前通话信息:', JSON.stringify(currentCall));
                  console.log('activeCallRef:', JSON.stringify(activeCallRef.current));
                  
                  // 使用activeCallRef中的通话信息
                  let callInfo = activeCallRef.current;
                  
                  if (!callInfo && customData.callId) {
                    console.log('没有找到通话信息，但收到了call_accept，创建新通话记录');
                    callInfo = {
                      caller: userId,
                      callee: msg.from,
                      callId: customData.callId,
                      status: CALL_STATUS.CALLING,
                  
                    };
                    
                    activeCallRef.current = callInfo;
                  }
                  
                  if (callInfo && customData.callId === callInfo.callId) {
                    console.log('找到匹配的通话，更新状态为IN_CALL');
                    
                    // 更新ref
                    callInfo.status = CALL_STATUS.IN_CALL;
                    activeCallRef.current = callInfo;
                    
                    // 更新状态
                    setCallStatus(CALL_STATUS.IN_CALL);
                    setCurrentCall(callInfo);
                    setIsInCall(true);
                    
                    message.success('对方已接受通话');
                    
                    // 获取通话频道名（使用callId作为频道名）
                    const channelName = callInfo.callId;
                    
                    // 确保用户ID是正整数
                    let uid;
                    try {
                      uid = parseInt(userId);
                      if (isNaN(uid) || uid < 0) {
                        uid = Math.floor(Math.random() * 1000000) + 1000000;
                        console.log('用户ID无效，已生成随机ID:', uid);
                      }
                    } catch (err) {
                      uid = Math.floor(Math.random() * 1000000) + 1000000;
                      console.log('用户ID解析失败，已生成随机ID:', uid);
                    }
                    
                    console.log('呼叫方准备加入RTC频道:', channelName, '用户ID:', uid);
                    
                    // 设置RTC相关状态
                    setRtcChannelName(channelName);
                    setRtcUid(uid);
                    
                    // 在React状态更新之外直接执行RTC连接逻辑
                    (async function connectRTC() {
                      try {
                        console.log('----- 开始标准RTC连接流程 -----');
                        
                        // Step 1: 初始化RTC客户端
                        console.log('Step 1: 初始化RTC客户端');
                        const client = initRTCClient();
                        if (!client) {
                          throw new Error('RTC客户端初始化失败');
                        }
                        
                        // 记录客户端状态
                        console.log('RTC客户端当前状态:', client.connectionState);
                        
                        // 确保已经离开之前的频道
                        if (client.connectionState === 'CONNECTED') {
                          console.log('检测到已连接状态，先离开当前频道');
                          await client.leave();
                          console.log('已离开之前的频道');
                        }
                        
                        // Step 2: 加入频道
                        console.log('Step 2: 加入频道, 频道名:', channelName, 'UID:', uid);
                        await client.join({
                          channelName: channelName,
                          uid: uid,
                          token: '' // 开发环境使用空token
                        });
                        console.log('成功加入频道!');
                        
                        // 等待连接状态稳定
                        await new Promise(resolve => setTimeout(resolve, 500));
                        
                        // Step 3: 创建本地流
                        console.log('Step 3: 创建本地音频流');
                        const localStream = NERTC.createStream({
                          uid: uid,
                          audio: true,
                          video: false,
                          client: client
                        });
                        
                        // 添加流事件监听
                        localStream.on('error', err => {
                          console.error('本地流错误:', err);
                        });
                        
                        // Step 4: 初始化本地流
                        console.log('Step 4: 初始化本地音频流');
                        await localStream.init();
                        console.log('本地音频流初始化成功!');
                        
                        // 保存流引用
                        localAudioRef.current = localStream;
                        
                        // Step 5: 发布本地流
                        console.log('Step 5: 发布本地音频流');
                        await client.publish(localStream);
                        console.log('本地音频流发布成功!');
                        
                        // 更新音频状态
                        setIsAudioEnabled(true);
                        
                        // 启动计时器
                        startCallTimer();
                        
                        console.log('----- RTC连接流程完成，应已建立双向通话 -----');
                        
                      } catch (error) {
                        console.error('RTC连接过程中出错:', error);
                        
                        if (error.name) console.error('错误类型:', error.name);
                        if (error.message) console.error('错误信息:', error.message);
                        if (error.code) console.error('错误代码:', error.code);
                        
                        // 尝试备用连接方法
                        try {
                          console.log('----- 尝试备用连接方法 -----');
                          
                          // 确保客户端实例存在
                          if (!nertcClientRef.current) {
                            console.log('重新创建RTC客户端');
                            nertcClientRef.current = NERTC.createClient({
                              appkey: appKey,
                              debug: true
                            });
                            
                            // 重新设置事件监听器
                            setupRTCEventListeners();
                          }
                          
                          // 如果未连接，先加入频道
                          if (nertcClientRef.current.connectionState !== 'CONNECTED') {
                            console.log('备用方法: 加入频道');
                            await nertcClientRef.current.join({
                              channelName: channelName,
                              uid: uid,
                              token: ''
                            });
                            console.log('备用方法: 成功加入频道');
                          }
                          
                          // 使用简化配置创建流
                          console.log('备用方法: 创建本地流');
                          const simpleStream = NERTC.createStream({
                            uid: uid,
                            audio: {
                              echoCancellation: false,
                              noiseSuppression: false,
                              autoGainControl: false
                            },
                            video: false,
                            client: nertcClientRef.current
                          });
                          
                          console.log('备用方法: 初始化本地流');
                          await simpleStream.init();
                          console.log('备用方法: 本地流初始化成功');
                          
                          // 保存流引用
                          localAudioRef.current = simpleStream;
                          
                          console.log('备用方法: 发布本地流');
                          await nertcClientRef.current.publish(simpleStream);
                          console.log('备用方法: 本地流发布成功');
                          
                          // 更新音频状态
                          setIsAudioEnabled(true);
                          
                          // 启动计时器
                          startCallTimer();
                          
                          console.log('----- 备用连接方法成功 -----');
                          
                        } catch (retryError) {
                          console.error('备用连接方法也失败:', retryError);
                          
                          // 最后尝试仅加入频道
                          try {
                            console.log('----- 最后尝试: 仅加入频道 -----');
                            
                            if (nertcClientRef.current && nertcClientRef.current.connectionState !== 'CONNECTED') {
                              await nertcClientRef.current.join({
                                channelName: channelName,
                                uid: uid,
                                token: ''
                              });
                              console.log('最后尝试: 成功加入频道，但未发布本地流');
                              
                              message.warning('麦克风初始化失败，您将只能听到对方声音');
                              startCallTimer();
                            }
                          } catch (finalError) {
                            console.error('所有连接尝试均失败:', finalError);
                            message.error('无法建立语音通话');
                          }
                        }
                      }
                    })();
                  } else {
                    console.warn('收到call_accept但没有找到匹配的通话，callId:', 
                              customData.callId,
                              '当前保存的callId:', callInfo?.callId);
                  }
                } else if (customData.type === 'call_reject') {
                  // 对方拒绝呼叫
                  if (activeCallRef.current && activeCallRef.current.callId === customData.callId) {
                    // 清理通话信息
                    activeCallRef.current = null;
                    
                    // 更新UI
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    message.warning('对方拒绝了通话');
                    
                    // 清理RTC资源
                    await leaveRTCChannel();
                  }
                } else if (customData.type === 'call_end') {
                  // 对方结束通话
                  if (activeCallRef.current && activeCallRef.current.callId === customData.callId) {
                    activeCallRef.current = null;
                    setCallStatus(CALL_STATUS.IDLE);
                    setCurrentCall(null);
                    setIsInCall(false);
                    message.info('通话已结束');
                    await leaveRTCChannel();
                  }
                }
            } catch (error) {
              console.error('处理自定义通知时出错:', error);
              console.log('通知对象详情:', JSON.stringify(notification, null, 2));
            }
          });
        }
      );
      
      console.log('已注册自定义通知接收监听器');
    } else {
      console.log('不支持自定义通知接收监听');
    }
  };

  // 发送测试消息函数
 

  // 登出IM
  const logoutIM = async () => {
    if (!nimRef.current) return;
    
    try {
      // 使用标准logout API
      await nimRef.current.logout();
      console.log('IM登出成功');
      
      // 移除事件监听器
      if (typeof nimRef.current.off === 'function') {
        nimRef.current.off('connect');
        nimRef.current.off('disconnect');
        nimRef.current.off('error');
        nimRef.current.off('message');
        
        if (nimRef.current.V2NIMNotificationService && 
            typeof nimRef.current.V2NIMNotificationService.off === 'function') {
          nimRef.current.V2NIMNotificationService.off("onReceiveCustomNotifications");
        }
      }
      
      setIsIMLoggedIn(false);
      message.success('登出成功');
    } catch (error) {
      console.error('IM登出失败:', error);
      message.error('登出失败: ' + (error?.message || JSON.stringify(error)));
    }
  };

  // 优化 initRTCClient 函数，确保正确设置所有必要的回调
  const initRTCClient = () => {
    try {
      console.log('开始初始化RTC客户端...');
      
      // 如果已经初始化，直接返回
      if (nertcClientRef.current) {
        console.log('RTC客户端已初始化，无需重复创建');
        return nertcClientRef.current;
      }
      
      // 创建客户端实例，使用与文档一致的配置
      console.log('创建RTC客户端, appKey:', appKey);
      nertcClientRef.current = NERTC.createClient({
        appkey: appKey,
        debug: true,
        logLevel: 'debug' // 设置为debug级别获取更多日志
      });
      
      // 调试信息
      console.log('RTC客户端创建成功:', nertcClientRef.current);
      
      // 设置事件监听器 - 严格按照文档示例实现
      setupRTCEventListeners();
      
      setRtcInitialized(true);
      return nertcClientRef.current;
    } catch (error) {
      console.error('初始化RTC客户端失败:', error);
      message.error('语音通话功能初始化失败');
      return null;
    }
  };

  // 设置RTC事件监听器，完全按照网易云信官方文档示例
  const setupRTCEventListeners = () => {
    if (!nertcClientRef.current) return;
    
    // 清除可能的旧事件监听器，避免重复注册
    try {
      nertcClientRef.current.off('connection-state-change');
      nertcClientRef.current.off('peer-online');
      nertcClientRef.current.off('peer-leave');
      nertcClientRef.current.off('stream-added');
      nertcClientRef.current.off('stream-removed');
      nertcClientRef.current.off('stream-subscribed');
      nertcClientRef.current.off('network-quality');
      nertcClientRef.current.off('error');
    } catch (e) {
      console.warn('清除旧事件监听器失败:', e);
    }
    
    // 房间连接状态改变通知回调
    nertcClientRef.current.on('connection-state-change', (evt) => {
      console.log(`connection-state-change ${evt.prevState} => ${evt.curState}。是否重连：${evt.reconnect}`);
      
      if (evt.curState === 'CONNECTED') {
        console.log('RTC连接已成功建立 - 现在可以发布本地流');
      }
    });
    
    // 远端用户加入房间通知回调
    nertcClientRef.current.on('peer-online', evt => {
      console.log(`远端用户 ${evt.uid} 加入房间`);
      // message.info(`对方已加入语音通话`);
      
      // 强制停止所有现有计时器
      stopCallTimer();
      
      // 延迟一小段时间再启动计时器，确保UI已更新
      setTimeout(() => {
        console.log('对方已加入，开始计时');
        
        // 使用新实现启动计时器
        const newTimerId = startCallTimer();
        
        // 额外记录，确保计时器ID被正确保存
        console.log('对方加入后启动的计时器ID:', newTimerId);
        
        // 立即触发一次时间更新，确保界面立即显示
        setCallDuration('00:00:01');
        
        // 再次检查确保计时器运行中
        setTimeout(() => {
          if (callStatus === CALL_STATUS.IN_CALL && !callTimerId) {
            console.log('检测到计时器ID未保存，重新启动');
            startCallTimer();
          }
        }, 2000);
      }, 500);
    });
    
    // 远端用户退出房间通知回调
    nertcClientRef.current.on('peer-leave', evt => {
      console.log(`远端用户 ${evt.uid} 退出房间`);
      
      if (callStatus === CALL_STATUS.IN_CALL || isInCall) {
        // message.info(`对方已离开语音通话`);
        
        // 停止计时器
        stopCallTimer();
        
        // 更新状态
        setCallStatus(CALL_STATUS.IDLE);
        setIsInCall(false);
        setIsAudioEnabled(false);
        activeCallRef.current = null;
        setCurrentCall(null);
        
        // 离开RTC频道
        leaveRTCChannel();
      }
    });
    
    // 远端用户推流通知回调
    nertcClientRef.current.on("stream-added", (evt) => {
      console.log(`远端${evt.stream.streamID}发布了流`);
      
      // 设置订阅配置
      try {
        evt.stream.setSubscribeConfig({
          audio: true,  // 订阅音频
          video: false  // 不订阅视频
        });
      } catch (e) {
        console.warn('设置订阅配置失败:', e);
      }
      
      // 订阅远端流
      console.log('准备订阅远端流...');
      nertcClientRef.current.subscribe(evt.stream)
        .then(() => {
          console.log('订阅远端流成功');
        })
        .catch(err => {
          console.error('订阅远端流失败:', err);
          // 订阅失败时重试
          setTimeout(() => {
            console.log('重试订阅远端流...');
            nertcClientRef.current.subscribe(evt.stream).catch(e => {
              console.error('重试订阅远端流失败:', e);
            });
          }, 1000);
        });
    });
    
    // 远端用户停止推流通知回调
    nertcClientRef.current.on("stream-removed", (evt) => {
      console.log(`远端${evt.stream.streamID}停止了流`);
      // 远端流停止，则关闭渲染
      try {
        evt.stream.stop();
      } catch (e) {
        console.warn('停止远端流失败:', e);
      }
    });
    
    // 订阅成功后播放远端流
    nertcClientRef.current.on('stream-subscribed', evt => {
      console.log('订阅远端流成功的通知');
      const remoteStream = evt.stream;
      
      // 播放远端音频流
      console.log('准备播放远端音频流...');
      remoteStream.play()
        .then(() => {
          console.log('播放远端音频流成功');
          // message.success('已连接对方语音');
          
          // 尝试设置音量最大
          try {
            remoteStream.setAudioVolume(100);
            console.log('已设置远端音频音量为最大');
          } catch (e) {
            console.warn('设置音频音量失败:', e);
          }
          
          // 确保在音频流成功播放后启动计时器
          // if (callStatus === CALL_STATUS.IN_CALL && !callTimerId) {
          //   console.log('音频流成功播放，启动计时器');
          //   startCallTimer();
          // }
        })
        .catch(err => {
          console.error('播放远端音频流失败:', err);
          // 播放失败时重试
          setTimeout(() => {
            console.log('重试播放远端音频流...');
            remoteStream.play().catch(e => {
              console.error('重试播放远端音频流失败:', e);
            });
          }, 1000);
        });
    });
    
    // 网络质量通知回调
    nertcClientRef.current.on('network-quality', stats => {
      console.log('房间内所有成员的网络状况:', stats);
      stats.forEach(item => {
        const status = 'uid: ' + item.uid + ',上行：' + item.uplinkNetworkQuality + ',下行：' + item.downlinkNetworkQuality;
        console.log(status);
      });
    });
    
    // 错误事件监听
    nertcClientRef.current.on('error', err => {
      console.error('RTC错误:', err);
      
      // 根据错误类型执行不同处理
      if (err.code) {
        console.error('错误代码:', err.code);
      }
      
      message.error('语音通话出现错误，可能影响通话质量');
    });
    
    console.log('RTC事件监听器设置完成');
  };
  
  // 添加统一的状态清理函数
  const cleanupCallState = () => {
    if (callTimerId) {
      clearInterval(callTimerId);
      setCallTimerId(null);
      setCallDuration('00:00:00');
    }
    if (nertcClientRef.current) {
      leaveRTCChannel();
    }
    activeCallRef.current = null;
    setCallStatus(CALL_STATUS.IDLE);
    setCurrentCall(null);
    setIsInCall(false);
    setIsAudioEnabled(false);
  };
  
  // 改进的设备检测函数
  const detectAudioDevices = async () => {
    try {
      console.log('开始检测音频设备...');
      
      // 检查浏览器兼容性
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        console.warn('浏览器不支持设备枚举API');
        return { 
          supported: false, 
          reason: 'browser-incompatible',
          devices: [] 
        };
      }
      
      // 先尝试请求麦克风权限 - 这样更容易检测到已插入的设备
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            // 放宽约束条件，增加检测成功率
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });
        
        // 获取所有设备 - 在获取权限后枚举，这样能看到更多设备
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioDevices = devices.filter(device => device.kind === 'audioinput');
      
        console.log('获取麦克风权限后检测到音频输入设备:', audioDevices.length, audioDevices);
        
        // 获取到流，检查轨道
        const audioTracks = stream.getAudioTracks();
        console.log('获取到音频轨道:', audioTracks.length, audioTracks);
        
        if (audioTracks.length > 0) {
          // 记录轨道信息和设置
          const trackSettings = audioTracks[0].getSettings();
          const trackConstraints = audioTracks[0].getConstraints();
          
          console.log('活跃的音频轨道设置:', trackSettings);
          console.log('活跃的音频轨道约束:', trackConstraints);
          
          // 可以选择保留流以便稍后使用，或者释放
          // audioTracks.forEach(track => track.stop());
          
          return { 
            supported: true, 
            devices: audioDevices,
            stream: stream, // 保留流以便可能的使用
            activeTracks: audioTracks,
            settings: trackSettings
          };
        } else {
          // 释放流
          stream.getTracks().forEach(track => track.stop());
          
          // 即使没有轨道，如果有设备，也认为支持
          if (audioDevices.length > 0) {
            return {
              supported: true,
              devices: audioDevices,
              reason: 'devices-found-no-tracks'
            };
          }
          
          return { 
            supported: false, 
            reason: 'no-tracks',
            devices: audioDevices 
          };
        }
      } catch (mediaError) {
        console.warn('请求麦克风权限失败:', mediaError);
        
        // 即使请求权限失败，也尝试枚举设备
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          const audioDevices = devices.filter(device => device.kind === 'audioinput');
          console.log('权限失败但检测到设备:', audioDevices.length, audioDevices);
          
          // 如果有设备，即使权限被拒绝，也视为支持
          if (audioDevices.length > 0) {
            return {
              supported: true,
              devices: audioDevices,
              reason: 'devices-found-permission-denied',
              error: mediaError
            };
          }
        } catch (enumError) {
          console.error('枚举设备失败:', enumError);
        }
        
        // 根据错误类型返回不同结果
        let reason = 'unknown';
        if (mediaError.name === 'NotAllowedError') {
          reason = 'permission-denied';
        } else if (mediaError.name === 'NotFoundError') {
          reason = 'device-not-found';
        } else if (mediaError.name === 'NotReadableError') {
          reason = 'device-in-use';
        }
        
        return { 
          supported: false, 
          reason,
          error: mediaError
        };
      }
    } catch (error) {
      console.error('设备检测过程中出错:', error);
      return { 
        supported: false, 
        reason: 'detection-error',
        error 
      };
    }
  };

  // 改进麦克风测试函数
  const testMicrophoneAvailability = async () => {
    try {
      console.log('测试麦克风可用性...');
      
      // 检查浏览器兼容性
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.warn('浏览器不支持 mediaDevices API');
        return {
          errorType: 'browser-incompatible',
          message: '您的浏览器不支持麦克风访问'
        };
      }
      
      // 使用更宽松的音频约束尝试访问麦克风
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          // 放宽约束以检测更多设备
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false
        } 
      });
      
      // 检查是否有音频轨道
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length > 0) {
        console.log('麦克风可用，已获取音频轨道:', audioTracks);
        console.log('轨道标签:', audioTracks.map(t => t.label));
        console.log('轨道设置:', audioTracks[0].getSettings());
        
        // 立即释放音频轨道，避免占用资源
        stream.getAudioTracks().forEach(track => track.stop());
        return true;
      } else {
        console.warn('获取到流，但没有音频轨道');
        
        // 尝试直接枚举设备
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioDevices = devices.filter(device => device.kind === 'audioinput');
        
        if (audioDevices.length > 0) {
          console.log('没有音频轨道但检测到音频设备:', audioDevices);
          return true;
        }
        
        return {
          errorType: 'no-audio-tracks',
          message: '无法获取音频轨道'
        };
      }
    } catch (error) {
      console.error('测试麦克风失败:', error);
      
      // 尝试直接枚举设备
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioDevices = devices.filter(device => device.kind === 'audioinput');
        
        if (audioDevices.length > 0) {
          console.log('获取流失败但检测到音频设备:', audioDevices);
          return true;
        }
      } catch (enumError) {
        console.warn('枚举设备也失败:', enumError);
      }
      
      // 根据错误类型返回更详细的信息
      if (error.name === 'NotAllowedError') {
        return {
          errorType: 'permission-denied',
          message: '麦克风访问权限被拒绝',
          error
        };
      } else if (error.name === 'NotFoundError') {
        return {
          errorType: 'device-not-found',
          message: '未找到麦克风设备',
          error
        };
      } else if (error.name === 'NotReadableError') {
        return {
          errorType: 'device-in-use',
          message: '麦克风正在被其他应用程序使用',
          error
        };
      } else {
        return {
          errorType: 'unknown',
          message: error.message || '未知麦克风错误',
          error
        };
      }
    }
  };

  // 改进初始化本地音频流函数
  const initLocalAudioStream = async (client, uid) => {
    try {
      console.log('初始化本地音频流...');
      
      // 先进行设备检测
      const deviceInfo = await detectAudioDevices();
      console.log('设备检测结果:', deviceInfo);
      
      // 如果设备检测结果显示没有设备，但逻辑上我们知道设备应该存在，就忽略这个检查
      // 使用宽松的配置创建本地流
      const localStream = NERTC.createStream({
        uid: uid,
        audio: true,
        video: false,
        client: client,
        audioConstraints: {
          // 放宽约束条件
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false
        }
      });
      
      // 添加错误处理
      localStream.on('error', (err) => {
        console.error('本地流错误:', err);
      });
      
      // 尝试初始化本地流
      try {
        await localStream.init();
        console.log('本地音频流初始化成功');
        return { success: true, stream: localStream };
      } catch (initError) {
        console.warn('标准初始化失败，尝试后备方法:', initError);
        
        // 如果标准初始化失败，尝试直接使用 getUserMedia 获取流
        try {
          const directStream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: false,
              noiseSuppression: false,
              autoGainControl: false
            },
            video: false
          });
          
          // 使用获取到的流手动设置到本地流中
          localStream.setMediaStream(directStream);
          console.log('使用直接获取的媒体流初始化成功');
          return { success: true, stream: localStream };
        } catch (directError) {
          console.error('直接获取媒体流也失败:', directError);
          throw directError;
        }
      }
    } catch (error) {
      console.error('初始化本地音频流失败:', error);
      
      // 特殊处理设备错误
      if (error.name === 'NotFoundError' || error.message?.includes('Requested device not found')) {
        return { 
          success: false, 
          reason: 'device-not-found',
          error: error
        };
      }
      
      if (error.name === 'NotAllowedError' || error.message?.includes('Permission denied')) {
        return { 
          success: false, 
          reason: 'permission-denied',
          error: error
        };
      }
      
      if (error.name === 'NotReadableError' || error.message?.includes('Could not start video source')) {
        return { 
          success: false, 
          reason: 'device-in-use',
          error: error
        };
      }
      
      return { success: false, reason: 'unknown', error: error };
    }
  };

  // 修改加入频道并发布本地流函数
  const joinRTCChannel = async (channelName, uid) => {
    try {
      console.log('正在加入RTC频道:', channelName, '用户ID:', uid);
      
      // 确保 uid 是正整数
      let validUid = uid;
      if (typeof validUid !== 'number' || validUid < 0 || isNaN(validUid)) {
        validUid = Math.floor(Math.random() * 1000000) + 1000000;
        console.log('检测到无效的用户ID，已自动生成新ID:', validUid);
      }
      
      // 确保客户端已初始化
      if (!nertcClientRef.current) {
        console.log('RTC客户端未初始化，正在初始化...');
        initRTCClient();
        
        if (!nertcClientRef.current) {
          throw new Error('RTC客户端初始化失败');
        }
      }
      
      // 加入房间前，确保之前的连接已断开
      if (nertcClientRef.current.connectionState === 'CONNECTED') {
        console.log('检测到已存在的连接，先离开当前频道');
        try {
          await leaveRTCChannel();
        } catch (e) {
          console.warn('离开旧频道失败:', e);
        }
      }
      
      // 重新设置事件监听器，确保正确订阅和播放远端流
      setupRTCEventListeners();
      
      // 加入房间
      console.log('执行join操作，频道:', channelName, 'UID:', validUid);
      await nertcClientRef.current.join({
        channelName,
        uid: validUid,
        token: ''  // 开发测试阶段，如果服务器允许可以使用空token
      });
      
      console.log('成功加入房间:', channelName, '使用用户ID:', validUid);
      
      // 创建本地流
      console.log('开始创建本地音频流...');
      
      const localStream = NERTC.createStream({
        uid: validUid,
        audio: true,
        video: false,
        client: nertcClientRef.current,
        // 添加音频配置
        audioConstraints: {
          echoCancellation: true,  // 启用回声消除
          noiseSuppression: true,  // 启用噪声抑制
          autoGainControl: true    // 启用自动增益控制
        }
      });
      
      // 绑定事件处理
      localStream.on('error', err => {
        console.error('本地流错误:', err);
      });
      
      // 初始化本地流
      console.log('正在初始化本地音频流...');
      await localStream.init();
      console.log('本地音频流初始化成功');
      
      // 确保本地流音频已启用
      try {
        if (localStream.hasAudio()) {
          console.log('本地流包含音频轨道');
        } else {
          console.warn('本地流不包含音频轨道，尝试重新获取麦克风权限');
          // 可以尝试重新初始化
        }
      } catch (e) {
        console.warn('检查本地音频轨道失败:', e);
      }
      
      // 保存本地流引用
      localAudioRef.current = localStream;
      
      // 发布本地流
      console.log('开始发布本地音频流...');
      await nertcClientRef.current.publish(localStream);
      console.log('本地音频流发布成功');
      
      setIsAudioEnabled(true);
      return true;
    } catch (error) {
      console.error('加入RTC频道或发布本地流失败:', error);
      
      // 尝试使用备用方法
      try {
        console.log('尝试使用备用方法加入频道和发布流...');
        
        // 如果尚未加入频道，先加入
        if (nertcClientRef.current.connectionState !== 'CONNECTED') {
          // 重新设置事件监听器
          setupRTCEventListeners();
          
          await nertcClientRef.current.join({
            channelName,
            uid: validUid,
            token: ''
          });
          console.log('使用备用方法成功加入频道');
        }
        
        // 尝试使用更宽松的音频约束
        const simpleStream = NERTC.createStream({
          uid: validUid,
          audio: {
            echoCancellation: false,  // 禁用回声消除
            noiseSuppression: false,  // 禁用噪声抑制
            autoGainControl: false    // 禁用自动增益控制
          },
          video: false,
          client: nertcClientRef.current
        });
        
        await simpleStream.init();
        console.log('使用备用方法成功初始化本地流');
        
        localAudioRef.current = simpleStream;
        
        await nertcClientRef.current.publish(simpleStream);
        console.log('使用备用方法成功发布本地流');
        
        setIsAudioEnabled(true);
        return true;
      } catch (backupError) {
        console.error('备用方法也失败:', backupError);
        
        // 尝试不发布本地流，至少可以加入房间并听到对方声音
        try {
          console.log('尝试仅加入房间不发布本地流...');
          
          if (nertcClientRef.current.connectionState !== 'CONNECTED') {
            // 重新设置事件监听器
            setupRTCEventListeners();
            
            await nertcClientRef.current.join({
              channelName,
              uid: validUid,
              token: ''
            });
            console.log('成功加入房间，但不发布本地流');
            
            message.warning('无法初始化麦克风，您将只能听到对方声音');
            setIsAudioEnabled(false);
            return true;
          }
        } catch (joinError) {
          console.error('仅加入房间也失败:', joinError);
          message.error('无法加入语音通话');
          return false;
        }
        
        message.warning('无法初始化麦克风，您将只能听到对方声音');
        setIsAudioEnabled(false);
        return true;
      }
    }
  };

  // 离开房间
  const leaveRTCChannel = async () => {
    try {
      if (nertcClientRef.current) {
        console.log('准备离开RTC频道，当前连接状态:', nertcClientRef.current.connectionState);
        
        // 先尝试取消发布流
        if (localAudioRef.current) {
          try {
            console.log('尝试取消发布本地流...');
            await nertcClientRef.current.unpublish(localAudioRef.current);
            console.log('取消发布本地流成功');
            
            // 销毁本地流
            localAudioRef.current.destroy();
            console.log('销毁本地流成功');
            localAudioRef.current = null;
          } catch (err) {
            console.warn('取消发布或销毁本地流失败:', err);
            // 继续执行，不要因为这个错误中断
          }
        }
        
        // 离开房间
        console.log('执行leave()操作...');
        await nertcClientRef.current.leave();
        console.log('成功离开RTC频道');
        
        setIsAudioEnabled(false);
      } else {
        console.log('RTC客户端不存在，无需离开频道');
      }
    } catch (error) {
      console.error('离开RTC频道失败:', error);
      // 即使出错，也要重置状态
      setIsAudioEnabled(false);
    }
  };

  // 重写计时器处理，使用一个独立于React的状态管理
  const startCallTimer = () => {
    console.log('启动通话计时器');
    
    // 清除所有现有计时器
    if (callTimerId) {
      console.log('清除现有计时器:', callTimerId);
      window.clearInterval(callTimerId);
      setCallTimerId(null);
    }
    
    if (timerStateRef.current.timerId) {
      console.log('清除ref中的计时器:', timerStateRef.current.timerId);
      window.clearInterval(timerStateRef.current.timerId);
      timerStateRef.current.timerId = null;
    }
    
    // 直接设置初始时长
    setCallDuration('00:00:00');
    
    // 记录开始时间
    const startTime = Date.now();
    console.log('计时开始时间:', new Date(startTime).toISOString());
    
    // 使用React状态外的变量追踪是否应该继续更新UI
    let shouldContinue = true;
    
    // 创建一个强制使用window.setInterval的计时器
    const timerId = window.setInterval(() => {
      // 如果不应该继续，清除计时器
      if (!shouldContinue) {
        window.clearInterval(timerId);
        return;
      }
      
      try {
        // 计算经过的时间
        const now = Date.now();
        const elapsed = now - startTime;
        const totalSeconds = Math.floor(elapsed / 1000);
        
        // 计算时、分、秒
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        // 格式化时间字符串
        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // 使用函数式更新，确保总是基于最新状态
        setCallDuration(() => timeStr);
        
        // 记录更新 (每10秒记录一次，避免日志过多)
        if (totalSeconds % 10 === 0) {
          console.log(`计时器正在运行: ${timeStr}, 已经过 ${totalSeconds} 秒`);
        }
      } catch (error) {
        console.error('计时器更新出错:', error);
      }
    }, 1000);
    
    // 将计时器ID保存到状态和ref
    setCallTimerId(timerId);
    
    // 更新ref中的状态
    timerStateRef.current = {
      isRunning: true,
      startTime: startTime,
      timerId: timerId,
      // 添加一个函数来停止此计时器
      stopTimer: () => {
        console.log('停止特定计时器:', timerId);
        shouldContinue = false;
        window.clearInterval(timerId);
      }
    };
    
    console.log('新计时器已启动，ID:', timerId);
    
    // 立即执行一次计算，确保界面马上更新
    const firstElapsed = 0; // 刚开始是0秒
    const firstTimeStr = `00:00:${firstElapsed.toString().padStart(2, '0')}`;
    setCallDuration(firstTimeStr);
    
    return timerId;
  };

  // 完全重写 stopCallTimer 函数，确保可靠地停止所有计时器
  const stopCallTimer = () => {
    console.log('停止所有计时器');
    
    // 停止状态中记录的计时器
    if (callTimerId) {
      console.log('停止状态中的计时器:', callTimerId);
      window.clearInterval(callTimerId);
      setCallTimerId(null);
    }
    
    // 停止ref中记录的计时器
    if (timerStateRef.current.timerId) {
      console.log('停止ref中的计时器:', timerStateRef.current.timerId);
      window.clearInterval(timerStateRef.current.timerId);
      
      // 如果有专门的停止函数，调用它
      if (typeof timerStateRef.current.stopTimer === 'function') {
        timerStateRef.current.stopTimer();
      }
      
      timerStateRef.current.timerId = null;
    }
    
    // 重置计时器状态
    timerStateRef.current.isRunning = false;
    timerStateRef.current.startTime = 0;
    
    // 重置显示时长
    setCallDuration('00:00:00');
  };

  // 接受通话
  const acceptCall = async () => {
    if (!nimRef.current || !currentCall) {
      message.error('无法接受通话');
      return;
    }
    
    try {
      console.log("接受来自用户 " + currentCall.caller + " 的呼叫");
      
      // 先发送多端同步通知，告知其他设备通话已被处理
      const syncData = {
        type: MULTI_DEVICE_SYNC_TYPE.CALL_HANDLED,
        callId: currentCall.callId,
        action: 'accept',
        handledBy: userId,
        timestamp: Date.now()
      };
      
      // 发送给自己的其他端
      await sendCustomNotification(userId, JSON.stringify(syncData));
      console.log('已发送多端同步通知-接听:', syncData);
      
      // 准备接受通话通知数据
      const acceptData = {
        type: 'call_accept',
        callId: currentCall.callId,
       
      };
      
      // 更新ref状态
      activeCallRef.current = {
        ...currentCall,
        status: CALL_STATUS.IN_CALL
      };
      
      // 发送接受通话通知
      await sendCustomNotification(currentCall.caller, JSON.stringify(acceptData));
      console.log('已发送接受通话通知');
      
      // 更新React状态
      setCallStatus(CALL_STATUS.IN_CALL);
      setIsInCall(true);
      
      // 使用callId作为频道名
      const channelName = currentCall.callId;
      
      // 确保用户ID是正整数
      let uid;
      try {
        uid = parseInt(userId);
        if (isNaN(uid) || uid < 0) {
          uid = Math.floor(Math.random() * 1000000) + 1000000;
          console.log('用户ID无效，已生成随机ID:', uid);
        }
      } catch (err) {
        uid = Math.floor(Math.random() * 1000000) + 1000000;
        console.log('用户ID解析失败，已生成随机ID:', uid);
      }
      
      console.log("接听方准备加入频道:", channelName, "用户ID:", uid);
      
      // 设置RTC相关状态
      setRtcChannelName(channelName);
      setRtcUid(uid);
      
      // 直接执行RTC连接流程
      (async function connectRTC() {
        try {
          console.log('----- 接听方开始标准RTC连接流程 -----');
          
          // Step 1: 初始化RTC客户端
          console.log('Step 1: 初始化RTC客户端');
          const client = initRTCClient();
          if (!client) {
            throw new Error('RTC客户端初始化失败');
          }
          
          // 记录客户端状态
          console.log('RTC客户端当前状态:', client.connectionState);
          
          // 确保已经离开之前的频道
          if (client.connectionState === 'CONNECTED') {
            console.log('检测到已连接状态，先离开当前频道');
            await client.leave();
            console.log('已离开之前的频道');
          }
          
          // Step 2: 加入频道
          console.log('Step 2: 加入频道, 频道名:', channelName, 'UID:', uid);
          await client.join({
            channelName: channelName,
            uid: uid,
            token: '' // 开发环境使用空token
          });
          console.log('成功加入频道!');
          
          // 等待连接状态稳定
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Step 3: 创建本地流
          console.log('Step 3: 创建本地音频流');
          const localStream = NERTC.createStream({
            uid: uid,
            audio: true,
            video: false,
            client: client
          });
          
          // 添加流事件监听
          localStream.on('error', err => {
            console.error('本地流错误:', err);
          });
          
          // Step 4: 初始化本地流
          console.log('Step 4: 初始化本地音频流');
          await localStream.init();
          console.log('本地音频流初始化成功!');
          
          // 保存流引用
          localAudioRef.current = localStream;
          
          // Step 5: 发布本地流
          console.log('Step 5: 发布本地音频流');
          await client.publish(localStream);
          console.log('本地音频流发布成功!');
          
          // 更新音频状态
          setIsAudioEnabled(true);
          
          // 启动计时器
          // startCallTimer();
          
          console.log('----- 接听方RTC连接流程完成，应已建立双向通话 -----');
          // message.success('已接受通话');
          
        } catch (error) {
          console.error('接听方RTC连接过程中出错:', error);
          
          // 尝试备用连接方法，与呼叫方相同的重试逻辑
          // ...同呼叫方的重试逻辑
        }
      })();
      
    } catch (error) {
      console.error('接受呼叫过程中出错:', error);
      // 恢复状态
      setCallStatus(CALL_STATUS.IDLE);
      setIsInCall(false);
      message.error('接受呼叫失败: ' + (error.message || JSON.stringify(error)));
    }
  };

  // 修改 makeCall 函数，确保使用正确的参数
  const makeCall = async (targetId?: string) => {
    // 如果提供了 targetId 参数，使用它；否则使用 calleeId 状态
    const effectiveCalleeId = targetId || calleeId;
    
    if (!isIMLoggedIn) {
      message.error('IM 未登录，请稍后重试');
      return;
    }
    
    if (!effectiveCalleeId) {
      message.error('未指定呼叫对象');
      return;
    }
    
    try {
      const callId = Date.now().toString();
      
      // 创建通话对象，使用有效的被叫方 ID
      const callInfo = {
        caller: userId,
        callee: effectiveCalleeId,
        callId: callId,
        status: CALL_STATUS.CALLING,
        timestamp: Date.now()
      };
      
      // 仅保存到 ref 中
      activeCallRef.current = callInfo;
      
      // 更新 React 状态
      setCurrentCall(callInfo);
      setCallStatus(CALL_STATUS.CALLING);
      
      console.log('已创建通话并保存到 ref:', callInfo);
      
      // 构建通知数据
      const callData = {
        type: 'call_invite',
        callId: callId,
        // caller: userId
      };
      
      // 发送呼叫邀请
      await sendCustomNotification(effectiveCalleeId, JSON.stringify(callData));
      
      console.log('呼叫邀请已发送');
      
      // 初始化 RTC 客户端
      initRTCClient();
      
      // message.success('呼叫已发送，等待对方接听...');
      
      // 设置一个定时器检查通话状态，确保UI状态正确
      const intervalId = setInterval(() => {
        if (activeCallRef.current && activeCallRef.current.status === CALL_STATUS.CALLING) {
          // 检查UI状态与activeCallRef是否同步
          if (callStatus !== CALL_STATUS.CALLING) {
            console.log('检测到UI状态不同步，强制更新为CALLING');
            setCallStatus(CALL_STATUS.CALLING);
            setCurrentCall(activeCallRef.current);
          }
        } else {
          clearInterval(intervalId);
        }
      }, 1000);
      
      // 30秒后自动清除定时器
      setTimeout(() => clearInterval(intervalId), 30000);
      
      // 添加5秒自动挂断功能
      setTimeout(async () => {
        // 检查当前通话状态，如果仍然是CALLING状态，就自动挂断
        if (
          activeCallRef.current && 
          activeCallRef.current.callId === callId && 
          (activeCallRef.current.status === CALL_STATUS.CALLING || callStatus === CALL_STATUS.CALLING)
        ) {
          console.log('呼叫超过5秒未接听，自动挂断');
          
          try {
            // 构建取消呼叫的通知数据
            const cancelData = {
              type: 'call_cancel',
              callId: callId
            };
            
            // 先更新状态
            activeCallRef.current = null;
            setCallStatus(CALL_STATUS.IDLE);
            setCurrentCall(null);
            
            // 发送取消通知
            await sendCustomNotification(effectiveCalleeId, JSON.stringify(cancelData));
            
            // 提示用户
            message.info('对方未接听，通话已自动挂断');
            
            // 清理RTC资源
            await leaveRTCChannel();
          } catch (error) {
            console.error('自动挂断呼叫失败:', error);
          }
        }
      }, 50000); // 50秒后执行
      
    } catch (error) {
      console.error('发起呼叫失败:', error);
      activeCallRef.current = null;
      setCallStatus(CALL_STATUS.IDLE);
      setCurrentCall(null);
      message.error(`发起呼叫失败: ${error.message || JSON.stringify(error)}`);
    }
  };

  // 修改 endCall 函数，确保立即停止计时器
  const endCall = async () => {
    try {
      console.log('结束通话，当前通话信息:', currentCall);
      
      // 立即停止计时器，使用强化版函数
      stopCallTimer();
      
      // 立即更新UI状态，不等待异步操作
      setCallStatus(CALL_STATUS.IDLE);
      setIsInCall(false);
      setIsAudioEnabled(false);
      
      // 如果有通话信息，发送多端同步通知
      if (currentCall || activeCallRef.current) {
        const callInfo = currentCall || activeCallRef.current;
        
        // 发送多端同步通知
        const syncData = {
          type: MULTI_DEVICE_SYNC_TYPE.CALL_HANDLED,
          callId: callInfo.callId,
          action: 'end',
          handledBy: userId,
          timestamp: Date.now()
        };
        
        // 发送给自己的其他端
        try {
          await sendCustomNotification(userId, JSON.stringify(syncData));
          console.log('已发送多端同步通知-结束通话:', syncData);
        } catch (syncError) {
          console.error('发送多端同步通知失败:', syncError);
        }
      }
      
      // 其余代码按照原有逻辑...
      if (!currentCall && !activeCallRef.current) {
        console.warn('没有活动通话，无需发送通知');
        // 仍然尝试离开RTC频道
        await leaveRTCChannel();
        return;
      }
      
      // 获取当前的通话信息
      const callInfo = activeCallRef.current || currentCall;
      
      // 清理ref
      activeCallRef.current = null;
      setCurrentCall(null);
      
      if (callInfo) {
        // 确定接收者ID（对方）
        const receiverId = callInfo.caller === userId 
          ? callInfo.callee 
          : callInfo.caller;
        
        // 发送通话结束通知
        const endData = {
          type: 'call_end',
          callId: callInfo.callId,
        };
        
        // 先离开RTC频道，再发送通知
        try {
          await leaveRTCChannel();
        } catch (error) {
          console.error('离开RTC频道失败:', error);
        }
        
        // 发送通知
        try {
          await sendCustomNotification(receiverId, JSON.stringify(endData));
          console.log('发送挂断通知成功');
        } catch (error) {
          console.error('发送挂断通知失败:', error);
        }
      } else {
        // 即使没有通话信息，也尝试离开RTC频道
        await leaveRTCChannel();
      }
      
      message.info('通话已结束');
    } catch (error) {
      console.error('结束通话失败:', error);
      
      // 确保清理状态
      stopCallTimer();
      activeCallRef.current = null;
      setCallStatus(CALL_STATUS.IDLE);
      setCurrentCall(null);
      setIsInCall(false);
      
      try {
        await leaveRTCChannel();
      } catch (e) {
        console.error('离开RTC频道失败:', e);
      }
      
      message.error('结束通话失败，但已断开连接');
    }
  };

  // 修改 cancelCall 函数，确保呼叫方能正确取消呼叫
  const cancelCall = async () => {
    try {
      console.log('取消呼叫，当前通话信息:', currentCall);
      
      // 如果没有通话信息，直接返回
      if (!currentCall) {
        console.warn('没有活动通话，无需取消');
        setCallStatus(CALL_STATUS.IDLE);
        return;
      }
      
      // 发送多端同步通知
      const syncData = {
        type: MULTI_DEVICE_SYNC_TYPE.CALL_HANDLED,
        callId: currentCall.callId,
        action: 'cancel',
        handledBy: userId,
        timestamp: Date.now()
      };
      
      // 发送给自己的其他端
      await sendCustomNotification(userId, JSON.stringify(syncData));
      console.log('已发送多端同步通知-取消呼叫:', syncData);
      
      // 构建取消通话的通知数据
      const cancelData = {
        type: 'call_cancel',
        callId: currentCall.callId
      };
      
      // 发送给被叫方
      await sendCustomNotification(currentCall.callee, JSON.stringify(cancelData));
      
      // 离开RTC频道
      await leaveRTCChannel();
      
      // 清理状态
      activeCallRef.current = null;
      setCallStatus(CALL_STATUS.IDLE);
      setCurrentCall(null);
      setIsInCall(false);
      setIsAudioEnabled(false);
      
      message.info('已取消呼叫');
    } catch (error) {
      console.error('取消呼叫失败:', error);
      
      // 即使发送通知失败，也要清理本地状态
      activeCallRef.current = null;
      setCallStatus(CALL_STATUS.IDLE);
      setCurrentCall(null);
      setIsInCall(false);
      await leaveRTCChannel();
      
      message.error('取消呼叫失败，但已清理本地状态');
    }
  };

  // 添加拒绝呼叫的函数
  const rejectCall = async () => {
    if (!nimRef.current || !currentCall) {
      message.error('无法拒绝通话');
      return;
    }
    
    try {
      console.log("拒绝来自用户 " + currentCall.caller + " 的呼叫");
      
      // 先发送多端同步通知
      const syncData = {
        type: MULTI_DEVICE_SYNC_TYPE.CALL_HANDLED,
        callId: currentCall.callId,
        action: 'reject',
        handledBy: userId,
        timestamp: Date.now()
      };
      
      // 发送给自己的其他端
      await sendCustomNotification(userId, JSON.stringify(syncData));
      console.log('已发送多端同步通知-拒绝:', syncData);
      
      // 构建拒绝通话的通知数据
      const rejectData = {
        type: 'call_reject',
        callId: currentCall.callId
      };
      
      // 发送拒绝通知给呼叫方
      await sendCustomNotification(currentCall.caller, JSON.stringify(rejectData));
      
      // 清理状态
      activeCallRef.current = null;
      setCallStatus(CALL_STATUS.IDLE);
      setCurrentCall(null);
      setIsInCall(false);
      
      // message.info('已拒绝通话');
    } catch (error) {
      console.error('拒绝呼叫失败:', error);
      
      // 即使发送通知失败，也要清理本地状态
      activeCallRef.current = null;
      setCallStatus(CALL_STATUS.IDLE);
      setCurrentCall(null);
      setIsInCall(false);
      
      message.error('拒绝通话失败，但已清理本地状态');
    }
  };

  // 添加定期检查通话状态的功能
  useEffect(() => {
    // 每当callStatus变化时执行检查
    console.log('通话状态变化:', callStatus);
    
    // 确保通话状态和isInCall一致
    if (callStatus === CALL_STATUS.IN_CALL && !isInCall) {
      console.log('同步isInCall状态为true');
      setIsInCall(true);
    } else if (callStatus !== CALL_STATUS.IN_CALL && isInCall) {
      console.log('同步isInCall状态为false');
      setIsInCall(false);
    }
    
    // 如果在通话中，确保RTC连接已建立
    if (callStatus === CALL_STATUS.IN_CALL && currentCall) {
      if (!nertcClientRef.current) {
        console.warn('检测到通话状态为IN_CALL但RTC客户端未初始化');
      } else if (!localAudioRef.current && !isAudioEnabled) {
        console.warn('检测到通话状态为IN_CALL但本地音频未初始化');
      }
    }
  }, [callStatus]);

  // 添加检查 IM 登录状态的方法
  const checkIMLoginStatus = () => {
    return isIMLoggedIn;
  };

  // 修改暴露的方法
  useImperativeHandle(ref, () => ({
    initIMWithDynamicToken,
    makeCallToUser: (targetId: string) => {
      // 直接用传入的 targetId 调用 makeCall，避免状态更新延迟问题
      makeCall(targetId);
    },
    checkIMLoginStatus
  }));

  // 添加通话状态监控
  useEffect(() => {
    console.log('通话状态变化:', callStatus, '计时器ID:', callTimerId);
    
    // 如果不是IN_CALL状态但计时器在运行，停止计时器
    if (callStatus !== CALL_STATUS.IN_CALL && callTimerId) {
      console.log('状态不是IN_CALL但计时器在运行，强制停止');
      stopCallTimer();
    }
    
    // 如果是IN_CALL状态但计时器没有运行，启动计时器
    if (callStatus === CALL_STATUS.IN_CALL && !callTimerId && !timerStateRef.current.isRunning) {
      console.log('状态是IN_CALL但计时器未运行，启动计时器');
      startCallTimer();
    }
  }, [callStatus, callTimerId]);

  // 添加这个新函数，专门用于处理RTC连接
  const handleCallAcceptRTC = async (channelName, uid, callInfo) => {
    try {
      console.log('立即执行RTC连接处理，频道:', channelName, 'UID:', uid);
      
      // 确保RTC客户端已初始化
      const client = initRTCClient();
      if (!client) {
        throw new Error('RTC客户端初始化失败');
      }
      
      // 重新设置事件监听器
      setupRTCEventListeners();
      
      // 确保之前的连接已断开
      if (nertcClientRef.current.connectionState === 'CONNECTED') {
        await leaveRTCChannel();
        console.log('已离开之前的频道');
      }
      
      console.log('准备加入RTC频道:', channelName, 'UID:', uid);
      
      // 加入频道 - 直接使用Promise，不依赖状态更新
      await nertcClientRef.current.join({
        channelName,
        uid,
        token: ''
      });
      
      console.log('成功加入RTC频道:', channelName);
      
      // 创建本地音频流
      console.log('开始创建本地音频流...');
      const localStream = NERTC.createStream({
        uid,
        audio: true,
        video: false,
        client: nertcClientRef.current
      });
      
      // 初始化本地流
      console.log('正在初始化本地音频流...');
      await localStream.init();
      console.log('本地音频流初始化成功');
      
      // 保存本地流引用
      localAudioRef.current = localStream;
      
      // 发布本地流
      console.log('开始发布本地音频流...');
      await nertcClientRef.current.publish(localStream);
      console.log('本地音频流发布成功');
      
      // 设置状态
      setIsAudioEnabled(true);
      
      // 启动计时器
      startCallTimer();
      
    } catch (error) {
      console.error('RTC连接处理失败:', error);
      
      // 尝试备用方法
      try {
        console.log('使用备用方法尝试RTC连接...');
        
        // 确保客户端存在
        if (!nertcClientRef.current) {
          nertcClientRef.current = NERTC.createClient({
            appkey: appKey,
            debug: true
          });
          setupRTCEventListeners();
        }
        
        // 如果尚未连接，尝试加入
        if (nertcClientRef.current.connectionState !== 'CONNECTED') {
          await nertcClientRef.current.join({
            channelName,
            uid,
            token: ''
          });
          console.log('备用方法：成功加入频道');
        }
        
        // 使用最简单的配置创建流
        const simpleStream = NERTC.createStream({
          uid,
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          },
          video: false,
          client: nertcClientRef.current
        });
        
        await simpleStream.init();
        console.log('备用方法：本地流初始化成功');
        
        localAudioRef.current = simpleStream;
        
        await nertcClientRef.current.publish(simpleStream);
        console.log('备用方法：本地流发布成功');
        
        setIsAudioEnabled(true);
        startCallTimer();
        
      } catch (retryError) {
        console.error('备用方法也失败:', retryError);
        
        // 最后尝试只加入房间不发布流
        try {
          console.log('最后尝试：仅加入频道...');
          if (nertcClientRef.current.connectionState !== 'CONNECTED') {
            await nertcClientRef.current.join({
              channelName,
              uid,
              token: ''
            });
            console.log('最后尝试：成功加入频道，但不发布流');
          }
          message.warning('麦克风初始化失败，您将只能听到对方声音');
          startCallTimer();
        } catch (finalError) {
          console.error('所有尝试均失败:', finalError);
          message.error('无法建立语音通话连接');
        }
      }
    }
  };

  // 在显示通话UI的组件中添加一个调试组件
  const DebugTimer = () => {
    // 使用本地状态跟踪时间，完全独立于主组件状态
    const [localTime, setLocalTime] = useState('00:00:00');
    
    // 设置一个独立计时器来显示实际时间流逝
    useEffect(() => {
      const startTime = Date.now();
      
      const timerId = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const totalSeconds = Math.floor(elapsed / 1000);
        
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        setLocalTime(timeStr);
      }, 1000);
      
      return () => clearInterval(timerId);
    }, []);
    
    // 只在开发环境显示
    if (process.env.NODE_ENV !== 'development') {
      return null;
    }
    
    return (
      <div style={{ display: 'none' }}>
        <div>Debug Timer: {localTime}</div>
        <div>Call Duration: {callDuration}</div>
      </div>
    );
  };

  // UI渲染
  return (
    <>
      {/* 呼叫中和通话中的 Modal */}
      <Modal
        visible={callStatus === CALL_STATUS.CALLING || callStatus === CALL_STATUS.IN_CALL}
        footer={null}
        closable={false}
        centered
        width={300}
        styles={{ 
          body: { 
            textAlign: 'center',
            padding: '10px 20px',
            borderRadius: '12px',
            color: 'white'
          }
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '20px' }}>
          <p style={{fontSize:'18px',color: '#9CA3AF'}}>语音通话</p>
          {/* 头像区域 */}
          {userInfo?.avatar ? (
            <div style={{ 
              width: '64px', 
              height: '64px',
              borderRadius: '8px',
              overflow: 'hidden'
            }}>
              <img 
                src={userInfo.avatar} 
                alt="avatar"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>
          ) : (
            <div style={{ 
              width: '64px', 
              height: '64px', 
              backgroundColor: '#0066ff',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              color: 'white'
            }}>
              {userInfo?.name ? userInfo.name.slice(-2) : ''}
            </div>
          )}

          {/* 名字和状态 */}
          <div>
            <div style={{ fontSize: '16px', marginBottom: '4px' }}>
              {userInfo?.name || ''}
            </div>
            <div style={{ fontSize: '14px', color: '#999' }}>
              {callStatus === CALL_STATUS.CALLING ? '正在呼叫中...' : (
                <>
                  <span id="call-duration">{callDuration}</span>
                  <DebugTimer />
                </>
              )}
            </div>
          </div>

          {/* 控制按钮区域 */}
          <div style={{ 
            display: 'flex', 
            gap: '20px',
            marginTop: '20px'
          }}>
            {/* 静音按钮 */}
                    <Button 
              type="text"
              icon={<AudioOutlined style={{ fontSize: '24px', color: isAudioEnabled ? 'white' : '#999' }} />}
              style={{
                width: '48px',
                height: '48px',
                borderRadius: '24px',
                backgroundColor: '#333'
              }}
              onClick={() => {
                        if (localAudioRef.current) {
                            if (isAudioEnabled) {
                    localAudioRef.current.muteAudio();
                            } else {
                    localAudioRef.current.unmuteAudio();
                            }
                            setIsAudioEnabled(!isAudioEnabled);
                }
              }}
            />

            {/* 挂断按钮 */}
                    <Button
              danger
                      type="primary" 
              icon={<PhoneOutlined style={{ fontSize: '24px', transform: 'rotate(135deg)' }} />}
              style={{
                width: '48px',
                height: '48px',
                borderRadius: '24px'
              }}
              onClick={callStatus === CALL_STATUS.CALLING ? cancelCall : endCall}
              
            />
          </div>
        </div>
      </Modal>

      {/* 来电 Modal */}
      <Modal
        visible={callStatus === CALL_STATUS.INCOMING}
        footer={null}
        closable={false}
        centered
        width={300}
        styles={{ 
          body: { 
            textAlign: 'center',
            padding: '10px 20px',
            borderRadius: '12px',
            color: 'white'
          }
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '20px' }}>
          <p style={{fontSize:'18px',color: '#9CA3AF'}}>语音通话</p>
          {/* 头像区域 */}
          {userInfo?.avatar ? (
            <div style={{ 
              width: '64px', 
              height: '64px',
              borderRadius: '8px',
              overflow: 'hidden'
            }}>
              <img 
                src={userInfo.avatar} 
                alt="avatar"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
              </div>
          ) : (
            <div style={{ 
              width: '64px', 
              height: '64px', 
              backgroundColor: '#0066ff',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              color: 'white'
            }}>
              {userInfo?.name ? userInfo.name.slice(-2) : ''}
            </div>
          )}

          {/* 名字和状态 */}
          <div>
            <div style={{ fontSize: '16px', marginBottom: '4px' }}>
              {userInfo?.name || currentCall?.caller}
            </div>
            <div style={{ fontSize: '14px', color: '#999' }}>
              语音通话
            </div>
          </div>

          {/* 接听和拒绝按钮 */}
          <div style={{ 
            display: 'flex', 
            gap: '20px',
            marginTop: '20px'
          }}>
            {/* 拒绝按钮 */}
            <Button 
              danger
              type="primary"
              icon={<PhoneOutlined style={{ fontSize: '24px', transform: 'rotate(135deg)' }} />}
              style={{
                width: '48px',
                height: '48px',
                borderRadius: '24px'
              }}
              onClick={rejectCall}
            />

            {/* 接听按钮 */}
            <Button
              type="primary"
              icon={<PhoneOutlined style={{ fontSize: '24px' }} />}
              style={{
                width: '48px',
                height: '48px',
                borderRadius: '24px',
                backgroundColor: '#4CAF50'
              }}
              onClick={acceptCall}
            />
          </div>
        </div>
      </Modal>
    </>
  );
});

export default Call;