/**
 * 分机号管理相关类型定义
 */

// 分机号数据项接口 - 根据新API接口规范定义
export interface ExtensionItem {
  id: number;                    // ID
  account: string;               // 账号
  password: string;              // 密码
  bindCode: string | null;       // 关联code
  bindType: number | null;       // 绑定类型：1-设备 2-用户
  status: number;                // 状态：0-正常 1-禁用
  corpId: string;                // 客户id
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}

// 表单数据接口
export interface ExtensionFormData {
  account: string;               // 账号
  password: string;              // 密码
  bindCode?: string;             // 关联code
  bindType?: number;             // 绑定类型：1-设备 2-用户
  status?: number;               // 状态：0-正常 1-禁用
}

// API响应接口
export interface ApiResponse<T = any> {
  status: number;                // 状态码
  msg: string;                   // 消息
  data: T;                       // 数据
}

// 列表查询参数接口
export interface ExtensionListParams {
  page?: number;                 // 页码，默认值：1
  perPage?: number;              // 页数，默认值：10
  account?: string;              // 账号模糊查询
}

// 列表响应数据接口
export interface ExtensionListResponse {
  items: ExtensionItem[];        // 分机号列表
  total: number;                 // 总条数
  page: number;                  // 页码
}
