import {
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Breadcrumb, Tag } from 'antd';
import React, { useState, useRef } from 'react';
import { postRequest } from '@/services/api/api';

const { RangePicker } = DatePicker;

export type TableListItem = {
    key: number;
    userName: string;
    assets: string;
    ip: string;
    createdAt: string;
    movement: number;
    module: string;
    changeData?: string;
};

interface ChangeItem {
    newValue: string;
    oldValue: string;
}

interface ChangeData {
    [key: string]: ChangeItem;
}

export default () => {
    const ref = useRef<any>();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [currentChangeData, setCurrentChangeData] = useState<ChangeData>({});
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        timeStart: '',
        timeEnd: '',
        timer: undefined
    });

    const showModal = (record: TableListItem) => {
        try {
            if (record.changeData) {
                const changeData = JSON.parse(record.changeData) as ChangeData;
                setCurrentChangeData(changeData);
                setIsModalOpen(true);
            }
        } catch (error) {
            messageApi.error('解析变更数据失败');
        }
    };

    const handleCancel = () => {
        setIsModalOpen(false);
        setCurrentChangeData({});
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '用户',
            dataIndex: 'userName',
        },
        {
            title: '资源',
            dataIndex: 'assets',
        },
        {
            title: '远端地址',
            dataIndex: 'ip',
        },
        {
            title: '日期',
            dataIndex: 'createdAt',
        },
        {
            title: '动作',
            dataIndex: 'movement',
            render: (_, record) => {
                let color = 'blue';
                let text = '创建';

                switch (record.movement) {
                    case 1:
                        color = 'orange';
                        text = '修改';
                        break;
                    case 2:
                        color = 'red';
                        text = '删除';
                        break;
                    default:
                        color = 'blue';
                        text = '创建';
                }

                return <Tag color={color}>{text}</Tag>;
            }
        },
        {
            title: '资源模块',
            dataIndex: 'module',
        },
        {
            title: '操作',
            key: 'option',
            width: 120,
            valueType: 'option',
            render: (_, record) => {
                return record.changeData ? (
                    <a key="link" onClick={() => showModal(record)}>详情</a>
                ) : null;
            }
        },
    ];

    const detailColumns = [
        {
            title: '变更字段',
            dataIndex: 'field',
            key: 'field',
        },
        {
            title: '变更前',
            dataIndex: 'oldValue',
            key: 'oldValue',
            render: (text: string) => {
                try {
                    const value = JSON.parse(text);
                    return JSON.stringify(value, null, 2);
                } catch {
                    return text;
                }
            }
        },
        {
            title: '变更后',
            dataIndex: 'newValue',
            key: 'newValue',
            render: (text: string) => {
                try {
                    const value = JSON.parse(text);
                    return JSON.stringify(value, null, 2);
                } catch {
                    return text;
                }
            }
        },
    ];

    const detailData = Object.entries(currentChangeData).map(([field, values], index) => ({
        key: index,
        field,
        oldValue: values.oldValue,
        newValue: values.newValue
    }));

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '审计日志', },
                ]}
            />
            <ProTable<TableListItem>
                actionRef={ref}
                columns={columns}
                request={async (params) => {
                    let postData = {
                        page: params.current,
                        perPage: params.pageSize,
                        keyWord: queryForm.keyWord,
                        dateStart: queryForm.timeStart,
                        dateEnd: queryForm.timeEnd
                    }
                    try {
                        const result = await postRequest('auditLog/get_ls', postData);
                        const { data, status, msg } = result as any;
                        if (status === 0) {
                            return {
                                data: data.items,
                                total: data.total,
                                success: true,
                            };
                        } else {
                            messageApi.error(msg);
                            return {
                                data: [],
                                total: 0,
                                success: false,
                            };
                        }
                    } catch (error) {
                        messageApi.error('获取数据失败');
                        return {
                            data: [],
                            total: 0,
                            success: false,
                        };
                    }
                }}
                toolbar={{
                    search: <Flex>
                        <Input style={{ width: '250px' }}
                            allowClear
                            value={queryForm.keyWord}
                            onChange={(e) => {
                                setQueryForm({ ...queryForm, keyWord: e.target.value })
                                ref.current?.reload();
                            }}
                            placeholder="搜索" />
                        <RangePicker
                            style={{ width: '320px', marginLeft: '6px' }}
                            onChange={(dates) => {
                                if (dates) {
                                    setQueryForm({
                                        ...queryForm,
                                        timeStart: dates[0]?.format('YYYY-MM-DD') || '',
                                        timeEnd: dates[1]?.format('YYYY-MM-DD') || ''
                                    });
                                    ref.current?.reload();
                                } else {
                                    setQueryForm({
                                        ...queryForm,
                                        timeStart: '',
                                        timeEnd: ''
                                    });
                                    ref.current?.reload();
                                }
                            }}
                        />
                    </Flex>
                }}
                rowKey="key"
                search={false}
                style={{ marginTop: '16px' }}
            />
            <Modal
                title="变更详情"
                open={isModalOpen}
                footer={null}
                onCancel={handleCancel}
                width={800}
            >
                <Table
                    columns={detailColumns}
                    dataSource={detailData}
                    pagination={false}
                    scroll={{ x: 'max-content' }}
                />
            </Modal>
        </>
    );
};