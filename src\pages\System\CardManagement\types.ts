/**
 * 卡片管理相关类型定义
 */

// 卡片数据项
export interface CardItem {
  id: number;                    // 卡片ID
  cardNumber: string;            // 卡片编号
  cardStatus: number;            // 卡片状态：0-禁用，1-启用，2-丢失，3-损坏
  employeeId?: number;           // 绑定员工ID
  employeeName?: string;         // 绑定员工姓名
  employeeAccount?: string;      // 绑定员工账号
  bindTime?: string;             // 绑定时间
  unbindTime?: string;           // 解绑时间
  bindOperatorAccount?: string;  // 绑定操作员账号
  bindOperatorName?: string;     // 绑定操作员姓名
  remark?: string;               // 备注信息
  corpId: string;                // 客户组织ID
  createdAt: string;             // 创建时间
  updatedAt: string;             // 更新时间
}

// 卡片表单数据
export interface CardFormData {
  cardNumber: string;            // 卡片编号
  cardStatus?: number;           // 卡片状态：0-禁用，1-启用，2-丢失，3-损坏
  employeeId?: number;           // 绑定员工ID
  employeeName?: string;         // 绑定员工姓名
  employeeAccount?: string;      // 绑定员工账号
  remark?: string;               // 备注信息
}

// 卡片列表查询参数
export interface CardListParams {
  page?: number;                 // 页码
  perPage?: number;              // 每页数量
  cardNumber?: string;           // 卡片编号模糊查询
  cardStatus?: number;           // 卡片状态筛选
  employeeName?: string;         // 员工姓名模糊查询
}

// 卡片列表响应数据
export interface CardListResponse {
  items: CardItem[];             // 数据列表
  total: number;                 // 总数
  page: number;                  // 当前页码
}

// API响应格式
export interface ApiResponse<T> {
  status: number;                // 状态码：0-成功 其他-失败
  msg: string;                   // 消息
  data: T;                       // 数据
}

// 卡片状态枚举
export const CardStatusEnum = {
  DISABLED: 0,    // 禁用
  ENABLED: 1,     // 启用
  LOST: 2,        // 丢失
  DAMAGED: 3,     // 损坏
} as const;

// 卡片状态标签映射
export const CardStatusLabels = {
  [CardStatusEnum.DISABLED]: '禁用',
  [CardStatusEnum.ENABLED]: '启用',
  [CardStatusEnum.LOST]: '丢失',
  [CardStatusEnum.DAMAGED]: '损坏',
} as const;

// 卡片状态颜色映射
export const CardStatusColors = {
  [CardStatusEnum.DISABLED]: 'default',
  [CardStatusEnum.ENABLED]: 'green',
  [CardStatusEnum.LOST]: 'orange',
  [CardStatusEnum.DAMAGED]: 'red',
} as const;
