/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\GoalAnalysisChart.tsx
 * @Description: 目标达成分析图表组件 - 使用G2进度条图表展示计划总进尺与实际总进尺的对比
 */

import { Card, Empty } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';
import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';
import styles from '../newTaskDashboard.less';

interface GoalAnalysisChartProps {
  // 可以接收外部数据，如果不传则使用默认模拟数据
  goalAnalysisData?: Array<{
    name: string;
    value: number;
  }>;
}

const GoalAnalysisChart: React.FC<GoalAnalysisChartProps> = ({ goalAnalysisData }) => {
  const goalAnalysisRef = useRef<HTMLDivElement>(null);

  // 默认数据 - 使用0值
  const defaultData = [
    { name: '总进尺', value: 0 },
    { name: '实际进尺', value: 0 },
  ];

  const chartData = goalAnalysisData || defaultData;

  // 判断是否有有效数据
  const hasValidData = chartData.some(item => item.value > 0);

  // G2图表渲染函数 - 目标达成分析（进度条样式）
  const renderGoalAnalysisChart = () => {
    if (!goalAnalysisRef.current) return;

    // 准备进度条数据 - 按照图片中的顺序
    const progressData = [
      { name: '总进尺', value: chartData[0]?.value || 0, category: '总进尺', order: 2 },
      { name: '实际进尺', value: chartData[1]?.value || 0, category: '实际进尺', order: 1 },
    ];

    const chart = new Chart({
      container: goalAnalysisRef.current,
      autoFit: true,
      height: 150,
      theme: 'dark',
    });

    // 计算Y轴的最大值，确保图表有合适的显示范围
    const maxValue = Math.max(...progressData.map(d => d.value));
    const yAxisMax = maxValue > 0 ? maxValue * 1.2 : 100; // 如果数据都为0，设置默认最大值

    chart
      .interval()
      .data(progressData)
      .encode('x', 'category')
      .encode('y', 'value')
      .encode('color', 'category')
      .scale('color', { range: ['#1e40af', '#00b042'] })
      .scale('x', {
        type: 'band',
        paddingInner: 0.8,
        paddingOuter: 0,
      })
      .scale('y', {
        domainMin: 0,
        domainMax: yAxisMax,
      })
      .coordinate({ transform: [{ type: 'transpose' }] })
      .axis('x', {
        title: false,
        labelStyle: { fill: '#fff', fontSize: 14 },
        line: false,
        tick: false,
      })
      .axis('y', {
        title: false,
        labelStyle: { fill: '#fff', fontSize: 12 },
        line: false,
        tick: false,
        grid: false,
      })
      .label({
        text: (d: any) => {
          // 当数值为0或很小时，不显示标签，避免与分类标签重叠
          if (d.value === 0 || d.value < 0.1) return '';

          // 计算数值相对于最大值的比例
          const maxValue = Math.max(...progressData.map(item => item.value));
          const ratio = d.value / maxValue;

          // 对于小数值，在标签前添加空格来增加偏移
          const padding = ratio < 0.2 ? '        ' : '  '; // 小数值添加更多空格
          return `${padding}${d.value.toLocaleString()} 米`;
        },
        position: 'right',
        style: {
          fill: '#fff',
          fontSize: 14,
          textAlign: 'left', // 左对齐，配合空格偏移
        },
      })
      .style({
        radius: 4,
        height: 10,
      });

    chart.render();
    return () => chart.destroy();
  };

  // 计算目标完成率 - 基于总进尺和实际进尺
  const calculateCompletionRate = () => {
    const totalFootage = chartData[0]?.value || 0;  // 总进尺
    const actualFootage = chartData[1]?.value || 0; // 实际进尺

    // 如果总进尺为0，避免除零错误
    if (totalFootage === 0) {
      return {
        completionRate: '0.0%',
        deviation: '0.0%',
      };
    }

    const completionRate = ((actualFootage / totalFootage) * 100).toFixed(1);
    const deviation = (((actualFootage - totalFootage) / totalFootage) * 100).toFixed(1);

    return {
      completionRate: `${completionRate}%`,
      deviation: `${deviation}%`,
    };
  };

  const { completionRate, deviation } = calculateCompletionRate();

  useEffect(() => {
    const cleanup = renderGoalAnalysisChart();
    return cleanup;
  }, [chartData]);

  return (
    <Card
      title="目标达成分析"
      bordered={false}
      className={styles.chartCard}
      headStyle={{
        fontSize: '22px',
        fontWeight: 'normal',
        fontFamily: 'PingFang SC',
        color: '#ffffff',
      }}
    >
      <div style={{ padding: '0px' }}>
        {hasValidData ? (
          <>
            {/* G2进度条图表容器 */}
            <div ref={goalAnalysisRef} style={{ height: '150px', marginBottom: '110px' }}></div>

            {/* 目标完成率显示 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ textAlign: 'left' }}>
                <div style={{ color: '#fff', fontSize: '12px', marginBottom: '4px' }}>
                  实际完成率
                </div>
                <div style={{ color: '#1E40AF', fontSize: '24px', fontWeight: 'bold' }}>
                  {completionRate}
                </div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ color: '#fff', fontSize: '12px', marginBottom: '4px' }}>
                  进尺偏差率
                </div>
                <div style={{ color: '#FF5219', fontSize: '24px', fontWeight: 'bold' }}>
                  {deviation}
                </div>
              </div>
            </div>
          </>
        ) : (
          /* 无数据状态 */
          <div style={{ height: '260px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Empty
              image={<BarChartOutlined style={{ fontSize: 48, color: '#666' }} />}
              description={
                <span style={{ color: '#999', fontSize: '14px' }}>
                  暂无目标达成数据
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default GoalAnalysisChart;
