
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';
import { message, Button, Image, QRCode, Row, Col, Input, Form } from 'antd';
import React, { useState, useEffect } from 'react';
import { createStyles } from 'antd-style';
import * as dd from "dingtalk-jsapi";
import loginBackground from '../../../../public/icons/login-background.png'
import { flushSync } from 'react-dom';   
// import { getRequest, postRequest } from '@/services/api/api';



const useStyles = createStyles(({ token }) => {

  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage: loginBackground
    },
  };
});



const Login: React.FC = () => {
  const { styles } = useStyles();
  const { location } = history;
  const { initialState, setInitialState } = useModel('@@initialState');
  const [messageApi, contextHolder] = message.useMessage();

  const [type, setType] = useState(0);
  const [code, setCode] = useState('111');
  // const [account, setAccount] = useState('');
  // const [password, setPassWord] = useState('');

  const loginAccount = (username, password) => {
    // 做两个身份登录
    // 判断权限
    // 1. 操作员 user aa123456
    // 2. 管理员 admin AA123456
    // if (account === 'admin' && password === 'AA123456') {
    if (username === '1' && password === '1') {
      sessionStorage.setItem('permissions', 'admin')
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          // currentUser: userInfo,
          currentUser: {
            "name": "管理员",
            "avatar": "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
          },
          permissions: 1
        }));
      });
      // 走接口保存数据
      history.push('/text');
    } else if (username === 'user' && password === 'Aa123456') {
      sessionStorage.setItem('permissions', 'user')
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: {
            "name": "操作员 ",
            "avatar": "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
          },
        }));
      });
      history.push('/index');
    } else {
      messageApi.open({
        type: 'error',
        content: '账号密码错误登录失败!',
      });
    }
  }

  const onFinish = (values) => {
    console.log('Success:', values);
    let { username, password } = values
    loginAccount(username, password)
  };

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo);
  };



  const loginDD = () => {
    console.log('钉钉登陆');
  }


  useEffect(() => {
    console.log('测试登录 当前路由', location);
    var zxcvbn = require('zxcvbn');
    console.log(zxcvbn('Tr0ub4dour&3'));
    // flushSync(() => {
    //   setInitialState((s) => ({
    //     ...s,
    //     // currentUser: userInfo,
    //     currentUser: {
    //       "name": "管理员",
    //       "avatar": "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
    //     },
    //     permissions: 1
    //   }));
    // });
    // // 走接口保存数据
    // history.push('/userInfo');
    if (type === 1) {
      loginDD()
    }
    // 1. 获取当前路由根据路由判断使用那种页面形式
  }, []);

  return (
    <div className={styles.container}>
      {contextHolder}
      {/* 账号密码登录 */}
      {
        type === 0 ? <> <div className="logo" style={{ margin: '20px' }}>
          <Row>
            <Col>
              <Image
                width={60}
                preview={false}
                src='https://filetest.eykj.cn/tool/logo.png'
              />
            </Col>
            <Col style={{ color: '#fff', fontSize: '24px', textAlign: 'left', marginLeft: '8px' }}>
              <div>铁福来云平台</div>
              <div style={{ fontSize: '14px' }}>铁福来云平台</div>
            </Col>
          </Row>
        </div>
          <Row style={{ marginTop: '77px' }}>
            <Col span={12} style={{ textAlign: 'center', color: '#fff' }}>
              {/* <Image
                style={{ marginTop: '74px' }}
                width={200}
                preview={false}
                src={defaultDiagram}
              /> */}
              <div style={{ fontSize: "28px", marginTop: '90px' }}>铁福来设备管理云平台</div>
              {/* <div style={{ fontSize: "16px", marginTop: '30px' }}>集成多种身份验证方式，实现快速自助签到与登记的智能化设备。</div> */}
            </Col>
            <Col offset={1}>
              <div style={{ backgroundColor: '#fff', height: "650px", width: '500px', borderRadius: '16px', padding: '90px 52px', textAlign: 'center' }}>
                <div style={{ fontSize: '32px' }}>账号登录</div>
                <Form
                  name="basic"
                  // labelCol={{ span: 8 }}
                  // wrapperCol={{ span: 16 }}
                  style={{ maxWidth: 600 }}
                  initialValues={{ remember: true }}
                  onFinish={onFinish}
                  onFinishFailed={onFinishFailed}
                  autoComplete="off"
                >
                  <Form.Item
                    style={{ marginTop: '56px' }}
                    name="username"
                    rules={[{ required: true, message: '请输入账号!' }]}
                  >
                    <Input variant="filled" placeholder="请输入账号" style={{ height: '54px', fontSize: '17px' }} />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    style={{ marginTop: '16px' }}
                    rules={[{ required: true, message: '请输入密码!' }]}
                  >
                    <Input.Password variant="filled" placeholder="请输入密码" style={{ height: '54px', fontSize: '17px' }} />
                  </Form.Item>
                  <Form.Item label={null}
                    style={{ marginTop: '77px' }}>
                    <Button type="primary" htmlType="submit" style={{ width: '100%', height: '54px', fontSize: '20px' }}>
                      确  认
                    </Button>
                  </Form.Item>
                </Form>
                {/* <div style={{ marginTop: '56px' }}><Input variant="filled" value={account} placeholder="请输入账号" style={{ height: '54px', fontSize: '17px' }} onChange={(e) => {
                  // console.log('e', e.target.value);
                  setAccount(e.target.value)
                }} /></div>
                <div style={{ marginTop: '16px' }}><Input.Password variant="filled" value={password} placeholder="请输入密码" style={{ height: '54px', fontSize: '17px' }} onChange={(e) => {
                  // console.log('e', e.target.value);?
                  setPassWord(e.target.value)
                }} /></div>
                <div style={{ marginTop: '77px' }}><Button type="primary" style={{ width: '100%', height: '54px', fontSize: '20px' }} onClick={loginAccount}>确  认</Button></div> */}
              </div>
            </Col>
          </Row></> : ''
      }
      {/* 钉钉免登 */}
      {
        type === 1 ? <> <div className="logo" style={{ margin: '20px' }}>
          <Row>
            <Col>
              <Image
                width={60}
                preview={false}
                src='https://filetest.eykj.cn/tool/logo.png'
              />
            </Col>
            <Col style={{ color: '#fff', fontSize: '24px', textAlign: 'left', marginLeft: '8px' }}>
              <div>铁福来云平台</div>
              <div style={{ fontSize: '14px' }}>铁福来云平台</div>
            </Col>
          </Row>
        </div>
          <Row style={{ marginTop: '77px' }}>
            <Col span={24} style={{ textAlign: 'center', color: '#fff' }}>
              <Image
                style={{ marginTop: '74px' }}
                width={200}
                preview={false}
                // src={defaultDiagram}
              />
              <div style={{ fontSize: "28px", marginTop: '90px' }}>铁福来云平台</div>
              <div style={{ fontSize: "16px", marginTop: '30px' }}>集成多种身份验证方式，实现快速自助签到与登记的智能化设备。</div>
            </Col>
          </Row></> : ''
      }
      {/* 扫码登陆 */}
      {
        type === 2 ? <> <div className="logo" style={{ margin: '20px' }}>
          <Row>
            <Col>
              <Image
                width={60}
                preview={false}
                src='https://filetest.eykj.cn/tool/logo.png'
              />
            </Col>
            <Col style={{ color: '#fff', fontSize: '24px', textAlign: 'left', marginLeft: '8px' }}>
              <div>铁福来云平台</div>
              <div style={{ fontSize: '14px' }}>铁福来云平台</div>
            </Col>
          </Row>
        </div>
          <Row style={{ marginTop: '77px' }}>
            <Col span={12} style={{ textAlign: 'center', color: '#fff' }}>
              <Image
                style={{ marginTop: '74px' }}
                width={200}
                preview={false}
                // src={defaultDiagram}
              />
              <div style={{ fontSize: "28px", marginTop: '90px' }}>铁福来云平台</div>
              <div style={{ fontSize: "16px", marginTop: '30px' }}>集成多种身份验证方式，实现快速自助签到与登记的智能化设备。</div>
            </Col>
            <Col offset={1}>
              <div style={{ backgroundColor: '#fff', height: "650px", width: '500px', borderRadius: '16px', padding: '90px 52px', textAlign: 'center' }}>
                <div style={{ fontSize: '32px' }}>扫码登录</div>
                <div style={{ marginTop: '16px', color: '#9E9E9E', fontSize: '20px' }}>请使用<span style={{ color: '#007FFF' }}>钉钉移动端</span>扫描二维码</div>
                <Row style={{ marginTop: '57px' }} justify="center">
                  <Col>
                    <QRCode size={280} value={code || '-'} bordered={false} />
                  </Col>
                </Row>
              </div>
            </Col>
          </Row></> : ''
      }

    </div>
  );
};

export default Login;
