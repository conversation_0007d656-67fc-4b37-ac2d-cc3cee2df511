import React, { useState, useEffect } from 'react';
import { Breadcrumb, message, Card, Row, Col, Tag, Result, Button, Spin, Flex } from 'antd';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';

const App: React.FC = () => {
    const [messageApi, contextHolder] = message.useMessage();
    const { location } = history;
    const [initialValues, setInitialValues] = useState({})
    const [msg, setMsg] = useState('')
    const [loading, setLoading] = useState<boolean>(false);
    const columns = [
        {
            title: '客户信息',
            items: [
                {
                    label: '客户ID',
                    dataIndex: 'corpId',
                },
                {
                    label: '客户名称',
                    dataIndex: 'companyName',
                },
                {
                    label: '客户类型',
                    dataIndex: 'type',
                    type: 'Tag'
                },
                {
                    label: '联系人',
                    dataIndex: 'userName',
                },
                {
                    label: '联系电话',
                    dataIndex: 'phone',
                },
                {
                    label: '电子邮箱',
                    dataIndex: 'email',
                },
                {
                    label: '地域/区域',
                    dataIndex: 'region',
                },
                {
                    label: '注册日期',
                    dataIndex: 'date',
                },
                {
                    label: '客户状态',
                    dataIndex: 'state',
                    type: 'Select',
                    options: [
                        { label: '正常', value: 0, color: 'blue' },
                        { label: '关闭', value: 1, color: 'red' },
                    ],
                },
                {
                    label: '最近交易日期',
                    dataIndex: 'transactionDate',
                },
                {
                    label: '累计交易金额',
                    dataIndex: 'transactionAmount',
                    addonBefore: "¥"
                },
            ]
        },
    ]

    const getIDFromURLUsingSubstring = (url: string) => {
        const idParam = 'id=';
        const startIndex = url.indexOf(idParam);
        if (startIndex === -1) {
            return null
        }
        const valueStartIndex = startIndex + idParam.length;
        let valueEndIndex = url.indexOf('&', valueStartIndex);
        if (valueEndIndex === -1) {
            valueEndIndex = url.length;
        }
        return url.substring(valueStartIndex, valueEndIndex);
    }
    const getInfo = async (id) => {
        setLoading(true)
        const result = await getRequest('customer/get_info', { id });
        const { data, status, msg } = result
        if (status === 0) {
            setInitialValues(data)
            setLoading(false)
        } else {
            setMsg(msg)
            setLoading(false)
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
    }

    useEffect(() => {
        if (location.search) {
            const url = location.search;
            const idValue = getIDFromURLUsingSubstring(url);
            // getInfo(idValue)
            getInfo(1)
        }
    }, []);

    return (<>
        {contextHolder}
        <Spin spinning={loading}>
            {msg ? <Result
                status="500"
                title="error"
                subTitle={msg}
                extra={<Button type="primary" onClick={() => { history.go(-1) }}>返回上一层</Button>}
            /> :
                <>
                    {columns.map((item) => {
                        return (
                            <Row gutter={[16, 14]} key={item.title}  >
                                <Col span={24}>
                                    <div style={{
                                        color: '#adadad', fontSize: '20px',
                                        fontWeight: 500
                                    }}>{item.title}</div>
                                </Col>
                                {item.items.map((it) => {
                                    return (
                                        <Col className="gutter-row" span={12} key={it.dataIndex}>
                                            <Flex>
                                                <div style={{
                                                    color: '#7e7e7e', marginBottom: '14px', width: '120px'
                                                }}>{it.label}</div>
                                                {/* <div>{initialValues[it.dataIndex]}</div> */}
                                                {
                                                    it.type === 'Tag' ?
                                                        <Tag color="blue">{initialValues[it.dataIndex]}</Tag>
                                                        : ''
                                                }
                                                {it.type === 'Select' ?
                                                    <>
                                                        {it.options.map((option) => {
                                                            if (option.value === initialValues[it.dataIndex]) {
                                                                return <Tag color={option.color}>{option.label}</Tag>
                                                            }
                                                        })
                                                        }
                                                    </>
                                                    : ''
                                                }
                                                {
                                                    !it.type ?
                                                        <div>{it.addonBefore ? it.addonBefore : ""}{initialValues[it.dataIndex]}</div>
                                                        : ''
                                                }
                                            </Flex>
                                        </Col>
                                    )
                                })
                                }
                            </Row>
                        )
                    })}
                    <Row style={{marginTop:'24px'}} >
                        <Col span={24}>
                            <div style={{   color: '#7e7e7e' ,marginBottom:'14px'}}>备注</div>
                        </Col>
                        <Col span={24} style={{
                            background: '#262626', height: '60px',
                            borderRadius: '6px',padding:'16px'
                        }}>
                            <div>{initialValues.remark}</div>
                        </Col>
                    </Row>
                </>
            }
        </Spin>
    </>)
}

export default App;