# 待办详情页面抓拍上传功能集成文档

## 概述

本文档描述了在待办详情页面中集成图片和视频抓拍上传功能的实现。该功能允许用户通过萤石播放器抓拍图片和录制视频，并自动或手动上传到服务器。

## 功能特性

### 1. 自动上传
- 萤石播放器抓拍图片后自动上传到 `/api/file/upload_capture_image`
- 萤石播放器录制视频后自动上传到 `/api/file/upload_capture_video`
- 上传成功后显示成功提示，失败时显示错误信息
- 后台异步处理，不影响用户操作

### 2. 文件管理
- 支持删除抓拍的图片和视频
- 自动释放blob URL内存
- 新抓拍内容用蓝色边框标识

## API接口

### 上传抓拍图片
```
POST /api/file/upload_capture_image
Content-Type: multipart/form-data

参数:
- file: 图片文件 (必需)
```

### 上传抓拍视频
```
POST /api/file/upload_capture_video
Content-Type: multipart/form-data

参数:
- file: 视频文件 (必需)
```

## 技术实现

### 1. 文件转换
- 将萤石播放器返回的base64图片数据转换为File对象
- 将blob视频数据转换为File对象
- 自动生成文件名（包含时间戳）

### 2. 上传服务
在 `src/services/api/api.ts` 中新增了两个上传函数：
- `uploadCaptureImage()`: 上传图片文件
- `uploadCaptureVideo()`: 上传视频文件

### 3. 用户界面
- 图片和视频缩略图上显示删除按钮
- 新抓拍的内容用蓝色边框标识
- 简洁的界面设计，专注于内容展示

## 使用流程

### 自动上传流程
1. 用户通过萤石播放器抓拍图片或录制视频
2. 系统接收到抓拍数据后立即显示在界面上
3. 后台自动将文件上传到服务器
4. 显示上传结果提示信息
5. 用户可以删除不需要的抓拍内容

## 错误处理

### 1. 数据格式错误
- 检查萤石播放器返回的数据格式
- 对异常数据进行容错处理
- 显示友好的错误提示

### 2. 上传失败
- 网络错误时显示重试提示
- 服务器错误时显示具体错误信息
- 不影响界面显示和其他功能

### 3. 文件处理错误
- base64转换失败时的错误处理
- blob文件读取失败时的错误处理
- 内存泄漏防护（及时释放blob URL）

## 注意事项

1. **文件大小限制**: 需要根据服务器配置调整文件大小限制
2. **格式支持**: 目前支持JPEG图片和MP4视频格式
3. **内存管理**: 及时释放不再使用的blob URL以防止内存泄漏
4. **网络优化**: 大文件上传时考虑分片上传或压缩处理
5. **用户体验**: 上传过程中提供进度反馈和取消功能

## 文件结构

```
src/
├── services/api/
│   └── api.ts                 # 新增上传API函数
└── pages/Todo/components/
    └── TodoDetail.tsx         # 待办详情组件（包含上传功能）
```

## 依赖项

- `@umijs/max`: HTTP请求库
- `antd`: UI组件库
- `react`: 前端框架

## 测试建议

1. 测试不同格式的图片和视频文件
2. 测试网络异常情况下的错误处理
3. 测试大文件上传的性能表现
4. 测试内存使用情况，确保无内存泄漏
5. 测试并发上传的稳定性
