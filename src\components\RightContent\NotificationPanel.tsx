/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-31 11:43:31
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-02 20:26:19
 * @FilePath: \diy_tfl_pc\src\components\RightContent\NotificationPanel.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React,{useState,useEffect} from 'react';
import { Tabs, Form, Empty,Flex,Button,Image,Row, Col,Input, Pagination,message,Modal} from 'antd';
import { BellOutlined } from '@ant-design/icons';
import hole from '../../../public/icons/hole.png'
import FolderOpen from '../../../public/icons/FolderOpen.png'
import { postRequest,getRequest } from '@/services/api/api';
import { history } from '@umijs/max';
import { Divider } from 'rc-menu';
const { TextArea } = Input;
const tabsStyle = `
  .notification-tabs .ant-tabs-nav-list {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  
  .notification-tabs .ant-tabs-tab {
    margin: 0;
  }
`;
const NotificationPanel: React.FC = (isOpen) => {
  const [form] = Form.useForm();
  const [todoList, setTodoList] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingIds, setLoadingIds] = useState<string[]>([]);
  const [rejectReason, setRejectReason] = useState('');
  const [currentRejectId, setCurrentRejectId] = useState<string>('');
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [tabNum, setTabNum] = useState('1');
  const [notificationCount, setNotificationCount] = useState(0);
  const [messageCount, setMessageCount] = useState(0);
  const getImageSource = (type: number) => type === 1 ? FolderOpen : hole;
  
  const handleReject = (id: string) => {
    setCurrentRejectId(id);
    setRejectReason('');
    setRejectModalVisible(true);
  };

  const handleRejectConfirm = async () => {
    if (!rejectReason.trim()) {
      messageApi.error('请输入拒绝理由');
      return;
    }

    setLoadingIds(prev => [...prev, currentRejectId]);
    try {
      const result = await postRequest('todo/post_modify', {
        id: currentRejectId,
        action: 2,
        refuse: rejectReason,
      });
      
      if (result.status === 0) {
        messageApi.success('操作成功');
        setRejectModalVisible(false);
        fetchTodoList({ page: currentPage, perPage: 10, status: 1, type: 0 });
      } else {
        messageApi.error(result.msg || '操作失败');
      }
    } catch (error) {
      messageApi.error('请求失败');
    } finally {
      setLoadingIds(prev => prev.filter(item => item !== currentRejectId));
    }
  };
//通知数量
const handleEdit = async () => {
  try {
    const result = await getRequest('drill/get_tode_num');
    const { status, msg, data } = result as any;
    if (status === 0) {
      setNotificationCount(data.todoNum)
      setMessageCount(data.messageNum)
      // const dataSource = data.todoNum+data.messageNum
      // setNotificationCount(dataSource);
    } else {
      // message.error(msg)
    }
  } catch (error) {
  }
};
  const handleRejectCancel = () => {
    setRejectModalVisible(false);
    setRejectReason('');
    setCurrentRejectId('');
  };

  const handleAgree = async (id: string) => {
    setLoadingIds(prev => [...prev, id]);
    try {
      const result = await postRequest('todo/post_modify', {
        id,
        action: 1
      });
      
      if (result.status === 0) {
        messageApi.success('操作成功');
        fetchTodoList({ page: currentPage, perPage: 10, type: 2 });
      } else {
        messageApi.error(result.msg || '操作失败');
      }
    } catch (error) {
      messageApi.error('请求失败');
    } finally {
      setLoadingIds(prev => prev.filter(item => item !== id));
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const activeKey = document.querySelector('.ant-tabs-tab-active')?.getAttribute('data-node-key') || '1';
    fetchTodoList({ 
      page, 
      perPage: 10,
      type: Number(activeKey) === 1 ? 2 : 1,
      ...(Number(activeKey) === 2 ? { status: 1 } : {})
    });
  };

  const handleTabs = (activeKey: string) => {
    setTabNum(activeKey);
    setCurrentPage(1);
    fetchTodoList({
      page: 1,
      perPage: 10,
      type: Number(activeKey) === 1 ? 2 : 1,
      ...(Number(activeKey) === 2 ? { status: 1 } : {})
    });
  };

  const fetchTodoList = async (params = { page: 1, perPage: 10, type: 2 }) => {
    try {
      const result = await postRequest('todo/get_ls', {
        ...params,
        status: 1
      });
      
      if (result.status === 0) {
        const processedItems = result.data.items.map(item => ({
          ...item,
          dynamicFields: typeof item.dynamicFields === 'string'
            ? JSON.parse(item.dynamicFields || '{}')
            : item.dynamicFields
        }));
        
        setTodoList(processedItems);
        setTotal(result.data.total);
      }
    } catch (error) {
      messageApi.error('获取列表失败');
    }
  };
  const resetPanel = () => {
    setCurrentPage(1);
    fetchTodoList({
      page: 1,
      perPage: 10,
      type: 2
    });
  };

  // 默认加载一次
  useEffect(() => {
    console.log(isOpen,'key')
    if(isOpen.isOpen==true){
      handleEdit()
      setTabNum('1')
      fetchTodoList();
    }
  }, [isOpen]);
  
  const PageListItem = ({ id, title, submitter, createdAt, dynamicFields, status, type, correlationId }: TodoItem) => {
    const isLoading = loadingIds.includes(id);
    return (
      <div style={{ padding: '12px', borderBottom: '1px solid #434343' }}>
        <div style={{ display: 'flex', gap: '12px' }} >
          <div style={{ marginTop: '2px' }}>
            <Image
              width={24}
              height={24}
              src={getImageSource(type)}
              preview={false}
            />
          </div>
          
          <div style={{ flex: 1 }}>
            <div style={{ 
              fontWeight: 'bold',
              fontSize: '14px',
              color: 'color: rgba(255, 255, 255, 0.85);',
              marginBottom: '6px',
              display: 'flex',
              alignItems: 'center',
              paddingLeft: '0'
            }}>
              {title}
            </div>
            
            <div style={{ 
              color:' rgba(255, 255, 255, 0.65)',
              fontSize: '14px',
              lineHeight: '20px',
              marginLeft: '0'
            }}>
              <span onClick={() => history.push(`/todo`)}>
                {dynamicFields && typeof dynamicFields === 'object' && Object.entries(dynamicFields)
                  .slice(0, 2)
                  .map(([key, value], index) => (
                  <div key={key} style={{ marginBottom: '8px', textAlign: 'left' }}>
                    <span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>{value?.title || key}：<span style={{ color: 'rgba(255, 255, 255, 0.65)' }}>{value?.value ?? value}</span> </span>
                  </div>
                ))}
              </span>
              
              <div style={{ marginTop: '6px',color: 'rgba(255, 255, 255, 0.45)', fontSize:'14px', marginLeft:'-25px'}}>创建时间：{createdAt}</div>
            </div>
            
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              gap: '10px', 
              marginTop: '16px' 
            }}>
              {status !== 1 && status !== 2 && (
                <>
                  {type !== 2 ? (
                    <>
                      <Button
                        onClick={() => handleReject(id)}
                        loading={isLoading}
                        disabled={isLoading}
                        style={{
                          borderRadius: '4px',
                          fontSize: '14px',
                          height: '32px',
                          width: '80px',
                          border: '1px solid #1677ff',
                          color: '#1677ff',
                          background: 'transparent'
                        }}
                      >
                        拒绝
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => handleAgree(id)}
                        loading={isLoading}
                        disabled={isLoading}
                        style={{
                          borderRadius: '4px',
                          fontSize: '14px',
                          height: '32px',
                          width: '80px'
                        }}
                      >
                        同意
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="primary"
                      onClick={() => handleAgree(id)}
                      loading={isLoading}
                      disabled={isLoading}
                      style={{
                        borderRadius: '4px',
                        fontSize: '14px',
                        height: '32px',
                        width: '80px'
                      }}
                    >
                      确认查收
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderListContent = () => {
    if (!todoList.length) {
      return (
        <Flex justify="center" align="center" style={{ padding: '32px 0' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span style={{ color: '#8c8c8c' }}>暂无待办通知事项</span>}
          />
        </Flex>
      );
    }

    return (
      <Flex vertical>
        {todoList.map(item => (
          <PageListItem {...item} key={item.id} />
        ))}
      </Flex>
    );
  };

  return (
    <div className="notification-panel" style={{ width: 300, zIndex: 1000, backgroundColor: '#141414' }}>
      {contextHolder}
      <style>{tabsStyle}</style>
      <Tabs 
        onChange={handleTabs}
        activeKey={tabNum}
        items={[
          {
            key: '1',
            label: (
              <span>
                通知 <span style={{ marginLeft: 4 }}>（{messageCount}）</span>
              </span>
            ),
            children: (
              <div style={{ padding: '0', textAlign: 'center' }}>
                 {renderListContent()}
                {todoList.length > 0 && (
                  <Flex justify="flex-end" style={{ marginTop: '12px' }}>
                    <Pagination
                      total={total}
                      current={currentPage}
                      onChange={handlePageChange}
                      showTotal={(total) => `总共${total}条`}
                    />
                  </Flex>
                )}
              </div>
            ),
          },
          {
            key: '2',
            label: (
              <span>
                待办 <span style={{ marginLeft: 4 }}>（{notificationCount}）</span>
              </span>
            ),
                 children: (
              <div style={{ padding: '0', textAlign: 'center' }}>
                 {renderListContent()}
              {todoList.length > 0 && (
                <Flex justify="flex-end" style={{ marginTop: '12px' }}>
                  <Pagination
                    total={total}
                    current={currentPage}
                    onChange={handlePageChange}
                    showTotal={(total) => `总共${total}条`}
                  />
                </Flex>
              )}
              </div>
            ),
          },
        ]}
        style={{ paddingLeft: '20px' }} 
        centered={false}
      />
           <Modal
     title="拒绝原因"
     open={rejectModalVisible}
     onOk={handleRejectConfirm}
     onCancel={handleRejectCancel}
     confirmLoading={loadingIds.includes(currentRejectId)}
   >
     <Form layout="vertical">
       <Form.Item
         label="请输入拒绝理由"
         required
         rules={[{ required: true, message: '请输入拒绝理由' }]}
       >
         <TextArea
           rows={4}
           value={rejectReason}
           onChange={(e) => setRejectReason(e.target.value)}
           placeholder="请输入拒绝理由"
           maxLength={500}
           showCount
         />
       </Form.Item>
     </Form>
   </Modal>
    </div>
    
  );
};

export default NotificationPanel; 