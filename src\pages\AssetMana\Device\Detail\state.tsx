import React, { useRef,useState } from 'react';
import {
    ProTable,
} from '@ant-design/pro-components';
import { Segmented,Tag,Popconfirm,message } from 'antd';
import { postRequest } from '@/services/api/api';
import Status from './status'

interface StatusProps {
  number: string;
  onDataChange?: (data: any) => void; // 添加回调函数属性
}
export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
type Align = '看板视图' | '列表视图';

  const cancel: PopconfirmProps['onCancel'] = (e) => {
    console.log(e);
    // message.error('Click on No');
  };
// const [alignValue, setAlignValue] = useState('看板视图');
const DeviceStatusPage: React.FC<StatusProps> = ({ number,onDataChange }) => {
    const [alignValue, setAlignValue] = React.useState<Align>('看板视图');
    const [activeTab, setActiveTab] = useState('看板视图');
    const ref = useRef();
      const columns: ProColumns<TableListItem>[] = [
        {
            title: '变量ID',
            dataIndex: 'id',
            search: false,
        },
        {
            title: '变量名称',
            dataIndex: 'title',
            // width: 200,
            // search: false,
        },
        {
            title: '变量标识',
            dataIndex: 'var',
            render: (text, record,) => {
                return (
                    <Tag color="blue">
                        {text}
                    </Tag>
                )
            },
            search: false,
        },
        {
            title: '变量类型',
            dataIndex: 'type',
            render: (text, record,) => {
                const options = [
                    {
                        label: '数值',
                        color: 'blue'
                    },
                    {
                        label: '文本',
                        color: 'blue'
                    },
                    {
                        label: '开关',
                        color: 'blue'
                    },
                    {
                        label: '时间',
                        color: 'blue'
                    },
                    {
                        label: '日期',
                        color: 'blue'
                    },
                    {
                        label: '频谱',
                        color: 'blue'
                    },
                ]
                const field = options[text]
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
            search: false,
        },
        {
            title: '当前值',
            dataIndex: 'val',
            search: false,
        },
        {
            title: '更新时间',
            dataIndex: 'updatedAt',
            search: false,
        },
        {
            title: '操作',
            dataIndex: 'option',
            valueType: 'option',
            render: (text,record) => [
                <a
                    key="config"
                    onClick={() => {
                        onDataChange(record);
                    }}
                >
                    历史记录
                </a>,
                
                <a key="transfer"
                    style={{ marginLeft: '24px' }}
                >
                         <Popconfirm
                           onConfirm={async ()=>{
                            const postData = {
                                deviceCode:record.deviceCode,
                                deviceModelID:record.id,
                            }
                            const result = await postRequest('device_data/post_refresh', postData);
                            console.log('刷新设备信息',result);
                            const {status,msg} = result as any
                            if(status === 0){
                                message.success('主动采集成功');
                                ref.current.reload();
                            }else{
                                message.error(msg);
                            }
                           }}
                           onCancel={cancel}
                            description="是否确认更新值"
                            okText="是的"
                            cancelText="取消"
                            >
                                主动采集
                        </Popconfirm>
                </a>,
            ],
        },
    ];

    return (
        <>
         <div style={{float:'right'}}>
         <Segmented
            value={alignValue}
            style={{ marginBottom: 8 }}
            onChange={(value) => {
            setAlignValue(value); // 更新选中的值
            console.log('当前选中的视图是:', value); // 这里可以读取切换后的值
            setActiveTab(value)
        }}
        options={['看板视图', '列表视图']}
      />
         </div>
    
            <div>
            {activeTab === '列表视图' ? (
                <ProTable<TableListItem>
                    actionRef={ref}
                    columns={columns}
                    request={async (params, sorter, filter) => {
                        // 表单搜索项会从 params 传入，传递给后端接口。
                        console.log('6666666666666', params, sorter, filter);
                        const { current, pageSize, ...rest } = params;
                        let postData = {
                            page: current,
                            perPage: pageSize,
                            number:number
                        }
                        const filteredData = {};
                        for (const key in rest) {
                            if (rest.hasOwnProperty(key)) {
                                const value = rest[key];
                                if (typeof value === 'number' && !isNaN(value) || typeof value === 'string' && value.trim() !== '') {
                                    filteredData[key] = value;
                                }
                            }
                        }
                        postData = {
                            ...postData,
                            ...filteredData
                        }
                        const result = await postRequest('device_data/get_ls', postData);
                        const { data, status, msg } = result as any
                        let dataSource
                        let total
                        if (status === 0) {
                            dataSource = data.items
                            total = data.total
                        } else {
                            message.error(msg);
                        }
                        return Promise.resolve({
                            data: dataSource,
                            total: total,
                            success: true,
                        });
                    }}
                    rowKey="key"
                    options={false}
                        search={{
                        defaultCollapsed: false,
                        labelWidth: 0,
                        // span: 3,
                    }}
                    form={{
                        initialValues: {
                            sort: 0
                        }
                    }}
                />
            ) : (
                <Status number={number}></Status> // 当 activeTab 是"看板视图"时显示数字 1
            )}
            </div>
        </>
    );
};

export default DeviceStatusPage;
