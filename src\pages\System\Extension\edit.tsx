import React, { useState, useEffect } from 'react';
import { Card, message, Spin } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { history, useSearchParams } from '@umijs/max';
import { extensionService } from './service';
import ExtensionForm from './components/ExtensionForm';
import { ExtensionFormData } from './types';

const EditExtension: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [initialValues, setInitialValues] = useState<ExtensionFormData>();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');

  // 获取分机号详情
  const fetchExtensionDetail = async () => {
    if (!id) {
      message.error('缺少分机号ID');
      history.push('/system/extension');
      return;
    }

    setDataLoading(true);
    try {
      const result = await extensionService.getDetail(Number(id));
      if (result.status === 0 && result.data) {
        setInitialValues({
          account: result.data.account,
          password: result.data.password,
          bindCode: result.data.bindCode || '',
          bindType: result.data.bindType || undefined,
          status: result.data.status,
        });
      } else {
        message.error(result.msg || '获取分机号详情失败');
        history.push('/system/extension');
      }
    } catch (error) {
      message.error('获取分机号详情失败');
      history.push('/system/extension');
    } finally {
      setDataLoading(false);
    }
  };

  // 处理表单提交
  const handleFinish = async (values: ExtensionFormData) => {
    setLoading(true);
    try {
      const result = await extensionService.update(Number(id!), values);
      if (result.status === 0) {
        message.success('修改分机号成功');
        history.push('/system/extension');
      } else {
        message.error(result.msg || '修改失败');
      }
    } catch (error) {
      message.error('修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 返回列表页
  const handleBack = () => {
    history.push('/system/extension');
  };

  useEffect(() => {
    fetchExtensionDetail();
  }, [id]);

  if (dataLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ArrowLeftOutlined
              style={{ cursor: 'pointer' }}
              onClick={handleBack}
            />
            <span>编辑分机号</span>
          </div>
        }

      >
        <div style={{ maxWidth: '600px' }}>
          <ExtensionForm
            initialValues={initialValues}
            onFinish={handleFinish}
            loading={loading}
          />
        </div>
      </Card>
    </div>
  );
};

export default EditExtension;
