import { message, Breadcrumb, Card, Col, Row, Tag, Spin } from 'antd';
import React, { useState, useEffect } from 'react';
import { getRequest, postRequest } from '@/services/api/api';
import { history } from '@umijs/max';
import Approve from './component/approve'
import Sealing from './component/sealing'
import Report from './component/report'
import Historica from './component/historica'
import VideoPlayer from './component/videoPlayer'
import EndHoleReport from './component/endHoleReport'
import ConstructionReport from './component/constructionReport'
// import History from './component/history'
// import Information from './component/information'
// import Life from './component/life'
// import Maintain from './component/maintain'

interface DeviceInfo {
    deviceName: string;           // 设备名称
    deviceCode: string;           // 设备序列号
    deviceModel: string;          // 设备型号
    configurationState?: number;  // 配置状态：0-启用 1-禁用
    creatAt: string;             // 创建时间
    updateAt: string;            // 最后更新时间
    currentState: number;        // 当前状态：0-打开 1-关闭 2-损坏
    lastMaintenanceDate?: string; // 最后保养日期
    miningFace?: string;         // 采面
    lane?: string;               // 巷道
    drillSite?: string;          // 矿场
    companyName?: string;        // 客户名称
    director?: string;           // 设备负责人
    account?: string;            // 设备负责人账号
    label?: string;              // 设备标签
    deviceModelId: string;       // 设备型号ID
    drillNumber?: string;        // 钻孔编号
}

interface ApiResponse<T> {
    status: number;
    msg?: string;
    data: T;
}
export default () => {
    const { location } = history;
    const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>();
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [activeTabKey, setActiveTabKey] = useState('0');
    const [infoLoaded, setInfoLoaded] = useState(false);
    const [detail, setDetail] = useState();
    const [deviceCode, setDeviceCode] = useState();

    // 获取设备详情
    const fetchDeviceInfo = async () => {
        const searchParams = new URLSearchParams(location.search);
        const id = searchParams.get('id');

        if (!id) {
            messageApi.error('设备ID不能为空');
            return;
        }

        setLoading(true);
        setInfoLoaded(false);
        try {
            const result = await getRequest<ApiResponse<DeviceInfo>>('drill/get_info', {
                id
            });

            if (result.status === 0) {
                setDeviceInfo(result.data);
                setInfoLoaded(true);
                setDeviceCode(result.data.drillNumber);
                if (result.data.drillNumber) {
                    fetchDetailWithNumber(result.data.drillNumber);
                } else {
                    messageApi.error('钻孔编号不能为空');
                }
            } else {
                messageApi.error(result.msg || '获取任务信息失败');
            }
        } catch (error) {
            messageApi.error('请求失败');
        } finally {
            setLoading(false);
        }
    };

    const fetchDetailWithNumber = async (number: string) => {
        setLoading(true);
        try {
            const result = await postRequest('device/get_detail', {
                number
            });

            if (result.status === 0) {
                setDetail(result.data.gbsId);
            } else {
                messageApi.error(result.msg || '获取详情失败');
            }
        } catch (error) {
            messageApi.error('请求失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDeviceInfo();

    }, []);

    const tabListNoTitle = [
        {
            label: '审批记录',
            key: '0',
        },
        {
            label: '历史记录',
            key: '1',
        },
        {
            label: '上报异常记录',
            key: '2',
        },
        {
            label: '封孔材料',
            key: '3',
        },
        {
            label: '终孔报告单',
            key: '4',
        },
        {
            label: '视频回放',
            key: '5',
        },
        {
            label: '施工报告',
            key: '6',
        },
    ];

    const contentListNoTitle: Record<string, React.ReactNode> = {
        '0': infoLoaded && deviceInfo ? <Approve number={deviceInfo.id} /> : <Spin />,
        '1': infoLoaded && deviceInfo ? <Historica number={deviceInfo} /> : <Spin />,
        '2': infoLoaded && deviceInfo ? <Report number={{ parentId: deviceInfo.id }} /> : <Spin />,
        '3': infoLoaded && deviceInfo ? <Sealing number={deviceInfo.id} /> : <Spin />,
        '4': infoLoaded && deviceInfo ? <EndHoleReport number={deviceInfo.id} /> : <Spin />,
        '5': infoLoaded && deviceInfo ? <VideoPlayer number={deviceInfo.id} gbsId={detail} deviceCode={deviceCode} /> : <Spin />,
        '6': infoLoaded && deviceInfo ? <ConstructionReport number={deviceInfo.id} /> : <Spin />,
    };

    const onTabChange = (key: string) => {
        setActiveTabKey(key);
    };

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '施工管理', },
                    { title: '施工记录', },
                    { title: '施工记录详情', },
                ]}
            />
            <Card bordered={false} loading={loading} style={{ marginTop: '24px' }}>

                <div style={{ marginTop: '6px', fontWeight: 500, fontSize: '20px' }}>{deviceInfo?.drillName || '-'}</div>
                <Row style={{ marginTop: '14px' }} gutter={[16, 8]}>
                    <Col span={6}>孔号：{deviceInfo?.holeNumber || '-'}</Col>
                    <Col span={6}>孔深：{deviceInfo?.holeDepth || '-'}</Col>
                    <Col span={6}>孔径：{deviceInfo?.holeDiameter || '-'}</Col>
                    <Col span={6}>开孔角度：{deviceInfo?.holeAngle || '-'}</Col>
                    <Col span={6}>见岩石距离：{deviceInfo?.rockDistance || '-'}</Col>
                    {/* <Col span={6}>扩孔起始距离：{deviceInfo?.reamingStartDistance || '-'}</Col>
                    <Col span={6}>预计出煤量：{deviceInfo?.estimatedCoalOutput || '-'}</Col> */}
                    <Col span={6}>开孔角度误差：{deviceInfo?.holeAngleError || '-'}</Col>
                </Row>
            </Card>

            <Card
                bordered={false}
                tabList={tabListNoTitle}
                activeTabKey={activeTabKey}
                onTabChange={onTabChange}
                style={{ marginTop: '24px' }}
                tabProps={{
                    size: 'middle',
                }}
            >
                {contentListNoTitle[activeTabKey]}
            </Card>
        </>
    );
};