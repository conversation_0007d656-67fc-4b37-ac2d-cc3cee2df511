/*
 * @Author: AI Assistant
 * @Date: 2025-01-02
 * @Description: 施工报告组件，包含设备利用率、能耗信息、计划结果与实际结果对比三个子模块
 */

import React, { useState, useEffect } from 'react';
import { Card, Tabs, Spin, message, Row, Col, Statistic, Progress, Table, Descriptions } from 'antd';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { getRequest } from '@/services/api/api';
import { Column } from '@ant-design/charts';

interface ConstructionReportProps {
    number: number; // 施工记录ID
}

// 设备利用率数据类型
interface EquipmentUtilization {
    deviceCode: string;          // 设备编号
    deviceName: string;          // 设备名称
    totalWorkTime: number;       // 总工作时间(小时)
    actualWorkTime: number;      // 实际工作时间(小时)
    utilizationRate: number;     // 利用率(%)
    idleTime: number;           // 空闲时间(小时)
    maintenanceTime: number;    // 维护时间(小时)
    faultTime: number;          // 故障时间(小时)
    efficiency: number;         // 工作效率(%)
    operatorName: string;       // 操作员姓名
    workDate: string;           // 工作日期
}

// API响应数据类型
interface ApiResponse<T> {
    status: number;
    msg: string;
    data: T;
}

// 施工报告API返回数据类型
interface ConstructionReportData {
    holeNumber: string;                      // 孔号
    position: string;                        // 位置
    constructionLocation: string;            // 施工位置
    drillingType: string;                    // 钻孔类型
    personnel: string | null;                // 人员
    water: number | null;                    // 用水量
    electricity: number | null;              // 用电量
    cementContent: number | null;            // 水泥量
    designHoleAngle: string;                 // 设计开孔角度
    designDirection: string;                 // 设计方位角
    designHoleHeight: string;                // 设计开孔高度
    designCoalDistance: string;              // 设计见煤距离
    designRockDistance: string;              // 设计见岩距离
    designHoleDepth: string;                 // 设计孔深
    practicalHoleAngle: string;              // 实际开孔角度
    practicalDirection: string;              // 实际方位角
    practicalHoleHeight: string;             // 实际开孔高度
    practicalHoleDepth: string;              // 实际孔深
    practicalCoalDistance: string;           // 实际见煤距离
    practicalRockDistance: string;           // 实际见岩距离
    holeAngleError: string;                  // 开孔角度误差
    directionError: string;                  // 方位角误差
    holeHeightError: string;                 // 开孔高度误差
    holeDepthError: string;                  // 孔深误差
    coalDistanceError: string;               // 见煤距离误差
    rockDistanceError: string;               // 见岩距离误差
    drillPipesNum: string;                   // 钻杆根数
    sealingDepth: number | null;             // 封孔深度
    holeAngleErrorRate: string;              // 开孔角度偏差率
    directionErrorRate: string;              // 方位角偏差率
    holeHeightErrorRate: string;             // 开孔高度偏差率
    holeDepthErrorRate: string;              // 孔深偏差率
    coalDistanceErrorRate: string;           // 见煤距离偏差率
    rockDistanceErrorRate: string;           // 见岩距离偏差率
    holeAngleStatus: number;                 // 开孔角度状态 (0-正常, 1-异常)
    directionStatus: number;                 // 方位角状态 (0-正常, 1-异常)
    holeHeightStatus: number;                // 开孔高度状态 (0-正常, 1-异常)
    coalDistanceStatus: number | null;       // 见煤距离状态 (0-正常, 1-异常)
    rockDistanceStatus: number | null;       // 见岩距离状态 (0-正常, 1-异常)
    holeDepthStatus: number;                 // 孔深状态 (0-正常, 1-异常)
    holeAngleRemark: string;                 // 开孔角度备注
    directionRemark: string;                 // 方位角备注
    holeHeightRemark: string;                // 开孔高度备注
    holeDepthRemark: string;                 // 孔深备注
    coalDistanceRemark: string;              // 见煤距离备注
    rockDistanceRemark: string;              // 见岩距离备注
    status: number;                          // 状态
    errorMessage: string | null;             // 错误信息
}

// 能耗信息数据类型（基于API数据）
interface EnergyConsumption {
    water: number | string | null;         // 用水量
    electricity: number | string | null;   // 用电量
    cementContent: number | string | null; // 水泥量
}

// 计划与实际对比数据类型（基于API数据转换）
interface PlanActualComparison {
    parameter: string;          // 参数名称
    designValue: string;        // 设计值
    practicalValue: string;     // 实际值
    errorValue: string;         // 误差值
    errorRate: string;          // 偏差率
    status: number;             // 状态 (0-正常, 1-异常)
    remark: string;             // 备注
    unit: string;               // 单位
}

const ConstructionReport: React.FC<ConstructionReportProps> = ({ number }) => {
    const [loading, setLoading] = useState(false);
    // const [equipmentData, setEquipmentData] = useState<EquipmentUtilization[]>([]);
    const [reportData, setReportData] = useState<ConstructionReportData | null>(null);
    const [energyData, setEnergyData] = useState<EnergyConsumption>({
        water: null,
        electricity: null,
        cementContent: null
    });
    const [comparisonData, setComparisonData] = useState<PlanActualComparison[]>([]);

    // 获取设备利用率数据 - 暂时注释
    // const fetchEquipmentUtilization = async () => {
    //     try {
    //         setLoading(true);
    //         // 模拟数据，实际应该调用API
    //         const mockData: EquipmentUtilization[] = [
    //             {
    //                 deviceCode: 'ZJ-001',
    //                 deviceName: '钻机设备001',
    //                 totalWorkTime: 24,
    //                 actualWorkTime: 18.5,
    //                 utilizationRate: 77.1,
    //                 idleTime: 3.5,
    //                 maintenanceTime: 1.5,
    //                 faultTime: 0.5,
    //                 efficiency: 85.2,
    //                 operatorName: '张三',
    //                 workDate: '2024-12-01'
    //             },
    //             {
    //                 deviceCode: 'ZJ-002',
    //                 deviceName: '钻机设备002',
    //                 totalWorkTime: 24,
    //                 actualWorkTime: 20.2,
    //                 utilizationRate: 84.2,
    //                 idleTime: 2.8,
    //                 maintenanceTime: 1.0,
    //                 faultTime: 0,
    //                 efficiency: 92.1,
    //                 operatorName: '李四',
    //                 workDate: '2024-12-01'
    //             }
    //         ];
    //         setEquipmentData(mockData);
    //     } catch (error) {
    //         message.error('获取设备利用率数据失败');
    //     } finally {
    //         setLoading(false);
    //     }
    // };

    // 获取施工报告数据
    const fetchConstructionReport = async () => {
        try {
            setLoading(true);
            console.log('开始获取施工报告数据，ID:', number);

            // {{ AURA-X: Add - 调用施工报告API接口. Approval: 寸止(ID:pending). }}
            const apiResponse = await getRequest<ApiResponse<ConstructionReportData>>('/drill/get_construction_report', { id: number });
            console.log('API返回数据:', apiResponse);

            if (!apiResponse || apiResponse.status !== 0 || !apiResponse.data) {
                throw new Error(apiResponse?.msg || 'API返回数据异常');
            }

            const response = apiResponse.data;
            setReportData(response);

            // 提取能耗信息
            const energyInfo: EnergyConsumption = {
                water: response.water,
                electricity: response.electricity,
                cementContent: response.cementContent
            };
            console.log('能耗信息:', energyInfo);
            console.log('能耗信息详细检查:', {
                water: { value: response.water, type: typeof response.water, isNull: response.water === null },
                electricity: { value: response.electricity, type: typeof response.electricity, isNull: response.electricity === null },
                cementContent: { value: response.cementContent, type: typeof response.cementContent, isNull: response.cementContent === null }
            });
            setEnergyData(energyInfo);

            // 转换计划对比数据 - 处理空字符串和null值
            const getValue = (value: string | null | undefined) => {
                return (value && value.trim() !== '') ? value : '-';
            };

            // 处理单位转换 - 将mm转换为m显示
            const convertUnit = (value: string, targetUnit: string) => {
                if (value === '-' || !value) return value;

                // 如果值包含mm单位，转换为m
                if (value.includes('mm')) {
                    const numValue = parseFloat(value.replace('mm', ''));
                    if (!isNaN(numValue)) {
                        return `${(numValue / 1000).toFixed(3)}${targetUnit}`;
                    }
                }

                // 如果值已经包含目标单位，直接返回
                if (value.includes(targetUnit)) {
                    return value;
                }

                // 如果值是纯数字，添加单位
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    return `${value}${targetUnit}`;
                }

                return value;
            };

            const comparisonInfo: PlanActualComparison[] = [
                {
                    parameter: '开孔角度',
                    designValue: getValue(response.designHoleAngle),
                    practicalValue: getValue(response.practicalHoleAngle),
                    errorValue: getValue(response.holeAngleError),
                    errorRate: getValue(response.holeAngleErrorRate),
                    status: response.holeAngleStatus || 0,
                    remark: getValue(response.holeAngleRemark),
                    unit: '度'
                },
                {
                    parameter: '方位角',
                    designValue: getValue(response.designDirection),
                    practicalValue: getValue(response.practicalDirection),
                    errorValue: getValue(response.directionError),
                    errorRate: getValue(response.directionErrorRate),
                    status: response.directionStatus || 0,
                    remark: getValue(response.directionRemark),
                    unit: '度'
                },
                {
                    parameter: '开孔高度',
                    designValue: getValue(response.designHoleHeight),
                    practicalValue: getValue(response.practicalHoleHeight),
                    errorValue: getValue(response.holeHeightError),
                    errorRate: getValue(response.holeHeightErrorRate),
                    status: response.holeHeightStatus || 0,
                    remark: getValue(response.holeHeightRemark),
                    unit: 'm'
                },
                {
                    parameter: '见煤距离',
                    designValue: getValue(response.designCoalDistance),
                    practicalValue: getValue(response.practicalCoalDistance),
                    errorValue: getValue(response.coalDistanceError),
                    errorRate: getValue(response.coalDistanceErrorRate),
                    status: response.coalDistanceStatus || 0,
                    remark: getValue(response.coalDistanceRemark),
                    unit: 'm'
                },
                {
                    parameter: '见岩距离',
                    designValue: getValue(response.designRockDistance),
                    practicalValue: getValue(response.practicalRockDistance),
                    errorValue: getValue(response.rockDistanceError),
                    errorRate: getValue(response.rockDistanceErrorRate),
                    status: response.rockDistanceStatus || 0,
                    remark: getValue(response.rockDistanceRemark),
                    unit: 'm'
                },
                {
                    parameter: '孔深',
                    designValue: getValue(response.designHoleDepth),
                    practicalValue: getValue(response.practicalHoleDepth),
                    errorValue: getValue(response.holeDepthError),
                    errorRate: getValue(response.holeDepthErrorRate),
                    status: response.holeDepthStatus || 0,
                    remark: getValue(response.holeDepthRemark),
                    unit: 'm'
                }
            ];
            console.log('计划对比数据:', comparisonInfo);
            setComparisonData(comparisonInfo);
        } catch (error: any) {
            console.error('获取施工报告数据失败:', error);
            message.error(`获取施工报告数据失败: ${error?.message || '未知错误'}`);

            // 设置空数据以避免渲染错误
            setEnergyData({ water: null, electricity: null, cementContent: null });
            setComparisonData([]);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (number) {
            // fetchEquipmentUtilization(); // 暂时注释
            fetchConstructionReport();
        }
    }, [number]);

    // 设备利用率表格列定义 - 暂时注释
    // const equipmentColumns: ProColumns<EquipmentUtilization>[] = [
    //     {
    //         title: '设备编号',
    //         dataIndex: 'deviceCode',
    //         width: 120,
    //     },
    //     {
    //         title: '设备名称',
    //         dataIndex: 'deviceName',
    //         width: 150,
    //     },
    //     {
    //         title: '总工作时间',
    //         dataIndex: 'totalWorkTime',
    //         width: 120,
    //         render: (text: any) => `${text}h`,
    //     },
    //     {
    //         title: '实际工作时间',
    //         dataIndex: 'actualWorkTime',
    //         width: 120,
    //         render: (text: any) => `${text}h`,
    //     },
    //     {
    //         title: '利用率',
    //         dataIndex: 'utilizationRate',
    //         width: 120,
    //         render: (text: any) => (
    //             <Progress
    //                 percent={text}
    //                 size="small"
    //                 status={text >= 80 ? 'success' : text >= 60 ? 'normal' : 'exception'}
    //             />
    //         ),
    //     },
    //     {
    //         title: '工作效率',
    //         dataIndex: 'efficiency',
    //         width: 120,
    //         render: (text: any) => `${text}%`,
    //     },
    //     {
    //         title: '操作员',
    //         dataIndex: 'operatorName',
    //         width: 100,
    //     },
    //     {
    //         title: '工作日期',
    //         dataIndex: 'workDate',
    //         width: 120,
    //     },
    // ];



    // 计划对比表格列定义
    const comparisonColumns: ProColumns<PlanActualComparison>[] = [
        {
            title: '参数名称',
            dataIndex: 'parameter',
            width: 120,
        },
        {
            title: '设计值',
            dataIndex: 'designValue',
            width: 100,
            render: (text: any, record: PlanActualComparison) => {
                if (text === '-') return '';
                // 如果数据已包含单位，直接显示；否则添加单位
                if (text.includes('m') || text.includes('度')) {
                    return text;
                }
                return `${text}${record.unit}`;
            },
        },
        {
            title: '实际值',
            dataIndex: 'practicalValue',
            width: 100,
            render: (text: any, record: PlanActualComparison) => {
                if (text === '-') return '';
                // 如果数据已包含单位，直接显示；否则添加单位
                if (text.includes('m') || text.includes('度')) {
                    return text;
                }
                return `${text}${record.unit}`;
            },
        },
        {
            title: '误差值',
            dataIndex: 'errorValue',
            width: 100,
            render: (text: any, record: PlanActualComparison) => {
                if (text === '-') return '';
                const numValue = parseFloat(text);
                const color = numValue > 0 ? '#ff4d4f' : numValue < 0 ? '#52c41a' : '#1890ff';
                // 如果数据已包含单位，直接显示；否则添加单位
                if (text.includes('m') || text.includes('度')) {
                    return <span style={{ color }}>{numValue > 0 ? '+' : ''}{text}</span>;
                }
                return <span style={{ color }}>{numValue > 0 ? '+' : ''}{text}{record.unit}</span>;
            },
        },
        {
            title: '偏差率',
            dataIndex: 'errorRate',
            width: 100,
            render: (text: any) => {
                if (text === '-') return '';
                // 如果已经包含%符号，直接使用，否则添加%
                const displayText = text.includes('%') ? text : `${text}%`;
                const numValue = parseFloat(text.replace('%', ''));
                const color = Math.abs(numValue) > 5 ? '#ff4d4f' : Math.abs(numValue) > 2 ? '#fa8c16' : '#52c41a';
                return <span style={{ color }}>{numValue > 0 ? '+' : ''}{displayText}</span>;
            },
        },
        {
            title: '状态',
            dataIndex: 'status',
            width: 80,
            render: (_: any, record: PlanActualComparison) => {
                const status = record.status;
                const statusConfig = {
                    0: { text: '正常', color: '#52c41a' },
                    1: { text: '异常', color: '#ff4d4f' }
                };
                const config = statusConfig[status as keyof typeof statusConfig] || statusConfig[0];
                return <span style={{ color: config.color }}>{config.text}</span>;
            },
        },
        {
            title: '备注',
            dataIndex: 'remark',
            width: 150,
            render: (text: any) => text === '-' ? '' : text,
        },
    ];

    return (
        <div style={{ padding: '16px', height: '100%', overflow: 'auto' }}>
            {/* 设备利用率模块 - 暂时注释 */}
            {/* <Card title="设备利用率统计" style={{ marginBottom: '24px' }} loading={loading}>
                <Row gutter={16} style={{ marginBottom: '16px' }}>
                    <Col span={12}>
                        <Statistic
                            title="平均利用率"
                            value={equipmentData.length > 0 ?
                                (equipmentData.reduce((sum, item) => sum + item.utilizationRate, 0) / equipmentData.length).toFixed(1) : 0
                            }
                            suffix="%"
                            valueStyle={{ fontSize: '20px', color: '#1890ff' }}
                        />
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title="平均效率"
                            value={equipmentData.length > 0 ?
                                (equipmentData.reduce((sum, item) => sum + item.efficiency, 0) / equipmentData.length).toFixed(1) : 0
                            }
                            suffix="%"
                            valueStyle={{ fontSize: '20px', color: '#52c41a' }}
                        />
                    </Col>
                </Row>
                <ProTable<EquipmentUtilization>
                    columns={equipmentColumns}
                    dataSource={equipmentData}
                    rowKey="deviceCode"
                    search={false}
                    options={false}
                    pagination={false}
                    size="small"
                />
            </Card> */}

            {/* 能耗信息模块 */}
            <Card title="能耗信息统计" style={{ marginBottom: '24px' }} loading={loading}>
                <Row gutter={16} style={{ marginBottom: '16px' }}>
                    <Col span={8}>
                        <Statistic
                            title="总用水量"
                            value={energyData?.water !== null && energyData?.water !== undefined ?
                                (typeof energyData.water === 'number' ? energyData.water.toFixed(1) : energyData.water) : 0}
                            suffix={energyData?.water !== null && energyData?.water !== undefined ? 'm³' : ''}
                            valueStyle={{ fontSize: '20px', color: '#1890ff' }}
                        />
                    </Col>
                    <Col span={8}>
                        <Statistic
                            title="总用电量"
                            value={energyData?.electricity !== null && energyData?.electricity !== undefined ?
                                (typeof energyData.electricity === 'number' ? energyData.electricity.toFixed(1) : energyData.electricity) : 0}
                            suffix={energyData?.electricity !== null && energyData?.electricity !== undefined ? 'kWh' : ''}
                            valueStyle={{ fontSize: '20px', color: '#fa8c16' }}
                        />
                    </Col>
                    <Col span={8}>
                        <Statistic
                            title="总水泥量"
                            value={energyData?.cementContent !== null && energyData?.cementContent !== undefined ?
                                (typeof energyData.cementContent === 'number' ? energyData.cementContent.toFixed(1) : energyData.cementContent) : 0}
                            suffix={energyData?.cementContent !== null && energyData?.cementContent !== undefined ? 't' : ''}
                            valueStyle={{ fontSize: '20px', color: '#722ed1' }}
                        />
                    </Col>
                </Row>
            </Card>

            {/* 计划结果与实际结果对比模块 */}
            <Card title="计划结果与实际结果对比分析" loading={loading}>
                <Row gutter={16} style={{ marginBottom: '16px' }}>
                    <Col span={8}>
                        <Statistic
                            title="正常参数"
                            value={comparisonData.filter(item => item.status === 0).length}
                            suffix={`/ ${comparisonData.length}`}
                            valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                        />
                    </Col>
                    <Col span={8}>
                        <Statistic
                            title="异常参数"
                            value={comparisonData.filter(item => item.status === 1).length}
                            suffix={`/ ${comparisonData.length}`}
                            valueStyle={{ color: '#ff4d4f', fontSize: '18px' }}
                        />
                    </Col>
                    <Col span={8}>
                        <Statistic
                            title="总参数"
                            value={comparisonData.length}
                            valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                        />
                    </Col>
                </Row>
                <ProTable<PlanActualComparison>
                    columns={comparisonColumns}
                    dataSource={comparisonData}
                    rowKey="parameter"
                    search={false}
                    options={false}
                    pagination={false}
                    size="small"
                />
            </Card>
        </div>
    );
};

export default ConstructionReport;
