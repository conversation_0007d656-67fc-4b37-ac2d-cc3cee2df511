import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import styles from './endHoleReport.module.less';
import { getRequest } from '@/services/api/api';

const EndHoleReport: React.FC = (number) => {
  const [data, setData] = useState<any>({});
  const [messageApi, contextHolder] = message.useMessage();
  useEffect(() => {
    console.log(number,'number');
    // 请求接口
    const report = async () => {
      const res = await getRequest('report/get_finish_report', {
        taskId: number.number
      });
      console.log(res,'res');
      const { data, status, msg } = res
      if (status === 0) {
        setData(data);
      } else {
        messageApi.open({
          type: 'error',
          content: msg,
      })
      }
    };
    report();
  }, [number]);
  return (
    <div className={styles.reportContainer}>
      <h2 className={styles.title}>钻孔终孔报告单</h2>

      <div className={styles.headerInfo} style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
        <span>巷道名称：{data.tunnelName}</span>
        <span>钻孔类型：{data.drillingType}</span>
        <span>钻孔位置：{data.drillingPosition}</span>
        <span>日期：{data.endTime}</span>
      </div>      
      <table className={styles.mainTable}>
        <thead>
          <tr>
            <th>钻孔编号</th>
            <th>钻孔参数</th>
            <th>孔径(mm)</th>
            <th>高度(m)</th>
            <th>方位角</th>
            <th>倾角</th>
            <th>列间距(m)</th>
            <th>孔间距(m)</th>
            <th>孔深(m)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td rowSpan={3}>{data.code}</td>
            <td>设计参数</td>
            <td>{data.designDiameter}</td>
            <td>{data.designHeight}</td>
            <td>{data.designAzimuth}</td>
            <td>{data.designDipAngle}</td>
            <td>{data.designColumnSpacing}</td>
            <td>{data.designHoleSpacing}</td>
            <td>{data.designHoleDepth}</td>
          </tr>
          <tr>
            <td>施工参数</td>
            <td>{data.constructionDiameter}</td>
            <td>{data.constructionHeight}</td>
            <td>{data.constructionAzimuth}</td>
            <td>{data.constructionDipAngle}</td>
            <td>{data.constructionColumnSpacing}</td>
            <td>{data.constructionHoleSpacing}</td>
            <td>{data.constructionHoleDepth}</td>
          </tr>
          <tr>
            <td>验收参数</td>
            <td>{data.acceptanceDiameter}</td>
            <td>{data.acceptanceHeight}</td>
            <td>{data.acceptanceAzimuth}</td>
            <td>{data.acceptanceDipAngle}</td>
            <td>{data.acceptanceColumnSpacing}</td>
            <td>{data.acceptanceHoleSpacing}</td>
            <td>{data.acceptanceHoleDepth}</td>
          </tr>
          <tr>
            <td className={styles.label}>开孔日期：</td>
            <td colSpan={4}>{data.startTime}</td>
            <td className={styles.label}>终孔时间：</td>
            <td colSpan={3}>{data.endTime}</td>
          </tr>
          <tr>
            <td className={styles.label}>钻孔结构参数：</td>
            <td colSpan={8}>{data.boreholeStructure}</td>
          </tr>
          <tr>
            <td className={styles.label}>冲孔情况：</td>
            <td colSpan={8}>{data.punchingSituation}</td>
          </tr>
          <tr>
            <td className={styles.label} rowSpan={4}>异常情况描述：</td>
          </tr>
          <tr>
            <td className={styles.label}>是否出现喷孔：</td>
            <td colSpan={3}>
              ({data.orificeStatus === 1 ? '√' : ' '})是 
            ({data.orificeStatus === 0 ? '√' : ' '})否
            </td>
            <td className={styles.label}>喷孔情况：</td>
            <td colSpan={4}>
            ({data.sprayHoleSituation === 1 ? '√' : ' '})轻度 
            ({data.sprayHoleSituation === 2 ? '√' : ' '})中度 
            ({data.sprayHoleSituation === 3 ? '√' : ' '})严重
            </td>
          </tr>
          <tr>
            <td className={styles.label}>是否出现卡钻现象：</td>
            <td colSpan={7}>
              ({data.cavingStatus === 1 ? '√' : ' '})是 
              ({data.cavingStatus === 0 ? '√' : ' '})否
            </td>
          </tr>
          <tr>
            <td className={styles.label}>异常位置(或其他异常情况)描述：</td>
            <td colSpan={7}>{data.abnormalPosition}</td>
          </tr>
          <tr>
            <td className={styles.label}>施工人员：</td>
            <td colSpan={8}>{data.constructor}</td>
          </tr>
          <tr>
            <td className={styles.label}>班长：</td>
            <td colSpan={1}>{data.teamLeader}</td>
            <td className={styles.label}>工区区长：</td>
            <td colSpan={1}>{data.areaLeader}</td>
            <td className={styles.label}>验收人员：</td>
            <td className={styles.label}>现场验收</td>
            <td>{data.siteAcceptance}</td>
            <td className={styles.label}>视频验收：</td>
            <td>{data.videoAcceptance}</td>
          </tr>
          {/* <tr>
            <td className={styles.label}>防突科（区）：</td>
            <td colSpan={1}>你还是</td>
            <td className={styles.label}>防突副总：</td>
            <td colSpan={1}>你还是</td>
            <td className={styles.label}>防突矿长：</td>
            <td colSpan={4}>你还是</td>
          </tr> */}
        </tbody>
      </table>
    </div>
  );
};

export default EndHoleReport;
