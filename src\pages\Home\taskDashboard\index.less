.dashboardContainer {
  // background-color: #000;
  min-height: 100vh;
  padding: 16px;
  color: #fff;
}

.breadcrumb {
  margin-bottom: 16px;
  color: #fff;
}

.topCardRow {
  display: flex;
  flex-wrap: wrap;
  
  :global {
    .ant-col {
      flex: 1 1 0 !important;
      width: 20% !important;
      
      @media (max-width: 768px) {
        width: 50% !important;
      }
      
      @media (max-width: 576px) {
        width: 100% !important;
      }
    }
  }
}

.dashboardCard {
  height: 100%;
  color: #fff;
  // background-color: #141414;
  border-radius: 4px;

  :global {
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #303030;
    }

    .ant-statistic-title {
      color: rgba(255, 255, 255, 0.85);
      font-size: 22px;
    }

    .ant-statistic-content {
      color: #fff;
    }
  }
}

.chartCard {
  height: 100%;
  color: #fff;
  // font-size: 22px;
  // background-color: #141414;
  border-radius: 4px;

  :global {
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #303030;
    }
  }
}

.tableCard {
  height: 100%;
  color: #fff;
  // background-color: #141414;
  border-radius: 4px;

  :global {
    .ant-card-head {
      color: #fff;
      border-bottom: 1px solid #303030;
    }

    .ant-table {
      color: #fff;
      background-color: transparent;
    }

    .ant-table-thead > tr > th {
      color: rgba(255, 255, 255, 0.85);
      background-color: #1f1f1f;
      border-bottom: 1px solid #303030;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #303030;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #1f1f1f;
    }
  }
}

.changeInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  color: rgba(255, 255, 255, 0.65);
  font-size: 20px;
  line-height: 28px;
}

.cardHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #1890ff;
  border-radius: 4px;
}

.rulerIcon {
  color: white;
  font-size: 18px;
}
