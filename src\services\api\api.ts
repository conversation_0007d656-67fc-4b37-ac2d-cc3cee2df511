import { request } from '@umijs/max';
import { message } from 'antd';
export async function getRequest<T>(
  api: string,
  params?: { [key: string]: any },
  options?: { [key: string]: any },
): Promise<T> {
  try {
    const response = await request<T>(`${VITE_APP_BASE_URL}${api}`, {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    });
    // console.log('封装陆游调用', response);

    return response;
  } catch (error) {
    // 处理网络错误或其他异常
    message.error(error.message || '网络错误');
    throw error; // 将错误抛出，以便调用方可以处理
  }
}
export async function postRequest<T>(
  api: string,

  params?: { [key: string]: any },
  options?: { [key: string]: any },
): Promise<T> {
  try {
    const response = await request<T>(`${VITE_APP_BASE_URL}${api}`, {
      // const response = await request<T>(api, {
      method: 'POST',
      data: {
        ...params,
      },
      ...(options || {}),
    });

    return response;
  } catch (error) {
    // 处理网络错误或其他异常
    message.error(error.message || '网络错误');
    throw error; // 将错误抛出，以便调用方可以处理
  }
}

/**
 * 上传抓拍图片
 * @param file 图片文件
 * @param options 其他选项
 */
export async function uploadCaptureImage(
  file: File,
  options?: { [key: string]: any },
): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await request(`${VITE_APP_BASE_URL}/api/file/upload_capture_image`, {
      method: 'POST',
      data: formData,
      requestType: 'form',
      ...(options || {}),
    });

    return response;
  } catch (error) {
    message.error(error.message || '图片上传失败');
    throw error;
  }
}

/**
 * 上传抓拍视频
 * @param file 视频文件
 * @param options 其他选项
 */
export async function uploadCaptureVideo(
  file: File,
  options?: { [key: string]: any },
): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await request(`${VITE_APP_BASE_URL}/api/file/upload_capture_video`, {
      method: 'POST',
      data: formData,
      requestType: 'form',
      ...(options || {}),
    });

    return response;
  } catch (error) {
    message.error(error.message || '视频上传失败');
    throw error;
  }
}

/**
 * 待办审批接口
 * @param params 审批参数
 */
export async function todoApproval(params: {
  id: number;
  action: number; // 1：同意 2：拒绝
  refuse?: string; // 拒绝原因（action为2时必填）
  remark?: string; // 交接班备注
  imageUrls: string[]; // 抓拍图片URL列表
  videoUrls: string[]; // 抓拍视频URL列表
  signUrl: string; // 签名URL
}): Promise<any> {
  try {
    const response = await request(`${VITE_APP_BASE_URL}/todo/post_modify`, {
      method: 'POST',
      data: params,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response;
  } catch (error) {
    message.error(error.message || '审批操作失败');
    throw error;
  }
}
