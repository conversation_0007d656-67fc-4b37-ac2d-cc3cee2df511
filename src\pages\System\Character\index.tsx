import React, { useState }from 'react';
import { Button,Modal, message, Input, DatePicker, Space,Breadcrumb } from 'antd';
import { history } from 'umi';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest, getRequest } from '@/services/api/api';
import { setTempData } from '@/utils/storage';
import { ExclamationCircleFilled } from '@ant-design/icons';

const { RangePicker } = DatePicker;

interface ActionItem {
  id: number;
  title: string;
  type: string;
  className: string;
  productId: string;
  acts: Array<{
    act: string;
    title: string;
  }>;
}

interface TableListItem {
  id: number;
  title: string;
  actions: string;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse<T = any> {
  status: number;
  msg?: string;
  data?: T;
  total?: number;
}

// 解析 actions 并获取所有权限的标题
const getActionTitle = (actions: any): string => {
  try {
    if (typeof actions === 'string') {
      const parsedActions = JSON.parse(actions) as ActionItem[];
      // 返回所有权限的标题，用逗号分隔
      return parsedActions.map(action => action.title).join('、');
    }
    return '';
  } catch (error) {
    console.error('处理 actions 失败:', error);
    return '';
  }
};
const getUserTitle = (userlst: any): string => {
  try {
    if (typeof userlst === 'string') {
      const parsedActions = JSON.parse(userlst) as ActionItem[];
      console.log(parsedActions,'parsedActions');
      
      // 返回所有权限的标题，用逗号分隔
      return parsedActions.users.map(action => action.name).join('、');
    }
    return '';
  } catch (error) {
    console.error('处理 actions 失败:', error);
    return '';
  }
};

// 获取管理员

const Character: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TableListItem | null>(null);
  // 处理删除
  const handleDelete = async (record: TableListItem) => {
    try {
      const result = await getRequest<ApiResponse>('permission/post_del', { id: record.id });
      if (result.status === 0) {
        messageApi.success('删除成功');
        actionRef.current?.reload();
      } else {
        messageApi.error(result.msg || '删除失败');
      }
    } catch (error) {
      messageApi.error('删除失败');
    }finally {
        setDeleteModalVisible(false);
        setCurrentRecord(null);
      }
  };
  // 显示删除确认框
  const showDeleteModal = (record: TableListItem) => {
    setCurrentRecord(record);
    setDeleteModalVisible(true);
  };

  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
    {
      title: '管理组名称',
      dataIndex: 'title',
      width: 150,
      ellipsis: true,
      search: false,
    },
    {
      title: '管理员',
      dataIndex: 'userlst',
      width: 150,
      ellipsis: true,
      search: false,
      render: (_, record) => {
        const titles = getUserTitle(record.userlst);
        return titles || '-';
      },
    },
    {
      title: '权限范围',
      dataIndex: 'actions',
      width: 200,
      ellipsis: true,
      search: false,
      render: (_, record) => {
        const titles = getActionTitle(record.actions);
        return titles || '-';
      },
    },
    {
      title: '最后操作人',
      dataIndex: 'name',
      width: 150,
      ellipsis: true,
      search: false,
    },
    {
      title: '操作时间',
      dataIndex: 'updatedAt',
      width: 180,
      ellipsis: true,
      search: false,
    },
    // {
    //   title: '修改时间',
    //   dataIndex: 'updatedAt',
    //   width: 180,
    //   ellipsis: true,
    //   search: false,
    // },
    {
      title: '操作',
      key: 'action',
      width: 180,
      search: false,
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => {
            history.push(`/system/character/detail?id=${record.id}`);
          }}>详情</a>
          <a style={{marginLeft:'24px'}}  onClick={() => {
           
            history.push('/system/character/add', {
              type: 'edit',
              record: record
            });
          }}>修改</a>
          <a style={{marginLeft:'24px'}} onClick={() => showDeleteModal(record)}>删除</a>
          
        </Space>
      ),
    },
    {
      title: '',
      dataIndex: 'keyWord',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        const label = item.dataIndex as string;
        const status = form.getFieldValue(label);
        const onchange = (value: string) => {
          form.setFieldsValue({ [label]: value });
        };
        return (
          <Input
            value={status}
            onChange={(e) => onchange(e.target.value)}
            placeholder='请输入管理组名称'
          />
        );
      },
    },
    {
      title: '',
      dataIndex: 'dateRange',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        const label = item.dataIndex as string;
        const status = form.getFieldValue(label);
        const onchange = (value: any) => {
          form.setFieldsValue({ [label]: value });
        };
        return (
          <RangePicker
            value={status}
            style={{width:'300px'}}
            onChange={(value) => onchange(value)}
            picker="date"
            format="YYYY-MM-DD"
          />
        );
      },
    },
  ];

  return (
    <>
      {contextHolder}
      <Breadcrumb
                    items={[
                        { title: '首页', },
                        { title: '系统设置', },
                        { title: '权限管理', },
                    ]}
                />
                <div style={{height:'10px'}}></div>
      <ProTable<TableListItem>
        columns={columns}
        actionRef={actionRef}
        request={async (params = {}) => {
          const { current, pageSize, keyWord, dateRange, ...rest } = params;
          
          // 请求参数
          const requestParams = {
            page: current,
            perPage: pageSize,
            keyWord: keyWord,
            dateStart: dateRange?.[0],
            dateEnd: dateRange?.[1]
          };

          try {
            const result = await postRequest<ApiResponse<{items: TableListItem[]; total: number}>>('permission/get_ls', requestParams);
            if (result.status === 0 && result.data) {
              return {
                data: result.data.items || [],
                success: true,
                total: result.data.total || 0,
              };
            }
            return {
              data: [],
              success: false,
              total: 0,
            };
          } catch (error) {
            console.error('获取数据失败:', error);
            messageApi.error('获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        rowKey="id"
        search={{
          defaultCollapsed: false,
          labelWidth: 0,
          span: 4,
        }}
        options={false}
       
        dateFormatter="string"
        headerTitle="权限管理"
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              history.push('/system/character/add');
            }}
          >
            添加权限
          </Button>,
        ]}
      />
       <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <ExclamationCircleFilled style={{ color: '#faad14', fontSize: '22px', marginRight: '12px' }} />
            <span>确认删除</span>
          </div>
        }
        open={deleteModalVisible}
        onOk={() => currentRecord && handleDelete(currentRecord)}
        onCancel={() => {
          setDeleteModalVisible(false);
          setCurrentRecord(null);
        }}
        okText="确定"
        cancelText="取消"
        closeIcon={false}
        width={400}
        className="confirm-modal"
      >
        <div style={{ 
          fontSize: '14px',
          marginLeft: '34px'
        }}>
          确定要删除该权限组吗？
        </div>
      </Modal>
    </>
  );
};

export default Character;
