import React, { useState, useEffect, useRef } from 'react';
import { Button, Modal, message, Input, Table, Select, DatePicker, Tag, Upload } from 'antd';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
    ProForm,
    ProFormText,
    ProFormSelect
} from '@ant-design/pro-components';
import { EllipsisOutlined, ContainerOutlined } from '@ant-design/icons';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import { history } from 'umi';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import { postRequest, getRequest } from '@/services/api/api';
import { setTempData } from '@/utils/storage';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { RangePicker } = DatePicker;


// 定义表格数据类型
interface TableListItem {
    id: number;
    deliverNumber: string;
    taskName: string;
    description: string;
    responsibleTeam: string;
    taskDate: string;
    startTime: string;
    endTime: string;
    constructionRequirements: string;
    constructionDrawings: string;
    taskPerson: string;
    taskRecipient: string;
    receiptDate: string;
    remark: string;
    status: number;
}
interface ApiResponse<T = any> {
    status: number;
    msg?: string;
    data?: T;
    total?: number;
}
const tableListDataSource: TableListItem[] = [];

// 生成20个不同的静态数据
const constructionNames = ['井筒施工', '巷道掘进', '通风系统安装', '排水系统施工', '供电系统安装', '运输系统建设', '支护工程', '钻探工程', '防尘设施安装', '消防系统建设'];
const constructionUnits = ['一一矿业施工队', '二二工程队', '三三建设集团', '四四矿建队', '五五工程公司'];
const constructionPersons = ['张明', '李强', '王伟', '赵刚', '周勇', '吴峰', '尉迟'];
const acceptancePersonnels = ['徐星辰', '陈光', '刘洋', '孙明', '钱伟'];

for (let i = 0; i < 10; i += 1) {
    const randomProgress = Math.floor(Math.random() * 100);
    const randomResult = Math.floor(Math.random() * 2);

    tableListDataSource.push({
        id: i,
        deliverNumber: `CONSTRUCTION${String(i + 1).padStart(3, '0')}`,
        taskName: constructionNames[i % constructionNames.length],
        description: constructionUnits[i % constructionUnits.length],
        responsibleTeam: constructionPersons[i % constructionPersons.length],
        taskDate: `${constructionPersons[(i + 1) % constructionPersons.length]}、${constructionPersons[(i + 2) % constructionPersons.length]}`,
        startTime: `2024-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')}`,
        endTime: `2024-${String((i + 2) % 12 + 1).padStart(2, '0')}-${String((i + 5) % 28 + 1).padStart(2, '0')}`,
        constructionRequirements: `2024-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')} 08:00:00`,
        constructionDrawings: `2024-${String((i + 2) % 12 + 1).padStart(2, '0')}-${String((i + 5) % 28 + 1).padStart(2, '0')} 18:00:00`,
        taskPerson: `${randomProgress}`,
        taskRecipient: acceptancePersonnels[i % acceptancePersonnels.length],
        receiptDate: `2024-${String((i + 3) % 12 + 1).padStart(2, '0')}-${String((i + 6) % 28 + 1).padStart(2, '0')}`,
        remark: i % 3 === 0 ? '请注意施工安全' : i % 3 === 1 ? '按期完成施工任务' : '确保工程质量',
        status: 1
    });
}

interface SelectOption {
    label: string;
    value: string | number;
}

// 添加类型定义
interface ExcelResponse {
    cols: any[];
    rows: any[];
}

interface ApiResult {
    status: number;
    data: any;
    msg?: string;
}

// 添加接口定义
interface DrillPlanOption {
    label: string;  // 打孔计划名称
    value: string | number;  // 打孔计划ID
}

const Deliver: React.FC = () => {
    const ref = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [importModalVisible, setImportModalVisible] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [importLoading, setImportLoading] = useState(false);
    const [excelCols, setExcelCols] = useState([]);
    const [excelRows, setExcelRows] = useState([]);
    const [drillPlanOptions, setDrillPlanOptions] = useState<DrillPlanOption[]>([]);

    // 定义表格列
    const columns: ProColumns<TableListItem>[] = [
        {
            title: '任务编号',
            dataIndex: 'deliverNumber',
            width: 160,
            fixed: 'left',
            ellipsis: true,
            search: false,
        },
        {
            title: '日期时间',
            dataIndex: 'startTime',
            width: 160,
            ellipsis: true,
            search: false,
        },
        {
            title: '施工地点',
            dataIndex: 'position',
            width: 120,
            ellipsis: true,
            search: false,
        },
        {
            title: '钻机编号',
            width: 140,
            dataIndex: 'drillNumber',
            ellipsis: true,
            search: false,
        },
        {
            title: '钻机类型',
            dataIndex: 'deliverName',
            width: 160,
            ellipsis: true,
            search: false,
        },

        {
            title: '班次',
            dataIndex: 'coalWork',
            width: 130,
            ellipsis: true,
            search: false,
        },
        {
            title: '人员',
            dataIndex: 'Person',
            width: 130,
            ellipsis: true,
            search: false,
        },
        {
            title: '孔号',
            dataIndex: 'Person',
            width: 140,
            ellipsis: true,
            search: false,
        },
        {
            title: '孔深',
            dataIndex: 'endTime',
            width: 160,
            ellipsis: true,
            search: false,
        },
        {
            title: '孔径',
            dataIndex: 'actualStarttime',
            width: 200,
            ellipsis: true,
            search: false,

        },
        {
            title: '下发人员',
            dataIndex: 'actualEndtime',
            width: 200,
            ellipsis: true,
            search: false,
        },
        {
            title: '负责人',
            dataIndex: 'actualEndtime',
            width: 200,
            ellipsis: true,
            search: false,
        },
        {
            title: '创建时间',
            dataIndex: 'description',
            width: 200,
            ellipsis: true,
            search: false,
        },
        // {
        //     title: '任务状态',
        //     dataIndex: 'status',
        //     search: false,
        //     width: 170,
        //     valueEnum: {
        //         0: {
        //             text: '待完成',
        //             status: 'Error',
        //         },
        //         1: {
        //             text: '已完成',
        //             status: 'Success',
        //         },
        //         2: {
        //             text: '进行中',
        //             status: 'Warning',
        //         },
        //     },
        // },
        {
            title: '操作',
            valueType: 'option',
            width: 100,
            fixed: 'right',
            render: (_, record) => [
                <a
                    key="detail"
                    onClick={() => {
                        history.push(`/?id=${record.id}`);
                    }}
                >
                    调整
                </a>,
                <a
                    key="detail"
                    onClick={() => {
                        history.push(`/?id=${record.id}`);
                    }}
                >
                    详情
                </a>,
            ],
        },
        {
            title: '',
            dataIndex: 'keyWord',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (<>
                    <Input
                        value={status}
                        onChange={(e) => onchange(e.target.value)}
                        placeholder='请输入任务名称'
                    />
                </>);
            },
        },

        {
            title: '',
            dataIndex: 'dateRange',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (<>
                    <RangePicker
                        value={status}
                        style={{ width: '300px' }}
                        onChange={(value) => onchange(value)}
                        picker="date"
                        format="YYYY-MM-DD"
                    />
                </>);
            },
        },
    ];

    const handleExcelUpload = (file: File) => {
        ExcelRenderer(file, (error, response) => {
            if (error) {
                messageApi.error('文件解析失败');
                return;
            }

            setExcelCols(response.cols);
            setExcelRows(response.rows);
        });
    };

    const uploadProps: UploadProps = {
        onRemove: (file) => {
            const index = fileList.indexOf(file);
            const newFileList = fileList.slice();
            newFileList.splice(index, 1);
            setFileList(newFileList);
            setExcelCols([]);
            setExcelRows([]);
        },
        beforeUpload: (file) => {
            setFileList([...fileList, file]);
            handleExcelUpload(file);
            return false;
        },
        fileList,
        accept: '.xlsx,.xls',
        maxCount: 1
    };

    const handleImport = async () => {
        setImportLoading(true);
        try {
            const formData = new FormData();
            formData.append('file', fileList[0] as any);
            formData.append('upload_type', 'local');
            formData.append('file_name', fileList[0].name);
            formData.append('drillPlanId', form.getFieldValue('drillPlanId'));

            fetch(`${VITE_APP_BASE_URL}drilltask/post_import`, {
                method: 'POST',
                headers: {
                    'authorization':sessionStorage.getItem('token'),
                    'serviceType': 2,
                },
                body: formData,
            })
                .then((res) => res.json())
                .then((res) => {
                    if (res.status === 0) {
                        console.log('导入数据', res);

                        messageApi.success('导入成功');
                        setImportModalVisible(false);
                        setExcelCols([]);
                        setExcelRows([]);
                        setFileList([]);
                        ref.current?.reload();
                    } else {
                        messageApi.error(res.msg || '导入失败');
                    }
                })
                .catch(() => {
                    messageApi.error('导入失败');
                })
                .finally(() => {
                });
        } catch (error) {
            messageApi.error('导入失败');
        } finally {
            setImportLoading(false);
        }
    };

    // 获取打孔计划选项数据
    const fetchDrillPlanOptions = async () => {
        try {
            const result = await getRequest<ApiResult>('drill/plan/select');
            if (result.status === 0 && Array.isArray(result.data)) {
                console.log('选项数据', result);

                const options = result.data.map(item => ({
                    label: item.taskName || item.name,
                    value: item.id
                }));
                setDrillPlanOptions(options);
            } else {
                messageApi.error('获取打孔计划数据失败');
            }
        } catch (error) {
            console.error('获取打孔计划数据出错:', error);
            messageApi.error('获取打孔计划数据失败');
        }
    };

    useEffect(() => {
        //   fetchDrillPlanOptions();
    }, []);

    return (
        <>
            {contextHolder}
            <ProTable<TableListItem>
                headerTitle="下发任务列表"
                actionRef={ref}
                rowKey="id"
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,
                    span: 4,
                }}
                toolBarRender={() => [
                    <Button
                        type="primary"
                        key="add"
                        onClick={() => setImportModalVisible(true)}
                    >
                        下发任务
                    </Button>,
                ]}
                scroll={{ x: 1300 }}
                dataSource={tableListDataSource}
                columns={columns}
            //   options={false}

            />
            <Modal
                title="导入文件"
                open={importModalVisible}
                footer={null}
                onCancel={() => {
                    setImportModalVisible(false);
                    setExcelCols([]);
                    setExcelRows([]);
                    setFileList([]);
                }}
                width={600}

            >
                <ProForm
                    submitter={{
                        render: (props, dom) => {
                            return (
                                <div style={{ textAlign: 'right', marginTop: 16 }}>
                                    <Button
                                        style={{ marginRight: 8 }}
                                        onClick={() => {
                                            setImportModalVisible(false);
                                            setExcelCols([]);
                                            setExcelRows([]);
                                            setFileList([]);
                                        }}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        loading={importLoading}
                                        disabled={fileList.length === 0}
                                        onClick={handleImport}
                                    >
                                        完成
                                    </Button>
                                </div>
                            );
                        },
                    }}
                >


                    <ProForm.Item
                        name="file"
                        label="上传文件"
                        rules={[{ required: true, message: '请上传文件' }]}
                    >
                        <Upload.Dragger
                            {...uploadProps}
                        >
                            <p className="ant-upload-drag-icon" style={{ marginTop: '20px' }}>
                                <ContainerOutlined />
                            </p>
                            <p className="ant-upload-text">点击或将文件拖拽到此区域上传</p>
                            <p className="ant-upload-hint">可支持扩展态： .xlsx, .xls </p>
                        </Upload.Dragger>
                    </ProForm.Item>
                    <ProFormSelect
                        name="drillPlanId"
                        style={{ width: '100%' }}
                        label="打孔计划"
                        placeholder='请选择打孔计划名称'
                        options={drillPlanOptions}
                        rules={[{ required: true, message: '请选择打孔计划' }]}
                    />
                    {excelRows.length > 0 && (
                        <div style={{ marginTop: 16 }}>
                            <h3>数据预览</h3>
                            <div style={{
                                maxHeight: '400px',
                                maxWidth: '100%',
                                overflow: 'auto'
                            }}>
                                <div style={{
                                    minWidth: '1500px',
                                }}>
                                    <OutTable
                                        data={excelRows}
                                        columns={excelCols}
                                        tableClassName="preview-table"
                                        tableHeaderRowClass="preview-header"
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </ProForm>
            </Modal>


        </>
    );

};

export default Deliver;
