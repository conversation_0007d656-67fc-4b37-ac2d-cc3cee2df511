/*
import { Table } from 'antd';

interface ReportProps {
  data: any[];


 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-24 13:11:32
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-24 13:49:03
 * @FilePath: \diy_tfl_pc\src\pages\Project\Construction\component\report.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { message, Segmented, Empty } from 'antd';
import React, { useState, useEffect } from 'react';
import { postRequest } from '@/services/api/api';
import { Chart } from '@antv/g2';

// 定义接口数据类型
interface AlarmItem {
    id: number;
    name: string;
    source: string;
    device: string;
    data: string;
    level: number;
    type: number;
    time: string; // 异常发生时间
    state: number;
    createdAt: string;
    updateAt: string; // 改为 string 类型
    corpId: string;
    taskId: number;
    operator: string;
    account: string;
    depth: string;
    duration: number | null; // 异常持续时间（秒）
}

interface ExceptionStatisticsItem {
    time: string; // 时间字段移到顶层
    depth: string;
    alarms: AlarmItem[];
}

interface ApiResponse {
    status: number;
    msg: string;
    data: ExceptionStatisticsItem[];
}
interface ReportProps {
    number: {
        parentId: string | number;
    };
}

const Report: React.FC<ReportProps> = ({ number }) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [segmentedValue, setSegmentedValue] = useState('看板视图');
    const [chartData, setChartData] = useState<any[]>([]);
    const [tableData, setTableData] = useState<AlarmItem[]>([]);
    const [loading, setLoading] = useState(false);

    // 验证数据格式
    const validateDataStructure = (data: any[]): boolean => {
        if (!Array.isArray(data) || data.length === 0) return false;

        return data.every(item =>
            item &&
            typeof item.time === 'string' &&
            typeof item.depth === 'string' &&
            Array.isArray(item.alarms)
        );
    };

    // 获取异常统计数据
    const fetchExceptionStatistics = async () => {
        try {
            setLoading(true);
            const postData = {
                parentId: number.parentId
            };
            console.log('调用异常统计接口，参数:', postData);
            const result = await postRequest('alarm/get_exception_statistics', postData);
            const { data, status, msg } = result as ApiResponse;
            console.log('异常统计接口响应:', result);

            if (status === 0 && data) {
                console.log('异常统计接口返回数据:', data);

                // // 验证数据格式
                // if (!validateDataStructure(data)) {
                //     messageApi.error('数据格式不正确，请检查接口返回的数据结构');
                //     return;
                // }

                // 处理数据，转换为图表需要的格式
                const processedData = data.map(item => {
                    // 统计各类型异常数量
                    const typeCount = {
                        传感器异常: 0,
                        钻杆顺序异常: 0,
                        钻杆断裂: 0,
                        压钻: 0,
                        瓦斯异常: 0,
                        冒烟: 0,
                        喷孔: 0,
                        见岩偏差过大: 0,
                    };

                    item.alarms.forEach(alarm => {
                        switch (alarm.type) {
                            case 0: typeCount.传感器异常++; break;
                            case 1: typeCount.钻杆顺序异常++; break;
                            case 2: typeCount.钻杆断裂++; break;
                            case 3: typeCount.压钻++; break;
                            case 4: typeCount.瓦斯异常++; break;
                            case 5: typeCount.冒烟++; break;
                            case 6: typeCount.喷孔++; break;
                            case 7: typeCount.见岩偏差过大++; break;
                        }
                    });

                    // 处理持续时间信息
                    const durations = item.alarms
                        .map(alarm => alarm.duration)
                        .filter(d => d !== null && d !== undefined);

                    let durationInfo = '';
                    if (durations.length === 0) {
                        durationInfo = '无持续时间数据';
                    } else if (durations.length === 1) {
                        const d = durations[0];
                        if (isNaN(d) || d < 0) {
                            durationInfo = '无效数据';
                        } else if (d < 60) {
                            durationInfo = `${d}秒`;
                        } else if (d < 3600) {
                            durationInfo = `${Math.floor(d / 60)}分${d % 60}秒`;
                        } else {
                            durationInfo = `${Math.floor(d / 3600)}时${Math.floor((d % 3600) / 60)}分`;
                        }
                    } else {
                        // 多个异常的持续时间处理
                        const validDurations = durations.filter(d => !isNaN(d) && d >= 0);
                        if (validDurations.length === 0) {
                            durationInfo = '无有效持续时间数据';
                        } else {
                            const totalDuration = validDurations.reduce((sum, d) => sum + d, 0);
                            const avgDuration = totalDuration / validDurations.length;
                            const maxDuration = Math.max(...validDurations);
                            const minDuration = Math.min(...validDurations);

                            let totalStr = '';
                            if (totalDuration < 60) totalStr = `${Math.floor(totalDuration)}秒`;
                            else if (totalDuration < 3600) totalStr = `${Math.floor(totalDuration / 60)}分${Math.floor(totalDuration % 60)}秒`;
                            else totalStr = `${Math.floor(totalDuration / 3600)}时${Math.floor((totalDuration % 3600) / 60)}分`;

                            let avgStr = '';
                            if (avgDuration < 60) avgStr = `${Math.floor(avgDuration)}秒`;
                            else if (avgDuration < 3600) avgStr = `${Math.floor(avgDuration / 60)}分${Math.floor(avgDuration % 60)}秒`;
                            else avgStr = `${Math.floor(avgDuration / 3600)}时${Math.floor((avgDuration % 3600) / 60)}分`;

                            durationInfo = `总计:${totalStr}, 平均:${avgStr}, 最长:${Math.floor(maxDuration)}秒, 最短:${Math.floor(minDuration)}秒`;
                        }
                    }

                    // 格式化时间，显示月-日 时:分:秒
                    const formatTime = (timeStr: string) => {
                        try {
                            const date = new Date(timeStr);
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');
                            const hour = date.getHours().toString().padStart(2, '0');
                            const minute = date.getMinutes().toString().padStart(2, '0');
                            const second = date.getSeconds().toString().padStart(2, '0');
                            return `${month}-${day} ${hour}:${minute}:${second}`;
                        } catch {
                            return timeStr;
                        }
                    };

                    return {
                        depth: parseFloat(item.depth) || 0, // 转换为数字，用于 y 轴
                        time: formatTime(item.time), // 格式化时间，只显示时分秒
                        originalTime: item.time, // 保留原始时间用于tooltip
                        totalCount: item.alarms.length,
                        durationInfo: durationInfo, // 添加持续时间信息
                        durationCount: durations.length, // 有持续时间数据的异常数量
                        ...typeCount,
                        alarms: item.alarms
                    };
                });

                console.log('处理后的图表数据（时间格式化）:', processedData.map(item => ({
                    time: item.time,
                    originalTime: item.originalTime,
                    depth: item.depth
                })));

                setChartData(processedData);

                // 处理表格数据：将所有异常记录展开为表格行
                const allAlarms: AlarmItem[] = [];
                const seenAlarmIds = new Set<number>();

                data.forEach(item => {
                    item.alarms.forEach(alarm => {
                        // 检查是否有重复的异常记录（基于ID）
                        if (!seenAlarmIds.has(alarm.id)) {
                            allAlarms.push(alarm);
                            seenAlarmIds.add(alarm.id);
                        } else {
                            console.log(`发现重复异常记录 ID: ${alarm.id}, 名称: ${alarm.name}`);
                        }
                    });
                });
                setTableData(allAlarms);

                // 调试：检查持续时间数据
                console.log('表格数据持续时间检查:', allAlarms.map(alarm => ({
                    id: alarm.id,
                    name: alarm.name,
                    duration: alarm.duration,
                    durationType: typeof alarm.duration
                })));

                // 显示数据摘要
                const totalAlarms = processedData.reduce((sum, item) => sum + item.totalCount, 0);
                const timeRange = processedData.length > 0 ?
                    `${processedData[0]?.time} 至 ${processedData[processedData.length - 1]?.time}` : '';
                console.log(`数据摘要: ${processedData.length}个时间点, 共${totalAlarms}个异常, 时间范围: ${timeRange}`);
                console.log(`表格数据: ${allAlarms.length}条异常记录`);
            } else {
                messageApi.error(msg || '获取异常统计数据失败');
            }
        } catch (error) {
            console.error('获取异常统计数据失败:', error);
            messageApi.error('获取异常统计数据失败');
        } finally {
            setLoading(false);
        }
    };
    const columns: ProColumns[] = [
        {
            title: '异常标题',
            dataIndex: 'name',
            search: false,
            width: 200,
        },
        {
            title: '告警类型',
            dataIndex: 'type',
            width: 120,
            valueEnum: {
                0: { text: '传感器异常', status: 'Warning' },
                1: { text: '钻杆顺序异常', status: 'Error' },
                2: { text: '钻杆断裂', status: 'Error' },
                3: { text: '压钻', status: 'Warning' },
                4: { text: '瓦斯异常', status: 'Error' },
                5: { text: '冒烟', status: 'Error' },
                6: { text: '喷孔', status: 'Error' },
                7: { text: '见岩偏差过大', status: 'Warning' },
            },
            search: false,
        },
        // {
        //     title: '告警等级',
        //     dataIndex: 'level',
        //     width: 100,
        //     valueEnum: {
        //         1: { text: '低', status: 'Default' },
        //         2: { text: '中', status: 'Warning' },
        //         3: { text: '高', status: 'Error' },
        //     },
        //     search: false,
        // },
        {
            title: '处理状态',
            dataIndex: 'state',
            width: 100,
            valueEnum: {
                0: { text: '未解决', status: 'Default' },
                1: { text: '已解决', status: 'Success' },
            },
        },
        {
            title: '孔深',
            dataIndex: 'depth',
            width: 80,
            render: (text: any) => `${text}m`,
            search: false,
        },
        {
            title: '处理时间',
            dataIndex: 'updateAt',
            width: 180,
            search: false,
            ellipsis: false,
            render: (text: any) => {
                if (!text) return '-';
                try {
                    // 格式化时间显示，去掉年份，横着显示
                    const date = new Date(text);
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hour = date.getHours().toString().padStart(2, '0');
                    const minute = date.getMinutes().toString().padStart(2, '0');
                    const second = date.getSeconds().toString().padStart(2, '0');
                    return (
                        <div style={{
                            whiteSpace: 'nowrap',
                            display: 'inline-block',
                            width: '100%',
                            textAlign: 'left',
                            overflow: 'visible',
                            wordBreak: 'keep-all',
                            fontSize: '12px',
                            lineHeight: '1.2'
                        }}>
                            {`${month}-${day} ${hour}:${minute}:${second}`}
                        </div>
                    );
                } catch {
                    return text;
                }
            },
        },
        {
            title: '异常发生时间',
            dataIndex: 'time',
            width: 180,
            search: false,
            ellipsis: false,
            render: (text: any) => {
                if (!text) return '-';
                try {
                    // 格式化时间显示，去掉年份，横着显示
                    const date = new Date(text);
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    const hour = date.getHours().toString().padStart(2, '0');
                    const minute = date.getMinutes().toString().padStart(2, '0');
                    const second = date.getSeconds().toString().padStart(2, '0');
                    return (
                        <div style={{
                            whiteSpace: 'nowrap',
                            display: 'inline-block',
                            width: '100%',
                            textAlign: 'left',
                            overflow: 'visible',
                            wordBreak: 'keep-all',
                            fontSize: '12px',
                            lineHeight: '1.2'
                        }}>
                            {`${month}-${day} ${hour}:${minute}:${second}`}
                        </div>
                    );
                } catch {
                    return text;
                }
            },
        },
        {
            title: '异常持续时间',
            dataIndex: 'duration',
            width: 100,
            render: (text: any) => {
                // 处理各种可能的空值情况
                if (text === null || text === undefined || text === '' || isNaN(text)) {
                    return '-';
                }

                const duration = Number(text);
                if (isNaN(duration) || duration < 0) {
                    return '-';
                }

                if (duration < 60) return `${duration}秒`;
                if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`;
                return `${Math.floor(duration / 3600)}时${Math.floor((duration % 3600) / 60)}分`;
            },
            search: false,
        },
        {
            title: '负责人',
            dataIndex: 'operator',
            width: 100,
            search: false,
        },
        {
            title: '来源',
            dataIndex: 'source',
            width: 80,
            valueEnum: {
                '0': { text: '自动', status: 'Processing' },
                '1': { text: '手动', status: 'Default' },
            },
            search: false,
        },
        // {
        //     title: '设备序列号',
        //     dataIndex: 'device',
        //     width: 120,
        //     search: false,
        // },
        // {
        //     title: '创建时间',
        //     dataIndex: 'createdAt',
        //     width: 160,
        //     search: false,
        // },
    ]

    // 渲染异常统计折线图
    const renderExceptionChart = () => {
        const container = document.getElementById('alarm-chart');
        if (!container || chartData.length === 0) return;

        // 清空容器
        container.innerHTML = '';

        const chart = new Chart({
            container: 'alarm-chart',
            autoFit: true,
            height: 400,
            theme: 'dark',
        });

        // 配置图表，x轴显示时间，y轴显示孔深
        chart
            .data(chartData)
            .encode('x', 'time')
            .encode('y', 'depth')
            .scale('x', {
                range: [0, 1],
                type: 'point', // 使用点刻度，适合离散的时间点
            })
            .scale('y', {
                domainMin: 0,
                nice: true,
            })
            .axis('x', {
                title: false,
                labelFormatter: (text: string) => text, // 直接显示格式化后的时间
                labelRotate: 0, // 确保标签水平显示，不旋转
                labelAlign: 'center',
                labelStyle: {
                    fontSize: 10,
                    fill: '#999',
                    textAlign: 'center'
                },
                labelAutoRotate: false, // 禁用自动旋转
                labelAutoHide: true, // 启用自动隐藏，避免重叠
                labelOverlap: 'hide', // 重叠时隐藏部分标签
                tickCount: Math.min(chartData.length, 8) // 限制刻度数量，避免过于拥挤
            })
            .axis('y', {
                title: false,
                labelFormatter: (value: number) => `${value}m`
            })
            .line()
            .style({
                lineWidth: 3,
                stroke: '#1890ff',
            });

        // 添加数据点
        chart
            .point()
            .data(chartData)
            .encode('x', 'time')
            .encode('y', 'depth')
            .style({
                fill: '#1890ff',
                stroke: '#fff',
                strokeWidth: 2,
                r: 6,
                cursor: 'pointer',
            })
            .tooltip({
                title: (d: any) => {
                    const multipleAlarms = d.totalCount > 1 ? ' (多条异常)' : '';
                    // 使用原始时间显示完整的时间信息
                    return `时间: ${d.originalTime || d.time}${multipleAlarms}`;
                },
                items: [
                    { field: 'depth', name: '孔深', valueFormatter: (d: any) => `${d}m` },
                    { field: 'totalCount', name: '总异常数', valueFormatter: (d: any) => `${d}起` },
                    { field: 'durationInfo', name: '异常持续时间', valueFormatter: (d: any) => d || '无数据' },
                    { field: 'durationCount', name: '有时长数据', valueFormatter: (d: any) => `${d}条` },
                    { field: '传感器异常', name: '传感器异常', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '钻杆顺序异常', name: '钻杆顺序异常', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '钻杆断裂', name: '钻杆断裂', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '压钻', name: '压钻', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '瓦斯异常', name: '瓦斯异常', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '冒烟', name: '冒烟', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '喷孔', name: '喷孔', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                    { field: '见岩偏差过大', name: '见岩偏差过大', valueFormatter: (d: any) => d > 0 ? `${d}起` : '-' },
                ]
            });

        // 添加交互
        chart.interaction('elementHighlight');

        chart.render();

        // 添加点击事件监听
        chart.on('element:click', () => {
            // 点击折线图时切换到列表视图
            console.log('点击了折线图，切换到列表视图');
            setSegmentedValue('列表视图');
        });
    };

    // 组件挂载时获取数据
    useEffect(() => {
        fetchExceptionStatistics();
    }, [number.parentId]);

    // 监听视图切换和数据变化，渲染图表
    useEffect(() => {
        if (segmentedValue === '看板视图' && chartData.length > 0) {
            // 延迟渲染，确保DOM已挂载
            setTimeout(() => {
                renderExceptionChart();
            }, 100);
        }
    }, [segmentedValue, chartData]);
    return (
        <>
            {contextHolder}

            {/* 数据统计和视图切换器 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <div style={{ fontSize: '14px', color: '#666' }}>
                    {chartData.length > 0 && (
                        <>
                            📊 数据统计：
                            <span style={{ marginLeft: '8px', color: '#1890ff' }}>
                                {chartData.length} 个时间点
                            </span>
                            <span style={{ marginLeft: '16px', color: '#52c41a' }}>
                                {tableData.length} 条异常记录
                            </span>
                            {chartData.some(item => item.totalCount > 1) && (
                                <span style={{ marginLeft: '16px', color: '#fa8c16' }}>
                                    ⚠️ 包含多条异常的时间点
                                </span>
                            )}
                        </>
                    )}
                </div>
                <Segmented<string>
                    options={['看板视图', '列表视图']}
                    value={segmentedValue}
                    onChange={(value) => setSegmentedValue(value)}
                />
            </div>

            {/* 根据选择的视图显示不同内容 */}
            {segmentedValue === '看板视图' ? (
                <div>
                    <div style={{
                        marginBottom: '10px',
                        textAlign: 'center',
                        color: '#999',
                        fontSize: '12px'
                    }}>
                        💡 点击折线图节点可切换到列表视图
                    </div>
                    {loading ? (
                        <div style={{
                            width: '100%',
                            height: '400px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            加载中...
                        </div>
                    ) : chartData.length === 0 ? (
                        <Empty
                            description="暂无异常数据"
                            style={{
                                width: '100%',
                                height: '400px',
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        />
                    ) : (
                        <div id="alarm-chart" style={{ width: '100%', height: '400px' }} />
                    )}
                </div>
            ) : (
                <div>
                    <ProTable
                        columns={columns}
                        dataSource={tableData}
                        rowKey={(record) => `${record.id}-${record.time}`} // 使用组合键避免重复
                        search={false}
                        options={false}
                        loading={loading}
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
                        }}
                        scroll={{ x: 1400 }}
                        size="small"
                        headerTitle="异常记录列表"
                        className="time-display-table"
                        tableStyle={{
                            whiteSpace: 'nowrap'
                        }}
                        components={{
                            body: {
                                cell: (props: any) => {
                                    const { children, ...restProps } = props;
                                    return (
                                        <td {...restProps} style={{
                                            ...restProps.style,
                                            whiteSpace: 'nowrap',
                                            overflow: 'visible',
                                            textOverflow: 'clip'
                                        }}>
                                            {children}
                                        </td>
                                    );
                                }
                            }
                        }}
                    />
                </div>
            )}
        </>
    );
};

export default Report;
