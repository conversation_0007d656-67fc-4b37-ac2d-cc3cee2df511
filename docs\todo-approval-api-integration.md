# 待办详情审批接口集成文档

## 概述

本文档描述了在待办详情页面中集成审批接口 `/todo/post_modify` 的实现。该接口用于处理待办的同意和拒绝操作，支持传递抓拍图片、视频和签名数据。

## API接口规范

### 接口地址
```
POST /todo/post_modify
Content-Type: application/json
```

### 请求参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 待办ID |
| action | integer | 是 | 操作类型：1-同意，2-拒绝 |
| refuse | string | 条件必需 | 拒绝原因（action为2时必填） |
| remark | string | 否 | 交接班备注 |
| imageUrls | array[string] | 是 | 抓拍图片URL列表 |
| videoUrls | array[string] | 是 | 抓拍视频URL列表 |
| signUrl | string | 是 | 签名URL |

## 技术实现

### 1. API服务函数
```typescript
export async function todoApproval(params: {
  id: number;
  action: number; // 1：同意 2：拒绝
  refuse?: string; // 拒绝原因（action为2时必填）
  remark?: string; // 交接班备注
  imageUrls: string[]; // 抓拍图片URL列表
  videoUrls: string[]; // 抓拍视频URL列表
  signUrl: string; // 签名URL
}): Promise<any>
```

### 2. 状态管理
```typescript
// 上传的文件URL列表
const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
const [uploadedVideoUrls, setUploadedVideoUrls] = useState<string[]>([]);
const [uploadedSignUrl, setUploadedSignUrl] = useState<string>('');
```

### 3. URL收集机制

#### 上传接口返回数据格式
```typescript
// 上传接口返回的数据结构
{
    status: 0,
    msg: "request:ok",
    data: "https://minioapi.eykj.cn/cust-dev/capture/images/xxx.jpeg" // 直接是URL字符串
}
```

#### 图片URL收集
```typescript
// 在图片上传成功后保存URL
const uploadResponse = await uploadCaptureImage(imageFile);
if (uploadResponse && uploadResponse.data) {
    // data字段直接是URL字符串
    setUploadedImageUrls(prev => [...prev, uploadResponse.data]);
}
```

#### 视频URL收集
```typescript
// 在视频上传成功后保存URL
const uploadResponse = await uploadCaptureVideo(videoFile);
if (uploadResponse && uploadResponse.data) {
    // data字段直接是URL字符串
    setUploadedVideoUrls(prev => [...prev, uploadResponse.data]);
}
```

#### 签名URL收集
```typescript
// 在签名上传成功后保存URL
const uploadResponse = await uploadCaptureImage(signatureFile);
if (uploadResponse && uploadResponse.data) {
    // data字段直接是URL字符串
    setUploadedSignUrl(uploadResponse.data);
}
```

## 审批流程

### 同意审批流程
1. 用户点击"同意"按钮
2. 弹出签名画板
3. 用户完成签名
4. 点击"确认签名"
5. 上传签名到服务器
6. 调用审批接口（action=1）
7. 更新本地状态
8. 显示成功提示
9. 延迟1.5秒后自动关闭抽屉

```typescript
const handleSignatureConfirm = async () => {
    // 1. 验证签名
    // 2. 上传签名文件
    // 3. 调用审批接口
    const approvalParams = {
        id: todoData.id,
        action: 1, // 同意
        imageUrls: uploadedImageUrls,
        videoUrls: uploadedVideoUrls,
        signUrl: signUrl
    };
    await todoApproval(approvalParams);
};
```

### 拒绝审批流程
1. 用户点击"拒绝"按钮
2. 直接调用审批接口（action=2）
3. 更新本地状态
4. 显示成功提示
5. 延迟1.5秒后自动关闭抽屉

```typescript
const handleApproval = async (stepId: number, approved: boolean) => {
    if (!approved) {
        const approvalParams = {
            id: todoData.id,
            action: 2, // 拒绝
            refuse: '审批拒绝',
            imageUrls: uploadedImageUrls,
            videoUrls: uploadedVideoUrls,
            signUrl: uploadedSignUrl || ''
        };
        await todoApproval(approvalParams);
    }
};
```

## 数据流转

### 完整数据流
```
抓拍图片 → 上传 → 获取URL → 保存到uploadedImageUrls
抓拍视频 → 上传 → 获取URL → 保存到uploadedVideoUrls
签名画板 → 上传 → 获取URL → 保存到uploadedSignUrl
                                    ↓
                            调用审批接口
                                    ↓
                            更新本地状态
```

### 接口调用时机
- **同意操作**: 签名确认后调用
- **拒绝操作**: 点击拒绝按钮后立即调用

## 错误处理

### 1. 数据验证
- 待办数据未加载检查
- 签名画板初始化检查
- 签名内容验证

### 2. 网络错误
- 文件上传失败处理
- 审批接口调用失败处理
- 友好的错误提示

### 3. 状态同步
- 本地状态更新
- 界面状态同步
- 错误状态回滚

## 注意事项

### 1. URL数据完整性
- 确保所有上传的文件都有对应的URL
- 处理上传失败的情况
- 空数组的处理

### 2. 接口参数校验
- action参数的正确性
- refuse参数在拒绝时的必填性
- URL数组的格式正确性

### 3. 用户体验
- 上传进度提示
- 审批操作确认
- 成功/失败反馈

## 测试建议

1. **功能测试**
   - 测试同意审批完整流程
   - 测试拒绝审批流程
   - 测试无抓拍内容的审批

2. **异常测试**
   - 测试网络异常情况
   - 测试文件上传失败
   - 测试接口调用失败

3. **数据测试**
   - 验证URL数据的正确性
   - 验证接口参数的完整性
   - 验证状态同步的准确性

## 文件结构

```
src/
├── services/api/
│   └── api.ts                     # 新增todoApproval函数
├── pages/Todo/components/
│   └── TodoDetail.tsx             # 审批功能集成
└── docs/
    └── todo-approval-api-integration.md  # 本文档
```

## 依赖项

- `@umijs/max`: HTTP请求库
- `antd`: UI组件库
- `react`: 前端框架
- `react-signature-canvas`: 签名画板组件
