import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, Row, Col, Image, Switch, message, Typography, Space, Button, Tag, Pagination, Empty, Spin } from 'antd';
// import { ReloadOutlined, FilterOutlined } from '@ant-design/icons';
import Work from './components/work';
import Parameter from './components/parameter';
import OperationComponent from './components/operation';
import Construction from './components/construction';
import Environmental from './components/environmental';
import Drill from './components/drill';
import Monitor from './components/monitor';
import Call from '@/pages/Call';  // 引入 Call 组件
// import VoiceCall from './components/VoiceCall';
import { getRequest, postRequest } from '@/services/api/api';
import { log } from 'console';
// import { log } from 'console';
const { Text } = Typography;

interface DeviceItem {
    deviceCode: string;
    deviceName: string; // 添加设备名称属性
    normal: 0 | 1; // 0: 正常, 1: 异常;
    workState: 0 | 1; // 0: 正常, 1: 异常
    workMode: number; // 修改为 number 类型来匹配后端数据
}

const Operation: React.FC = () => {
    const [currentPage, setCurrentPage] = React.useState(1);
    const [activeTabKey, setActiveTabKey] = useState<string>('work');
    const [detailTabKey, setDetailTabKey] = useState<string>('parameter');
    const [messageApi, contextHolder] = message.useMessage();
    const [msg, setMsg] = useState('');
    // const [selectedWork, setSelectedWork] = useState<{ id: number; name: string }>();
    const pageSize = 4; // 每页显示数量
    const [devices, setDevices] = useState<DeviceItem[]>([]);
    const [total, setTotal] = useState(0); // 添加总数状态
    const [loading, setLoading] = useState(false);
    const [selectedDeviceId, setSelectedDeviceId] = useState<string>('');
    const [selectedDeviceIdInfo, setSelectedDeviceIdInfo] = useState<any>(null); // 改为any类型，存储设备详细信息对象
    const [gbsId, setGbsId] = useState<string>('');
    const [isInitialized, setIsInitialized] = useState(false); // 添加初始化标记
    const [ysParam, setYsParam] = useState<any>(null);
    // 工作
    const [selectedDeviceIdWork, setSelectedDeviceIdWork] = useState<string>('');
    const [isCallVisible, setIsCallVisible] = useState(false);
    const [currentCallee, setCurrentCallee] = useState('');
    const [showCallModal, setShowCallModal] = useState(false);
    const callComponentRef = useRef<any>(null);
    const [imInitialized, setImInitialized] = useState(false);

    // ... existing code ...

    // 在组件挂载时初始化 IM
    useEffect(() => {
        const token = sessionStorage.getItem('sign');
        const account = sessionStorage.getItem('account');

        if (token && account) {
            // 延迟初始化以确保组件已经完全挂载
            setTimeout(async () => {
                if (callComponentRef.current) {
                    try {
                        await callComponentRef.current.initIMWithDynamicToken();
                        setImInitialized(true);
                        console.log('IM 初始化成功');
                    } catch (error) {
                        console.error('IM 初始化失败:', error);
                        message.error('IM 初始化失败，请刷新页面重试');
                    }
                }
            }, 500); // 增加延迟时间确保组件完全准备就绪
        }
    }, []);

    // 修改语音呼叫处理函数
    const handleVoiceCall = async (deviceCode: string) => {
        if (!imInitialized) {
            message.warning('IM 服务正在初始化，请稍后再试');
            return;
        }

        if (callComponentRef.current) {
            try {
                // 先检查 IM 登录状态
                const isLoggedIn = await callComponentRef.current.checkIMLoginStatus();
                if (!isLoggedIn) {
                    // 如果未登录，尝试重新登录
                    await callComponentRef.current.initIMWithDynamicToken();
                }
                console.log('111111111', deviceCode);

                // 发起呼叫
                callComponentRef.current.makeCallToUser(deviceCode);
            } catch (error) {
                console.error('发起呼叫失败:', error);
                message.error('发起呼叫失败，请重试');
            }
        } else {
            message.error('通话组件未准备就绪');
        }
    };

    // 工作模式映射
    const workStatusMap = {
        0: '空闲',
        1: '工作中',
        // 2: '打孔中',
        // 4: '成孔',
        // 5: '封孔'
    };
    const fetchDevices = async (page: number = currentPage) => {
        try {
            setLoading(true);
            const deviceData = {
                page: page,
                perPage: pageSize,
                offlineState: 1
            }
            const result = await postRequest('device/get_ls', deviceData);
            // console.log('设备列表数据:', result.data);
            const { data, status, msg } = result as any;
            if (status === 0 && data) {
                setDevices(data.items || []);
                setTotal(data.total || 0); // 设置总数
            } else {
                setMsg(msg || '获取信息失败');
                messageApi.error(msg || '获取信息失败');
                setDevices([]);
                setTotal(0);
            }
        } catch (error) {
            console.error('获取信息出错:', error);
            setMsg('获取信息失败');
            messageApi.error('获取信息失败');
            setDevices([]);
            setTotal(0);
        } finally {
            setLoading(false);
        }
    }
    // 获取设备列表数据
    useEffect(() => {
        // 组件挂载时获取第一页数据
        fetchDevices(1);
        setIsInitialized(true);
    }, []);

    // 监听页码变化，重新获取数据
    useEffect(() => {
        // 只有在初始化完成后，页码变化时才重新获取数据
        if (isInitialized) {
            fetchDevices(currentPage);
        }
    }, [currentPage, isInitialized]);
    useEffect(() => {
        if (devices.length > 0 && !selectedDeviceId) {
            // 只有在没有选择设备时才自动选择第一个设备
            deviceInfo(devices[0].deviceCode)
            getDeviceInfo(devices[0].deviceCode)
            getWorkInfo(devices[0].deviceCode)
            setSelectedDeviceId(devices[0].deviceCode);
        }
    }, [devices]);
    const tabList = [
        {
            key: 'parameter',
            tab: '设备参数',
        },
        {
            key: 'operation',
            tab: '设备实时运行信息',
        },
        {
            key: 'construction',
            tab: '施工信息',
        },
        {
            key: 'environmental',
            tab: '环境信息',
        },
    ];
    // 设备信息
    const deviceInfo = async (op: any) => {
        const deviceData = {
            number: op
        }
        const result = await postRequest('device_data/get_detail', deviceData);
        const { data, status, msg } = result as any;
        if (status === 0 && data) {
            // 设置设备详细信息，但不覆盖selectedDeviceId（设备选择状态）
            // selectedDeviceId 应该保持为设备代码字符串，用于设备列表的选择状态
            // selectedDeviceIdInfo 用于存储设备的详细信息对象，供其他组件使用
            console.log('deviceInfo API返回的数据:', data);
            setSelectedDeviceIdInfo(data);
        } else {
            setMsg(msg || '获取信息失败');
            messageApi.error(msg || '获取信息失败');
        }
    }
    // 获取工作里面的信息
    const getWorkInfo = async (deviceId: string) => {
        const deviceData = {
            number: deviceId
        };
        const result = await postRequest('device_data/get_current', deviceData);
        const { data, status, msg } = result as any;
        if (status === 0 && data) {
            setSelectedDeviceIdWork(data)
        } else {
            setMsg(msg || '获取信息失败');
            messageApi.error(msg || '获取信息失败');
        }
    }
    // 获取设备信息
    const getDeviceInfo = async (deviceId: string) => {
        const deviceData = {
            number: deviceId
        };
        const result = await postRequest('device/get_detail', deviceData);
        const { data, status, msg } = result as any;
        if (status === 0 && data) {
            // 不要覆盖 selectedDeviceIdInfo，它应该保留设备详细数据
            // setSelectedDeviceIdInfo(data) // 注释掉这行，避免覆盖设备详细信息
            setGbsId(data.gbsId)

            // 解析ysParam参数
            if (data.ysParam) {
                try {
                    const parsedYsParam = JSON.parse(data.ysParam);
                    console.log('解析的ysParam:', parsedYsParam);
                    setYsParam(parsedYsParam);
                } catch (error) {
                    console.error('解析ysParam失败:', error);
                    setYsParam(null);
                }
            } else {
                setYsParam(null);
            }
        } else {
            setMsg(msg || '获取信息失败');
            messageApi.error(msg || '获取信息失败');
        }
    }

    // 使用 useMemo 优化详细标签页内容，避免不必要的重新渲染
    const detailList: Record<string, React.ReactNode> = useMemo(() => ({
        parameter: <Parameter deviceId={selectedDeviceIdInfo} />,
        operation: <OperationComponent deviceId={selectedDeviceIdInfo} />,
        construction: <Construction deviceId={selectedDeviceIdInfo} />,
        environmental: <Environmental deviceId={selectedDeviceIdInfo} />,
    }), [selectedDeviceIdInfo]);

    const onDetailTabChange = (key: string) => {
        setDetailTabKey(key);
    };

    // 使用 useMemo 优化主要内容列表，避免监控组件不必要的重新渲染
    const contentList: Record<string, React.ReactNode> = useMemo(() => ({
        work: <Work deviceId={selectedDeviceIdWork} />,
        monitor: <Monitor data={selectedDeviceId ? {
            deviceCode: selectedDeviceId  // 传递当前选中的设备code
        } : null} />,
        drill: <Drill deviceId={selectedDeviceId} />,
    }), [selectedDeviceIdWork, selectedDeviceId]);
    const onTabChange = (key: string) => {
        setActiveTabKey(key);
    };

    // 处理分页变化
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        // 重新获取数据的逻辑已经在useEffect中处理
    };

    return (
        <>
            <Row gutter={[16, 16]} style={{ height: '100%' }}>
                {/* 左侧内容区 */}
                <Col flex="auto" style={{ width: 'calc(100% - 400px)' }} >
                    <Space direction="vertical" style={{ width: '100%', height: '100%' }} size={16}>
                        {/* 顶部导航 */}
                        <Card
                            bordered={false}
                        >
                            <Space size="middle">
                                <Button
                                    type={activeTabKey === 'work' ? 'primary' : 'default'}
                                    onClick={() => onTabChange('work')}
                                >
                                    工作
                                </Button>
                                <Button
                                    type={activeTabKey === 'monitor' ? 'primary' : 'default'}
                                    onClick={() => onTabChange('monitor')}
                                >
                                    监控
                                </Button>
                                <Button
                                    type={activeTabKey === 'drill' ? 'primary' : 'default'}
                                    onClick={() => onTabChange('drill')}
                                >
                                    打孔记录
                                </Button>
                            </Space>

                            {/* 内容区域 */}
                            <div style={{
                                marginTop: 16,
                            }}>
                                {contentList[activeTabKey]}
                            </div>
                        </Card>

                        {/* 底部信息 */}
                        <Card
                            style={{ marginTop: 16 }}
                            bordered={false}
                            tabList={tabList}
                            activeTabKey={detailTabKey}
                            onTabChange={onDetailTabChange}
                        >
                            <div style={{
                                background: '#1d1d1d',
                                borderRadius: 10
                            }}>{detailList[detailTabKey]}</div>

                        </Card>
                    </Space>
                </Col>

                {/* 右侧设备列表 */}
                <Col span={5} >
                    <Card
                        title=""
                        bordered={false}
                        style={{ height: '100%', overflowY: 'auto', borderBottom: 'none' }}
                    >
                        <div style={{ paddingBottom: '24px ', color: 'rgba(255, 255, 255, 0.85)', fontWeight: '600', fontSize: '16px' }}>设备列表</div>
                        <Spin spinning={loading} tip="加载中...">
                            <Space direction="vertical" style={{ width: '100%' }}>
                                {devices.length > 0 ? (
                                    devices.map((device) => (
                                        <Card
                                            key={device.deviceCode}
                                            size="small"
                                            bordered={false}
                                            style={{
                                                marginBottom: 8,
                                                cursor: 'pointer',
                                                transition: 'all 0.3s',
                                                border: selectedDeviceId === device.deviceCode
                                                    ? '2px solid #1890ff'
                                                    : '1px solid rgba(255,255,255,0.12)',
                                                backgroundColor: selectedDeviceId === device.deviceCode
                                                    ? 'rgba(24, 144, 255, 0.1)'
                                                    : 'transparent',
                                                boxShadow: selectedDeviceId === device.deviceCode
                                                    ? '0 0 8px rgba(24, 144, 255, 0.3)'
                                                    : 'none',
                                            }}
                                            onClick={() => {
                                                // console.log('device122121',device);
                                                setSelectedDeviceId(device.deviceCode);
                                                deviceInfo(device.deviceCode)
                                                getWorkInfo(device.deviceCode)
                                                getDeviceInfo(device.deviceCode)
                                                setActiveTabKey('work');
                                            }}
                                        >
                                            <Row justify="space-between" align="middle" style={{ marginBottom: 12 }}>
                                                <Col >
                                                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                                        <Text style={{ fontSize: '20px' }}>{device.deviceName}</Text>
                                                        {selectedDeviceId === device.deviceCode && (
                                                            <Tag color="blue" style={{ marginInlineEnd: 0, fontSize: '12px' }}>
                                                                已选择
                                                            </Tag>
                                                        )}
                                                    </div>
                                                </Col>
                                                <Col>
                                                    <Tag style={{ marginInlineEnd: 0 }} color={device.normal === 0 ? 'success' : 'error'}>
                                                        {device.normal === 0 ? '正常' : '异常'}
                                                    </Tag>
                                                </Col>
                                            </Row>
                                            <Row justify="space-between" style={{ marginBottom: 5 }}>
                                                <Col>运行状态</Col>
                                                <Col>
                                                    {device.workState === 1 ? '开机' : '关机'}
                                                </Col>
                                            </Row>
                                            <Row justify="space-between" style={{ marginBottom: 12 }}>
                                                <Col>工作模式</Col>
                                                <Col>
                                                    {workStatusMap[device.workMode as keyof typeof workStatusMap]}
                                                </Col>
                                            </Row>
                                            <Row style={{ width: '100%' }} justify="center">
                                                <Col
                                                    style={{
                                                        width: '100%',
                                                        paddingTop: 8,
                                                        textAlign: 'center',
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            width: '100%',
                                                            borderRadius: 6,
                                                            padding: 6,
                                                            border: '0.5px solid rgba(255, 255, 255, 0.2)',
                                                        }}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleVoiceCall(device.deviceCode);
                                                        }}
                                                    >
                                                        <Image width={20} src="/icons/phone.png" preview={false} />
                                                        <span style={{ margin: 0, padding: 5 }}>语音呼叫</span>
                                                    </div>
                                                </Col>
                                            </Row>
                                        </Card>
                                    ))
                                ) : (
                                    <Empty
                                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        description="暂无数据"
                                        style={{ color: 'rgba(255, 255, 255, 0.45)', marginTop: 24 }}
                                    />
                                )}
                            </Space>
                        </Spin>

                        {/* 分页器 */}
                        {total > 0 && (
                            <Row justify="end" style={{ marginTop: 16 }}>
                                <Pagination
                                    current={currentPage}
                                    pageSize={pageSize}
                                    total={total}
                                    onChange={handlePageChange}
                                    size="small"
                                    showSizeChanger={false}
                                    showTotal={(total, range) =>
                                        `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                                    }
                                />
                            </Row>
                        )}
                    </Card>
                </Col>
            </Row>

            {/* 添加 Call 组件 */}
            <Call ref={callComponentRef} />
        </>
    );
};

export default Operation;
