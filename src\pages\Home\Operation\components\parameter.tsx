import { history } from '@umijs/max';
import { Button, Col, message, Result, Row, Spin } from 'antd';
import { DesktopOutlined, TagOutlined, BarcodeOutlined, EnvironmentOutlined, NumberOutlined, UserOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
interface ParameterProps {
  deviceId: any; // 改为 any 类型，因为传入的是设备信息对象
}
const Parameter: React.FC<ParameterProps> = ({ deviceId }) => {
  const [initialValues, setInitialValues] = useState<any>({});
  useEffect(() => {
    // 确保 deviceId 存在且是对象时才设置
    if (deviceId && typeof deviceId === 'object') {
      setInitialValues(deviceId);
    } else {
      setInitialValues({});
    }
  }, [deviceId]);
  const [messageApi, contextHolder] = message.useMessage();
  const [msg, setMsg] = useState('');
  const [loading, setLoading] = useState<boolean>(false);
  const sections = [
    {
      title: (
        <span>
          <DesktopOutlined style={{ marginRight: '8px' }} />
          设备信息
        </span>
      ),
      items: [
        {
          label: (
            <span>
              <TagOutlined style={{ marginRight: '6px' }} />
              设备名称
            </span>
          ),
          dataIndex: 'deviceName',
        },
        {
          label: (
            <span>
              <BarcodeOutlined style={{ marginRight: '6px' }} />
              设备序列号
            </span>
          ),
          dataIndex: 'deviceCode',
        },
        {
          label: (
            <span>
              <EnvironmentOutlined style={{ marginRight: '6px' }} />
              设备地点
            </span>
          ),
          dataIndex: 'position',
        },
        {
          label: (
            <span>
              <NumberOutlined style={{ marginRight: '6px' }} />
              当前工作孔号
            </span>
          ),
          dataIndex: 'drillNumber',
        },
        {
          label: (
            <span>
              <UserOutlined style={{ marginRight: '6px' }} />
              当前施工人员
            </span>
          ),
          dataIndex: 'userName',
        },
      ],
    },
  ];
  return (
    <>
      {contextHolder}
      <Spin spinning={loading}>
        {msg ? (
          <Result
            status="500"
            title="error"
            subTitle={msg}
            extra={
              <Button
                type="primary"
                onClick={() => {
                  history.go(-1);
                }}
              >
                返回上一层
              </Button>
            }
          />
        ) : (
          <div style={{ background: '#1f1f1f', padding: 24, borderRadius: '10px' }}>
            {sections.map((section, index) => (
              <div key={index} style={{ marginBottom: 24 }}>
                <div
                  style={{
                    color: 'rgba(255, 255, 255, 0.85)',
                    fontSize: 18,
                    marginBottom: 24,
                  }}
                >
                  {section.title}
                </div>
                <Row gutter={[16, 16]}>
                  {section.items.map((item) => (
                    <Col span={6} key={item.dataIndex}>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 10,
                          marginBottom: 5,
                        }}
                      >
                        <div style={{ color: 'rgba(255, 255, 255, 0.45)', fontSize: 16 }}>
                          {item.label}
                        </div>
                        <div style={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 16 }}>
                          {(initialValues && initialValues[item.dataIndex]) || '-'}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            ))}
          </div>
        )}
      </Spin>
    </>
  );
};

export default Parameter;
