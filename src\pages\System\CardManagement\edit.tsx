import React, { useState, useEffect } from 'react';
import { Card, message, Spin } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { history, useSearchParams } from '@umijs/max';
import { cardService } from './service';
import CardForm from './components/CardForm';
import { CardFormData, CardItem } from './types';

const EditCard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [initialValues, setInitialValues] = useState<CardFormData | undefined>();
  const [searchParams] = useSearchParams();
  
  const cardId = searchParams.get('id');

  // 获取卡片详情
  useEffect(() => {
    const fetchCardDetail = async () => {
      if (!cardId) {
        message.error('缺少卡片ID参数');
        history.push('/system/cardmanagement');
        return;
      }

      setDataLoading(true);
      try {
        const result = await cardService.getDetail(Number(cardId));
        if (result.status === 0 && result.data) {
          const cardData: CardItem = result.data;
          setInitialValues({
            cardNumber: cardData.cardNumber,
            cardStatus: cardData.cardStatus,
            employeeId: cardData.employeeId,
            employeeName: cardData.employeeName,
            employeeAccount: cardData.employeeAccount,
            remark: cardData.remark,
          });
        } else {
          message.error(result.msg || '获取卡片详情失败');
          history.push('/system/cardmanagement');
        }
      } catch (error) {
        message.error('获取卡片详情失败，请重试');
        history.push('/system/cardmanagement');
      } finally {
        setDataLoading(false);
      }
    };

    fetchCardDetail();
  }, [cardId]);

  // 处理表单提交
  const handleFinish = async (values: CardFormData) => {
    if (!cardId) {
      message.error('缺少卡片ID参数');
      return;
    }

    setLoading(true);
    try {
      const result = await cardService.update(Number(cardId), values);
      if (result.status === 0) {
        message.success('修改卡片成功');
        history.push('/system/cardmanagement');
      } else {
        message.error(result.msg || '修改失败');
      }
    } catch (error) {
      message.error('修改失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 返回列表页
  const handleBack = () => {
    history.push('/system/cardmanagement');
  };

  if (dataLoading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" tip="正在加载卡片详情..." />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ArrowLeftOutlined
              style={{ cursor: 'pointer' }}
              onClick={handleBack}
            />
            <span>编辑卡片</span>
          </div>
        }
      >
        <div style={{ maxWidth: '600px' }}>
          <CardForm
            initialValues={initialValues}
            onFinish={handleFinish}
            loading={loading}
          />
        </div>
      </Card>
    </div>
  );
};

export default EditCard;
