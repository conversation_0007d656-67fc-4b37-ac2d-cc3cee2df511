
// @ts-ignore
import React, { useRef, useState } from 'react';
import { Button, Flex } from 'antd';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import './index.css'




const App: React.FC = () => {
    const file = 'https://filetest.eykj.cn/tool/网关-云平台数据接口对接.pdf'
    const type = 'pdf'
    const [cols, setCols] = useState([]);
    const [rows, setRows] = useState([]);


    const fileHandler = (event) => {
        console.log('event', event);

        const fileObj = event.target.files[0];


        // 将 fileObj 作为参数传入
        ExcelRenderer(fileObj, (error, response) => {
            if (error) {
                console.log(error);
            } else {
                // this.setState({
                //   cols: response.cols,
                //   rows: response.rows
                // });
                console.log('response', response);
                setCols(response.cols)
                setRows(response.rows)

            }
        });
    };
    return (
        <>
            <input type="file" onChange={(event) => fileHandler(event)} style={{ "padding": "10px" }} />
            <OutTable data={rows} columns={cols} tableClassName="ExcelTable2007" tableHeaderRowClass="heading" />
        </>
    );
}

export default App;