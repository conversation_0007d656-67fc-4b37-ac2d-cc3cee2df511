import React, { useRef, useState } from 'react';
import { Button, Input, Flex, message, Tag, Drawer, Form, Select, DatePicker, Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { postRequest,getRequest } from '@/services/api/api';
import dayjs from 'dayjs';

interface TunnelProps {
    noodlesId?: string;
    onSwitchTab?: (tab: string, data: { id: number; name: string }) => void;
}

interface TunnelItem {
    id: number;
    lanewayId: string;
    name: string;
    type: string;
    length: string;
    width: string;
    height: string;
    tiltAngle: string;
    startTime: string;
    endTime: string;
    status: number;
    responsiblePerson: string;
    remark: string;
    noodlesIds: number;
    noodlesName: string;
    createdAt: string;
    updateAt: string;
    corpId?: string;
}

const Tunnel: React.FC<TunnelProps> = ({ noodlesId, onSwitchTab }) => {
    const ref = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
    });
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [currentRecord, setCurrentRecord] = useState<TunnelItem>();
    const [addDrawerVisible, setAddDrawerVisible] = useState(false);
    const [form] = Form.useForm();
    const [isEdit, setIsEdit] = useState(false);

    const showEditDrawer = (record: TunnelItem) => {
        setIsEdit(true);
        setCurrentRecord(record);
        setAddDrawerVisible(true);
        form.setFieldsValue({
            ...record,
            startTime: record.startTime ? dayjs(record.startTime) : undefined,
            endTime: record.endTime ? dayjs(record.endTime) : undefined,
        });
    };

    const handleDelete = async (record: TunnelItem) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该巷道吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const result = await getRequest('laneway/post_del', {
                        id: record.id
                    });
                    const { status, msg } = result as any;
                    if (status === 0) {
                        messageApi.success('删除成功');
                        ref.current?.reload();
                    } else {
                        messageApi.error(msg || '删除失败');
                    }
                } catch (error) {
                    messageApi.error('删除失败');
                }
            }
        });
    };

    const columns: ProColumns<TunnelItem>[] = [
        {
            title: '巷道编号',
            dataIndex: 'lanewayId',
            width: 150,
            fixed: 'left',
            render: (_, record) => (
                <a onClick={() => {
                    setCurrentRecord(record);
                    setDrawerVisible(true);
                }}>
                    {record.lanewayId}
                </a>
            ),
        },
        {
            title: '巷道名称',
            dataIndex: 'name',
            width: 150,
            ellipsis: true,
        },
        {
            title: '巷道类型',
            dataIndex: 'type',
            width: 120,
        },
        {
            title: '长度(m)',
            dataIndex: 'length',
            width: 100,
            align: 'right',
        },
        {
            title: '宽度(m)',
            dataIndex: 'width',
            width: 100,
            align: 'right',
        },
        {
            title: '高度(m)',
            dataIndex: 'height',
            width: 100,
            align: 'right',
        },
        {
            title: '倾角',
            dataIndex: 'tiltAngle',
            width: 100,
            align: 'right',
        },
        {
            title: '开始时间',
            dataIndex: 'startTime',
            width: 130,
        },
        {
            title: '结束时间',
            dataIndex: 'endTime',
            width: 130,
        },
        {
            title: '使用状态',
            dataIndex: 'status',
            width: 100,
            render: (_, record) => (
                <Tag color={record.status === 1 ? 'blue' : 'default'}>
                    {record.status === 1 ? '使用中' : '未使用'}
                </Tag>
            ),
        },
        {
            title: '责任人',
            dataIndex: 'responsiblePerson',
            width: 120,
            ellipsis: true,
        },
        {
            title: '采面名称',
            dataIndex: 'noodlesName',
            width: 150,
            ellipsis: true,
        },
        {
            title: '备注',
            dataIndex: 'remark',
            width: 200,
            ellipsis: true,
        },
        {
            title: '操作',
            width: 180,
            valueType: 'option',
            fixed: 'right',
            render: (_, record) => [
                <Button
                    key="see"
                    type="link"
                    onClick={() => {
                        onSwitchTab?.('drillsite', {
                            id: record.id,
                            name: record.name
                        });
                    }}
                >
                    查看钻场
                </Button>,
                <Button
                    key="edit"
                    type="link"
                    onClick={() => showEditDrawer(record)}
                >
                    编辑
                </Button>,
                <Button
                    key="delete"
                    type="link"
                    onClick={() => handleDelete(record)}
                >
                    删除
                </Button>,
            ],
        },
    ];

    const handleAdd = async (values: any) => {
        try {
            const formData = {
                ...values,
                startTime: values.startTime.format('YYYY-MM-DD'),
                endTime: values.endTime.format('YYYY-MM-DD'),
                noodlesIds: Number(noodlesId),
                status: Number(values.status),
            };

            const result = await postRequest('laneway/post_add', formData);
            const { status: responseStatus, msg } = result as any;
            if (responseStatus === 0) {
                messageApi.success('添加成功');
                setAddDrawerVisible(false);
                form.resetFields();
                ref.current?.reload();
            } else {
                messageApi.error(msg || '添加失败');
            }
        } catch (error) {
            messageApi.error('添加失败');
        }
    };

    const handleEdit = async (values: any) => {
        try {
            const formData = {
                ...values,
                startTime: values.startTime.format('YYYY-MM-DD'),
                endTime: values.endTime.format('YYYY-MM-DD'),
                noodlesIds: Number(noodlesId),
                status: Number(values.status),
                id: currentRecord?.id,
            };

            const result = await postRequest('laneway/post_modify', formData);
            const { status: responseStatus, msg } = result as any;
            if (responseStatus === 0) {
                messageApi.success('修改成功');
                setAddDrawerVisible(false);
                setIsEdit(false);
                form.resetFields();
                ref.current?.reload();
            } else {
                messageApi.error(msg || '修改失败');
            }
        } catch (error) {
            messageApi.error('修改失败');
        }
    };

    return (
        <>
            {contextHolder}
            <ProTable<TunnelItem>
                columns={columns}
                actionRef={ref}
                scroll={{ x: 'max-content' }}
                sticky
                search={false}
                dateFormatter="string"
                toolbar={{
                    search: <Flex>
                        <Input style={{ width: '250px' }}
                            allowClear
                            value={queryForm.keyWord}
                            onChange={(e) => {
                                setQueryForm({ ...queryForm, keyWord: e.target.value });
                                ref.current?.reload();
                            }}
                            placeholder="搜索" />
                    </Flex>
                }}
                toolBarRender={() => [
                    <Button
                        key="add"
                        type="primary"
                        onClick={() => {
                            setAddDrawerVisible(true);
                        }}
                    >
                        添加巷道
                    </Button>,
                ]}
                request={async (params) => {
                    if (!noodlesId) {
                        return {
                            data: [],
                            success: true,
                            total: 0,
                        };
                    }
                    try {
                        const result = await postRequest('laneway/get_ls', {
                            page: params.current,
                            perPage: params.pageSize,
                            parentId: noodlesId,
                            keyWord: queryForm.keyWord,
                        });
                        const { data, status, msg } = result as any;
                        if (status === 0) {
                            return {
                                data: data?.items || [],
                                success: true,
                                total: data?.total || 0,
                            };
                        } else {
                            messageApi.error(msg || '获取数据失败');
                            return {
                                data: [],
                                success: false,
                                total: 0,
                            };
                        }
                    } catch (error) {
                        messageApi.error('获取数据失败');
                        return {
                            data: [],
                            success: false,
                            total: 0,
                        };
                    }
                }}
            />
            <Drawer
                title={`巷道详情 - ${currentRecord?.name || ''}`}
                width={600}
                open={drawerVisible}
                onClose={() => setDrawerVisible(false)}
                footer={null}
            >
                <div>
                    <p>巷道编号：{currentRecord?.lanewayId}</p>
                    <p>巷道名称：{currentRecord?.name}</p>
                    <p>巷道类型：{currentRecord?.type}</p>
                    <p>长度(m)：{currentRecord?.length}</p>
                    <p>宽度(m)：{currentRecord?.width}</p>
                    <p>高度(m)：{currentRecord?.height}</p>
                    <p>倾角：{currentRecord?.tiltAngle}</p>
                    <p>开始时间：{currentRecord?.startTime}</p>
                    <p>结束时间：{currentRecord?.endTime}</p>
                    <p>使用状态：
                        <Tag color={currentRecord?.status === 1 ? 'blue' : 'default'}>
                            {currentRecord?.status === 1 ? '使用中' : '未使用'}
                        </Tag>
                    </p>
                    <p>责任人：{currentRecord?.responsiblePerson}</p>
                    <p>采面名称：{currentRecord?.noodlesName}</p>
                    <p>备注：{currentRecord?.remark}</p>
                </div>
            </Drawer>
            <Drawer
                title={isEdit ? "修改巷道" : "添加巷道"}
                width={600}
                open={addDrawerVisible}
                onClose={() => {
                    setAddDrawerVisible(false);
                    setIsEdit(false);
                    form.resetFields();
                }}
                closable={false}
                extra={
                    <Button type="text" onClick={() => {
                        setAddDrawerVisible(false);
                        setIsEdit(false);
                        form.resetFields();
                    }}>
                        <CloseOutlined style={{ fontSize: 16 }} />
                    </Button>
                }
                footer={
                    <div style={{ textAlign: 'right' }}>
                        <Button onClick={() => {
                            setAddDrawerVisible(false);
                            setIsEdit(false);
                            form.resetFields();
                        }} style={{ marginRight: 8 }}>
                            取消
                        </Button>
                        <Button type="primary" onClick={() => form.submit()}>
                            提交
                        </Button>
                    </div>
                }
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={isEdit ? handleEdit : handleAdd}
                >
                    <Form.Item
                        name="lanewayId"
                        label="巷道编号"
                        rules={[{ required: true, message: '请输入巷道编号' }]}
                    >
                        <Input placeholder="请输入巷道编号" />
                    </Form.Item>
                    <Form.Item
                        name="name"
                        label="巷道名称"
                        rules={[{ required: true, message: '请输入巷道名称' }]}
                    >
                        <Input placeholder="请输入巷道名称" />
                    </Form.Item>
                    <Form.Item
                        name="type"
                        label="巷道类型"
                        rules={[{ required: true, message: '请输入巷道类型' }]}
                    >
                        <Input placeholder="请输入巷道类型" />
                    </Form.Item>
                    <Form.Item
                        name="length"
                        label="长度(m)"
                        rules={[{ required: true, message: '请输入长度' }]}
                    >
                        <Input placeholder="请输入长度" />
                    </Form.Item>
                    <Form.Item
                        name="width"
                        label="宽度(m)"
                        rules={[{ required: true, message: '请输入宽度' }]}
                    >
                        <Input placeholder="请输入宽度" />
                    </Form.Item>
                    <Form.Item
                        name="height"
                        label="高度(m)"
                        rules={[{ required: true, message: '请输入高度' }]}
                    >
                        <Input placeholder="请输入高度" />
                    </Form.Item>
                    <Form.Item
                        name="tiltAngle"
                        label="倾角"
                        rules={[{ required: true, message: '请输入倾角' }]}
                    >
                        <Input placeholder="请输入倾角" />
                    </Form.Item>
                    <Form.Item
                        name="startTime"
                        label="开始时间"
                        rules={[{ required: true, message: '请选择开始时间' }]}
                    >
                        <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item
                        name="endTime"
                        label="结束时间"
                        rules={[{ required: true, message: '请选择结束时间' }]}
                    >
                        <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item
                        name="status"
                        label="使用状态"
                        rules={[{ required: true, message: '请选择使用状态' }]}
                        initialValue={0}
                    >
                        <Select
                            options={[
                                { label: '未使用', value: 0 },
                                { label: '使用中', value: 1 },
                            ]}
                            placeholder="请选择使用状态"
                        />
                    </Form.Item>
                    <Form.Item
                        name="responsiblePerson"
                        label="责任人"
                        rules={[{ required: true, message: '请输入责任人' }]}
                    >
                        <Input placeholder="请输入责任人" />
                    </Form.Item>
                    <Form.Item
                        name="remark"
                        label="备注"
                        rules={[{ required: true, message: '请输入备注' }]}
                    >
                        <Input.TextArea rows={4} placeholder="请输入备注" />
                    </Form.Item>
                </Form>
            </Drawer>
        </>
    );
};

export default Tunnel; 