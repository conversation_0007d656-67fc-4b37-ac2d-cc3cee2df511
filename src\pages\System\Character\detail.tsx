import React, { useState, useEffect } from 'react';
import { Breadcrumb, message, Card, Row, Col, Result, Button, Spin } from 'antd';
import { history } from '@umijs/max';
import { postRequest } from '@/services/api/api';

interface ActionItem {
  id: number;
  title: string;
  type: string;
  className: string;
  productId: string;
  acts: Array<{
    act: string;
    title: string;
  }>;
}

interface DetailData {
  id: number;
  title: string;
  actions: string;
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

interface ApiResponse {
  status: number;
  msg?: string;
  data?: DetailData;
}

// 解析 actions 并获取所有权限的标题和具体权限
const getActionDetails = (actions: any): { title: string; permissions: string }[] => {
  try {
    if (typeof actions === 'string') {
      const parsedActions = JSON.parse(actions) as ActionItem[];
      return parsedActions.map(action => ({
        title: action.title,
        permissions: action.acts.map(act => act.title).join('、')
      }));
    }
    return [];
  } catch (error) {
    console.error('处理权限列表失败:', error);
    return [];
  }
};
const getUserTitle = (userlst: any): string => {
  try {
    if (typeof userlst === 'string') {
      const parsedActions = JSON.parse(userlst) as ActionItem[];
      console.log(parsedActions,'parsedActions');
      
      // 返回所有权限的标题，用逗号分隔
      return parsedActions.users.map(action => action.name).join('、');
    }
    return '';
  } catch (error) {
    console.error('处理 actions 失败:', error);
    return '';
  }
};
const Detail: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { location } = history;
  const [initialValues, setInitialValues] = useState<DetailData>({} as DetailData);
  const [msg, setMsg] = useState('');
  const [loading, setLoading] = useState<boolean>(false);

  const columns = [
    {
      title: '基本信息',
      items: [
        {
          label: '角色名称',
          dataIndex: 'title',
          span: 12,
        },
        {
          label: '最后操作人',
          dataIndex: 'name',
          span: 12,
        },
        {
          label: '管理人员',
          dataIndex: 'userlst',
          span: 12,
          render: (value: string) => {
            const userTitle = getUserTitle(value);
            return userTitle || '-';
          },
        },
        {
          label: '创建时间',
          dataIndex: 'createdAt',
          span: 12,
        },
        {
          label: '权限范围',
          dataIndex: 'actions',
          span: 12,
          render: (value: string) => {
            const actionDetails = getActionDetails(value);
            return (
              <div>
                {actionDetails.map((detail, index) => (
                  <div key={index} style={{ marginBottom: index < actionDetails.length - 1 ? '16px' : 0 }}>
                    <div>{detail.title}：</div>
                    <div style={{ marginLeft: '20px', marginTop: '8px' }}>
                      {detail.permissions}
                    </div>
                  </div>
                ))}
              </div>
            );
          },
        },
        {
          label: '修改时间',
          dataIndex: 'updatedAt',
          span: 12,
        },
      ]
    },
  ];

  const getIDFromURLUsingSubstring = (url: string): string | null => {
    const idParam = 'id=';
    const startIndex = url.indexOf(idParam);
    if (startIndex === -1) {
      return null;
    }
    const valueStartIndex = startIndex + idParam.length;
    let valueEndIndex = url.indexOf('&', valueStartIndex);
    if (valueEndIndex === -1) {
      valueEndIndex = url.length;
    }
    return url.substring(valueStartIndex, valueEndIndex);
  };

  const getInfo = async (id: string) => {
    setLoading(true);
    try {
      const result = await postRequest<ApiResponse>('permission/get_info', { id });
      if (result.status === 0 && result.data) {
        setInitialValues(result.data);
      } else {
        setMsg(result.msg || '获取数据失败');
        messageApi.error(result.msg || '获取数据失败');
      }
    } catch (error) {
      setMsg('获取数据失败');
      messageApi.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (location.search) {
      const url = location.search;
      const idValue = getIDFromURLUsingSubstring(url);
      if (idValue) {
        getInfo(idValue);
      }
    }
  }, []);

  return (
    <>
      {contextHolder}
      <Breadcrumb
        items={[
          { title: '首页' },
          { title: '系统设置' },
          { title: '权限管理' },
          { title: '权限详情' },
        ]}
      />
      <Spin spinning={loading}>
        {msg ? (
          <Result
            status="500"
            title="error"
            subTitle={msg}
            extra={<Button type="primary" onClick={() => { history.go(-1); }}>返回上一层</Button>}
          />
        ) : (
          <>
            {columns.map((item) => (
              <Card bordered={false} key={item.title} style={{ marginTop: 24 }}>
                <Row gutter={[16, 28]}>
                  <Col span={24}>
                    <div style={{ color: '#adadad', fontSize: '20px' }}>{item.title}</div>
                  </Col>
                  {item.items.map((it) => (
                    <Col span={it.span || 24} key={it.dataIndex}>
                      <div style={{ color: '#7e7e7e', marginBottom: '14px', fontSize: '14px' }}>{it.label}</div>
                      <div style={{ fontSize: '14px' }}>
                        {it.render ? 
                          it.render(initialValues[it.dataIndex]) : 
                          (initialValues[it.dataIndex] === 0 ? '0' : (initialValues[it.dataIndex] || '-'))}
                      </div>
                    </Col>
                  ))}
                </Row>
              </Card>
            ))}
          </>
        )}
      </Spin>
    </>
  );
};

export default Detail;