import { CloseOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Input, Modal, message, Button, Drawer, Form, Flex, Select, Tag, Radio, ConfigProvider, theme, Switch, TimePicker, DatePicker } from 'antd';
import { useState, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';

interface IoTPointProps {
  number: string;
}

type TableListItem = {
  id: number;
  title: string;
  var: string;
  type: number;
  createdAt: string;
  updatedAt: string;
};

export default ({ number }: IoTPointProps) => {
  const ref = useRef<ActionType>();
  const [form] = Form.useForm();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [queryForm, setQueryForm] = useState({
    keyWord: '',
  });
  const [drawerTitle, setDrawerTitle] = useState('添加点位');
  const [editId, setEditId] = useState<number | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const handleEdit = async (record: TableListItem) => {
    setDrawerTitle('修改点位');
    setEditId(record.id);
    try {
      const result = await getRequest('model/get_attribute_info', { id: record.id, number });
      const { status, msg, data } = result as any;
      if (status === 0) {
        form.setFieldsValue({
          title: data.title,
          var: data.var,
          type: data.type,
          unit: data.unit,
          defaultVal: data.defaultVal,
          decimalVal: data.decimalVal,
          max: data.max,
          min: data.min,
        });
        setDrawerVisible(true);
      } else {
        messageApi.error(msg || '获取详情失败');
      }
    } catch (error) {
      messageApi.error('请求失败');
    }
  };

  const handleAdd = () => {
    setDrawerTitle('添加点位');
    setEditId(null);
    form.resetFields();
    setDrawerVisible(true);
  };

  const handleDelete = async (id: number) => {
    setDeleteLoading(true);
    try {
      const result = await getRequest('model/post_del_attribute', { id, number });
      const { status, msg } = result as any;
      if (status === 0) {
        messageApi.success('删除成功');
        Modal.destroyAll();
        ref.current?.reload();
      } else {
        messageApi.error(msg);
      }
    } finally {
      setDeleteLoading(false);
    }
  };

  const columns: ProColumns<TableListItem>[] = [
    {
      title: '点位名称',
      dataIndex: 'title',
      width: 150,
      ellipsis: true,
    },
    {
      title: '点位标识',
      dataIndex: 'var',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数值类型',
      dataIndex: 'type',
      width: 120,
      render: (_, record) => {
        const typeMap = {
          0: { text: '数值', color: 'blue' },
          1: { text: '文本', color: 'green' },
          2: { text: '开关', color: 'orange' },
          3: { text: '时间', color: 'purple' },
          4: { text: '日期', color: 'cyan' },
          5: { text: '频谱', color: 'magenta' },
        };
        const type = typeMap[record.type as keyof typeof typeMap];
        return <Tag color={type.color}>{type.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180,
      ellipsis: true,
    },
    {
      title: '最后修改时间',
      dataIndex: 'updatedAt',
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'option',
      width: 180,
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => handleEdit(record)}
          style={{ marginLeft: '8px' }}
        >
          修改
        </a>,
        <a key="subscribeAlert"
          onClick={() => {
            const darkThemeConfig = {
              token: {
                colorBgElevated: '#141414',
                colorText: '#fff',
                colorIcon: '#fff',
                colorTextHeading: '#fff',
                colorBgMask: 'rgba(0,0,0,0.45)',
                colorBorder: '#303030',
                colorPrimaryHover: '#177ddc',
                colorPrimary: '#177ddc',
              },
              algorithm: theme.darkAlgorithm,
            };
            ConfigProvider.config({
              theme: darkThemeConfig,
            });
            Modal.confirm({
              title: '是否确认删除数据',
              icon: <ExclamationCircleFilled />,
              content: (
                <div>
                  <p>确认删除么？数据一经删除,不可恢复！</p>
                </div>
              ),
              okText: '确认',
              cancelText: '取消',
              closable: true,
              okButtonProps: {
                type: 'primary',
              },
              onOk: () => handleDelete(record.id),
              onCancel: () => Modal.destroyAll(),
            });
          }}
          style={{ marginLeft: '8px' }}
        >
          删除
        </a>,
      ],
    },
  ];

  const onFinish = async (values: any) => {
    const postData = {
      ...values,
      model: number,
      ...(editId ? { id: editId } : {})
    };
    const api = editId ? 'model/post_modify_attribute' : 'model/post_add_attribute';
    const result = await postRequest(api, postData);
    const { status, msg } = result as any;
    if (status === 0) {
      messageApi.success(editId ? '修改成功' : '添加成功');
      setDrawerVisible(false);
      form.resetFields();
      ref.current?.reload();
    } else {
      messageApi.error(msg);
    }
  };

  const formItems = [
    {
      label: '点位名称',
      name: 'title',
      rules: [{ required: true, message: '请输入点位名称!' }],
    },
    {
      label: '点位标识',
      name: 'var',
      rules: [{ required: true, message: '请输入点位标识!' }],
    },
    {
      label: '数值类型',
      name: 'type',
      rules: [{ required: true, message: '请选择数值类型!' }],
      type: 'radio',
      options: [
        { label: '数值', value: 0 },
        { label: '文本', value: 1 },
        { label: '开关', value: 2 },
        { label: '时间', value: 3 },
        { label: '日期', value: 4 },
        { label: '频谱', value: 5 },
      ],
    },
  ];

  const extraFormItems = {
    number: [
      {
        label: '单位',
        name: 'unit',
        rules: [{ required: true, message: '请输入单位!' }],
      },
      {
        label: '默认值',
        name: 'defaultVal',
        rules: [{ required: true, message: '请输入默认值!' }],
      },
      {
        label: '小数位数',
        name: 'decimalVal',
        rules: [{ required: true, message: '请输入小数位数!' }],
      },
      {
        label: '最大值',
        name: 'max',
        rules: [{ required: true, message: '请输入最大值!' }],
      },
      {
        label: '最小值',
        name: 'min',
        rules: [{ required: true, message: '请输入最小值!' }],
      },
    ],
    text: [
      {
        label: '单位',
        name: 'unit',
        rules: [{ required: true, message: '请输入单位!' }],
      },
      {
        label: '默认值',
        name: 'defaultVal',
        rules: [{ required: true, message: '请输入默认值!' }],
      },
    ],
    switch: [
      {
        label: '默认值',
        name: 'defaultVal',
        type: 'switch',
        rules: [{ required: true, message: '请选择默认值!' }],
      },
    ],
    time: [
      {
        label: '默认值',
        name: 'defaultVal',
        type: 'time',
        rules: [{ required: true, message: '请选择默认时间!' }],
      },
    ],
    date: [
      {
        label: '默认值',
        name: 'defaultVal',
        type: 'date',
        rules: [{ required: true, message: '请选择默认日期!' }],
      },
    ],
  };

  return (
    <>
      {contextHolder}
      <ProTable<TableListItem>
        actionRef={ref}
        columns={columns}
        request={async (params) => {
          const postData = {
            page: params.current,
            perPage: params.pageSize,
            number,
            ...(queryForm.keyWord ? { keyword: queryForm.keyWord } : {}),
          };

          try {
            const result = await postRequest('model/get_attribute_ls', postData);
            const { data, status, msg } = result as any;

            if (status === 0) {
              return {
                data: data.items,
                total: data.total,
                success: true,
              };
            }

            messageApi.error(msg || '获取数据失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch (error) {
            messageApi.error('请求失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        toolbar={{
          search: <Flex>
            <Input
              style={{ width: '250px' }}
              allowClear
              value={queryForm.keyWord}
              onChange={(e) => {
                setQueryForm({ ...queryForm, keyWord: e.target.value });
                ref.current?.reload();
              }}
              onPressEnter={() => {
                ref.current?.reload();
              }}
              placeholder="请输入点位名称"
            />
          </Flex>,
          actions: [
            <Button
              key="add"
              type="primary"
              onClick={handleAdd}
            >
              添加点位
            </Button>
          ]
        }}
        rowKey="id"
        search={false}
      />
      <Drawer
        title={drawerTitle}
        width={600}
        open={drawerVisible}
        onClose={() => {
          setDrawerVisible(false);
          form.resetFields();
        }}
        closeIcon={false}
        headerStyle={{ borderBottom: 0 }}
        extra={
          <Button type="text" onClick={() => {
            setDrawerVisible(false);
            form.resetFields();
          }}>
            <CloseOutlined />
          </Button>
        }
        footerStyle={{ borderTop: 0 }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={() => {
              setDrawerVisible(false);
              form.resetFields();
            }} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" onClick={() => form.submit()}>
              确定
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          {formItems.map(item => (
            <Form.Item
              key={item.name}
              label={item.label}
              name={item.name}
              rules={item.rules}
            >
              {item.type === 'select' ? (
                <Select
                  placeholder={`请选择${item.label}`}
                  options={item.options}
                />
              ) : item.type === 'radio' ? (
                <Radio.Group
                  options={item.options}
                />
              ) : (
                <Input placeholder={`请输入${item.label}`} />
              )}
            </Form.Item>
          ))}

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');
              if (type === 0) {
                return (
                  <div>
                    {extraFormItems.number.map(item => (
                      <Form.Item
                        key={item.name}
                        label={item.label}
                        name={item.name}
                        rules={item.rules}
                      >
                        <Input placeholder={`请输入${item.label}`} />
                      </Form.Item>
                    ))}
                  </div>
                );
              } else if (type === 1) {
                return (
                  <div>
                    {extraFormItems.text.map(item => (
                      <Form.Item
                        key={item.name}
                        label={item.label}
                        name={item.name}
                        rules={item.rules}
                      >
                        <Input placeholder={`请输入${item.label}`} />
                      </Form.Item>
                    ))}
                  </div>
                );
              } else if (type === 2) {
                return (
                  <div>
                    {extraFormItems.switch.map(item => (
                      <Form.Item
                        key={item.name}
                        label={item.label}
                        name={item.name}
                        rules={item.rules}
                        valuePropName={item.type === 'switch' ? 'checked' : undefined}
                      >
                        {item.type === 'switch' ? (
                          <Switch />
                        ) : (
                          <Input placeholder={`请输入${item.label}`} />
                        )}
                      </Form.Item>
                    ))}
                  </div>
                );
              } else if (type === 3) {
                return (
                  <div>
                    {extraFormItems.time.map(item => (
                      <Form.Item
                        key={item.name}
                        label={item.label}
                        name={item.name}
                        rules={item.rules}
                      >
                        <TimePicker style={{ width: '100%' }} />
                      </Form.Item>
                    ))}
                  </div>
                );
              } else if (type === 4) {
                return (
                  <div>
                    {extraFormItems.date.map(item => (
                      <Form.Item
                        key={item.name}
                        label={item.label}
                        name={item.name}
                        rules={item.rules}
                      >
                        <DatePicker style={{ width: '100%' }} />
                      </Form.Item>
                    ))}
                  </div>
                );
              }
              return null;
            }}
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
}; 