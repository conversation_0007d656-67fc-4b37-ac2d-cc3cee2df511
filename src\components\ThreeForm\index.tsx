import React, { forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Button, Space, Select, DatePicker, Row, Col } from 'antd';
import type { FormInstance } from 'antd';
import { history } from '@umijs/max';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

const { TextArea } = Input;

export interface Column {
  label: string;
  dataIndex: string;
  type?: 'text' | 'password' | 'select' | 'date' | 'textarea';
  rules?: any[];
  options?: { label: string; value: any }[];
  rows?: number; // 用于textarea的行数
}

interface ThreeFormProps {
  columns: Column[];
  onFinish: (values: any) => void;
  initialValues?: any;
  form: FormInstance;
}

export interface ThreeFormRef {
  form: FormInstance;
}

const ThreeForm = forwardRef<ThreeFormRef, ThreeFormProps>(({
  columns,
  onFinish,
  initialValues,
  form,
}, ref) => {
  // 暴露 form 实例给父组件
  useImperativeHandle(ref, () => ({
    form
  }));

  const formItemStyle = {
    width: '100%',
    backgroundColor: '#141414',
    borderColor: '#434343',
    color: '#fff',
  };

  const renderFormItem = (column: Column) => {
    const placeholder = column.placeholder || `请${column.type === 'select' ? '选择' : '输入'}${column.label}`;

    switch (column.type) {
      case 'password':
        return (
          <Input.Password 
          placeholder={`请输入${column.label}`}
            autoComplete="new-password"
            style={formItemStyle}
          />
        );
      case 'select':
        return (
          <Select 
          placeholder={`请选择${column.label}`}
            getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
            style={{  ...formItemStyle }}
            dropdownStyle={{ borderColor: '#434343' }}
          >
            {column.options?.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        );
      case 'date':
        return (
          <Form.Item noStyle>
            <DatePicker 
              placeholder={`请选择${column.label}`}
              style={{ ...formItemStyle }}
              getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
              format="YYYY-MM-DD"
              onChange={(date, dateString) => {
                form.setFieldValue(column.dataIndex, dateString);
              }}
            />
          </Form.Item>
        );
      case 'textarea':
        return (
          <TextArea 
            placeholder={placeholder}
            rows={column.rows || 4}
            style={{height:'48px',...formItemStyle}}
          />
        );
      default:
        return (
          <Input 
          placeholder={`请输入${column.label}`}
            style={formItemStyle}
          />
        );
    }
  };

  // 将表单项分成三列
  const renderFormItems = () => {
    const formItems: React.ReactNode[] = [];
    const totalColumns = 3; // 固定3列
    
    // 计算每列应该显示的项目
    for (let colIndex = 0; colIndex < totalColumns; colIndex++) {
      const columnItems = columns.filter((_, index) => index % totalColumns === colIndex);
      
      formItems.push(
        <Col 
          key={colIndex} 
          xs={24}
          sm={24}
          md={6}
          lg={6}
          xl={6}
          style={{ 
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {columnItems.map((column) => (
            <Form.Item
              key={column.dataIndex}
              label={
                <span style={{ 
                  color: '#fff',
                  width: '100%',
                  textAlign: 'left'
                }}>
                  {column.label}
                </span>
              }
              name={column.dataIndex}
              rules={column.rules}
              style={{ 
                marginBottom: '24px', 
                width: '100%',
              }}
            >
              {renderFormItem(column)}
            </Form.Item>
          ))}
        </Col>
      );
    }

    return formItems;
  };

  const handleFinish = (values: any) => {
    // 确保所有日期字段都是字符串格式
    const processedValues = { ...values };
    columns.forEach(column => {
      if (column.type === 'date' && processedValues[column.dataIndex]) {
        // 如果是 dayjs 对象，转换为字符串
        if (dayjs.isDayjs(processedValues[column.dataIndex])) {
          processedValues[column.dataIndex] = processedValues[column.dataIndex].format('YYYY-MM-DD');
        }
      }
    });
    onFinish(processedValues);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleFinish}
      initialValues={initialValues}
      style={{ 
        width: '100%',
        maxWidth: '1600px',
        margin: '0 auto',
      }}
    >
      <Row 
        style={{ 
          width: '100%',
          display: 'flex',
          justifyContent: 'space-between',
        }}
        gutter={[0, 24]}
      >
        {renderFormItems()}
      </Row>
    </Form>
  );
});

ThreeForm.displayName = 'ThreeForm';

export default ThreeForm;
