# 摄像头配置API集成总结

## 🎯 **API接口信息**

### **1. 设备详情接口（获取现有配置）**
- **接口地址**: `/device/get_info`
- **请求方法**: GET
- **参数**: `{ id: 设备ID }`
- **用途**: 获取设备详情，包含现有的摄像头配置参数

### **2. 摄像头配置接口（保存配置）**
- **接口地址**: `/device/post_modify`
- **请求方法**: POST
- **Content-Type**: application/json

### **请求参数**
```json
{
  "id": 123,                    // 设备ID (integer, 必需)
  "ysParam": "{\"channelNo\": \"1\", \"deviceSerial\": \"33010175992677797274:33010042991117112440\"}"
}
```

## 🔧 **代码实现**

### **1. 获取设备详情（表单预填充）**
```javascript
// 调用设备详情接口获取现有配置
const result = await getRequest('device/get_info', { id: record.id });
const { status, msg, data } = result as any;

if (status === 0 && data) {
    // 如果有摄像头配置参数，解析并填充表单
    if (data.ysParam) {
        const ysParamData = JSON.parse(data.ysParam);
        cameraForm.setFieldsValue({
            serialNumber: ysParamData.deviceSerial || '',
            channelNumber: ysParamData.channelNo || ''
        });
    } else {
        cameraForm.resetFields();
    }
}
```

### **2. 参数构造（保存配置）**
```javascript
// 构造摄像头配置参数
const ysParam = JSON.stringify({
    channelNo: formValues.channelNumber,        // 通道号
    deviceSerial: formValues.serialNumber      // 设备序列号
});
```

### **3. API调用（保存配置）**
```javascript
// 调用摄像头配置API
const result = await postRequest('device/post_modify', {
    id: currentCameraDevice.id,
    ysParam: ysParam
});
```

### **4. 响应处理（保存配置）**
```javascript
const { status, msg } = result as any;
if (status === 0) {
    messageApi.success('摄像头配置成功');
    setCameraConfigModalVisible(false);
    cameraForm.resetFields();
    ref.current?.reload();  // 刷新表格数据
} else {
    messageApi.error(msg || '摄像头配置失败');
}
```

## 📊 **表单字段映射**

| 表单字段 | API参数 | 说明 | 示例值 |
|---------|---------|------|--------|
| serialNumber | deviceSerial | 设备序列号 | "33010175992677797274:33010042991117112440" |
| channelNumber | channelNo | 通道号 | "1" |

## 🎨 **用户界面**

### **表单字段**
1. **设备序列号**
   - 标签：设备序列号
   - 占位符：请输入设备序列号，格式如：33010175992677797274:33010042991117112440
   - 验证：必填

2. **通道号**
   - 标签：通道号
   - 占位符：请输入通道号，如：1
   - 验证：必填

## 🔍 **调试功能**

### **控制台输出**
配置过程中会输出详细的调试信息：

```javascript
console.log('🎥 摄像头配置请求参数:', {
    id: currentCameraDevice.id,
    ysParam: ysParam,
    parsedYsParam: JSON.parse(ysParam)
});

console.log('🎥 摄像头配置API响应:', result);
```

### **输出示例**
```
🎥 摄像头配置请求参数: {
  id: 123,
  ysParam: "{\"channelNo\":\"1\",\"deviceSerial\":\"33010175992677797274:33010042991117112440\"}",
  parsedYsParam: {
    channelNo: "1",
    deviceSerial: "33010175992677797274:33010042991117112440"
  }
}
🎥 摄像头配置API响应: { status: 0, msg: "success" }
```

## ✅ **功能特点**

1. **双API集成**: 获取设备详情 + 保存摄像头配置
2. **表单预填充**: 自动加载现有配置到表单
3. **参数验证**: 表单字段必填验证
4. **错误处理**: 完整的成功/失败处理逻辑
5. **用户反馈**: 友好的成功/错误消息提示
6. **数据刷新**: 配置成功后自动刷新表格数据
7. **调试支持**: 详细的控制台调试信息
8. **用户体验**: 编辑现有配置而非从空白开始

## 🚀 **使用方法**

1. 在设备管理页面点击"摄像头配置"按钮
2. 系统自动调用设备详情接口，预填充现有配置
3. 编辑设备序列号和通道号（如果需要修改）
4. 点击确认提交配置
5. 查看成功提示和表格数据刷新

现在摄像头配置功能已经完全集成了正确的API接口！
