import { Footer, Question, SelectLang, AvatarDropdown, AvatarName } from '@/components';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history, Link } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import { currentUser as queryCurrentUser } from '@/services/ant-design-pro/api';
import { getRequest, postRequest } from '@/services/api/api';
import React from 'react';
import { message } from 'antd';
import SmartBreadcrumb from '@/components/SmartBreadcrumb';
import { initGlobalErrorHandler } from '@/utils/globalErrorHandler';
const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

// {{ AURA: Add - 初始化全局错误处理，防止萤石云播放器错误导致页面崩溃 }}
initGlobalErrorHandler();

// 添加定时器相关变量
let logoutTimer: NodeJS.Timeout;
const LOGOUT_TIME = 60 * 60 * 1000; // 10秒
// 退出登录
const sysTo = async () => {
  const result = await getRequest('sys/logOut');
  const { status, msg, data } = result as any;
  if (status === 0) {
    return status
  } else {
    // message.error(msg)
  }

};
// 重置定时器的函数
const resetLogoutTimer = () => {
  if (logoutTimer) {
    clearTimeout(logoutTimer);
  }
  logoutTimer = setTimeout(() => {
    sysTo().then(res => {
      if (res === 0) {
        message.error('由于你长时间未操作，请重新登录');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('User');
        history.push(loginPath);
      }
    })
  }, LOGOUT_TIME);
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  console.log('111111111111111111111111111111111111111111111');

  const fetchUserInfo = async () => {
    try {
      const msg = await getRequest('personal/get_info');
      return msg.data;
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };
  // 如果不是登录页面，执行
  const { location } = history;
  if (location.pathname !== loginPath) {
    const token = sessionStorage.getItem('token');
    let currentUser = {}
    if (!token) {
      history.push(loginPath);
    } else {
      currentUser = await fetchUserInfo();
    }
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  const user = sessionStorage.getItem('User');
  let currentUser = {}
  if (user) {
    currentUser = JSON.parse(user)
  }

  // 添加事件监听器
  React.useEffect(() => {
    if (sessionStorage.getItem('token')) {
      // 初始化定时器
      resetLogoutTimer();

      // 添加用户活动监听
      const activityEvents = ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'];
      const handleUserActivity = () => {
        resetLogoutTimer();
      };

      activityEvents.forEach(event => {
        document.addEventListener(event, handleUserActivity);
      });

      // 清理函数
      return () => {
        if (logoutTimer) {
          clearTimeout(logoutTimer);
        }
        activityEvents.forEach(event => {
          document.removeEventListener(event, handleUserActivity);
        });
      };
    }
  }, []);

  return {
    actionsRender: () => [<Question key="doc" />, <SelectLang key="SelectLang" />],
    avatarProps: {
      src: currentUser?.avatar,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    waterMarkProps: {
      content: currentUser?.name,
    },
    // footerRender: () => <Footer />,
    footerRender: () => '',
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      const token = sessionStorage.getItem('token');
      // if (!initialState?.currentUser && location.pathname !== loginPath) {
      //   history.push(loginPath);
      // }
      if (!token && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    // links: isDev
    //   ? [
    //     <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
    //       <LinkOutlined />
    //       <span>OpenAPI 文档</span>
    //     </Link>,
    //   ]
    //   : [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      const { location } = history;
      // 排除登录页面和不需要面包屑的页面
      const excludePaths = ['/user/login', '/user', '/'];
      const shouldShowBreadcrumb = !excludePaths.includes(location.pathname) &&
        !location.pathname.startsWith('/user/');

      return (
        <>
          {shouldShowBreadcrumb && (
            <div style={{
              padding: '16px 24px 0 24px',
              backgroundColor: 'transparent'
            }}>
              <SmartBreadcrumb />
            </div>
          )}
          <div style={{
            padding: shouldShowBreadcrumb ? '0 24px 24px 24px' : '24px'
          }}>
            {children}
          </div>
          {isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};
