# 默认值修改总结

## 🎯 **修改目标**
将所有组件的默认数据从假数据改为0值或空数组，确保在没有真实数据时不显示误导性信息。

## 📊 **组件修改详情**

### **1. GoalAnalysisChart（目标达成分析）**
```javascript
// 修改前
const defaultData = [
  { name: '总进尺', value: 14560 },
  { name: '实际进尺', value: 12456 },
];

// 修改后
const defaultData = [
  { name: '总进尺', value: 0 },
  { name: '实际进尺', value: 0 },
];
```

### **2. EquipmentPieChart（设备投入产出比）**
```javascript
// 修改前
const defaultEquipmentStatusData = [
  { status: '运行', count: 24, color: '#1E40AF' },
  { status: '维修', count: 4, color: '#FF9200' },
  { status: '停机', count: 2, color: '#C2C2C2' },
];
utilizationRate = 80
efficiency = 519

// 修改后
const defaultEquipmentStatusData = [
  { status: '运行', count: 0, color: '#1E40AF' },
  { status: '维修', count: 0, color: '#FF9200' },
  { status: '停机', count: 0, color: '#C2C2C2' },
];
utilizationRate = 0
efficiency = 0
```

### **3. AbnormalStatsChart（异常事件统计）**
```javascript
// 修改前
const defaultAbnormalStatsData = [
  { type: '液压系统', count: 1, color: '#007fff' },
  { type: '泵压异常', count: 2, color: '#FF9200' },
  { type: '瓦斯突出', count: 2, color: '#6c63f0' },
  { type: '其他', count: 7, color: '#C2C2C2' },
];

// 修改后
const defaultAbnormalStatsData = [
  { type: '传感器异常', count: 0, color: '#007fff' },
  { type: '钻杆顺序异常', count: 0, color: '#FF9200' },
  { type: '钻杆断裂', count: 0, color: '#6c63f0' },
  { type: '压钻', count: 0, color: '#8B5CF6' },
  { type: '瓦斯异常', count: 0, color: '#00b042' },
  { type: '冒烟', count: 0, color: '#FF5722' },
  { type: '喷孔', count: 0, color: '#9C27B0' },
  { type: '见岩偏差过大', count: 0, color: '#C2C2C2' },
];
```

### **4. TeamEfficiencyTable（班组效能表格）**
```javascript
// 修改前
const defaultTeamEfficiencyData = [
  { team: 'A组', taskComplete: 87, ... },
  { team: 'B组', taskComplete: 82, ... },
  // ... 更多假数据
];

// 修改后
const defaultTeamEfficiencyData: TeamEfficiencyData[] = [];
```

### **5. EquipmentHealthRanking（设备健康排行榜）**
```javascript
// 修改前
const defaultAllHealthScoreData = [
  { equipment: 'ZJ-1002', score: 92, rank: 1 },
  { equipment: 'ZJ-1003', score: 76, rank: 2 },
  // ... 更多假数据
];

// 修改后
const defaultAllHealthScoreData: HealthScoreData[] = [];
```

### **6. RadarComparisonChart（雷达对比图）**
```javascript
// 修改前
const defaultRadarData = [
  { equipment: 'ZJ-1002', metric: '进程速度', value: 85 },
  { equipment: 'ZJ-1002', metric: '维护成本', value: 75 },
  // ... 更多假数据
];

// 修改后
const defaultRadarData: RadarData[] = [];
```

## ✅ **修改效果**

### **数值类型组件**
- **GoalAnalysisChart**: 显示0值的进度条，完成率显示为0.0%
- **EquipmentPieChart**: 显示0值的饼图，利用率和效率显示为0
- **AbnormalStatsChart**: 显示0值的柱状图，所有异常类型计数为0

### **列表类型组件**
- **TeamEfficiencyTable**: 显示空表格，无数据行
- **EquipmentHealthRanking**: 显示空排行榜，无设备数据
- **RadarComparisonChart**: 显示空雷达图，无对比数据

## 🎯 **用户体验改进**

1. **真实性**: 不再显示误导性的假数据
2. **清晰性**: 用户可以清楚地知道当前没有数据
3. **一致性**: 所有组件都使用统一的默认值策略
4. **专业性**: 符合企业级应用的数据展示标准

## 📝 **注意事项**

- 所有组件仍然保持完整的功能，只是默认值改为0或空
- 当接口返回真实数据时，组件会正常显示实际数据
- 图表组件在数据为0时仍然会渲染，但显示为空状态
- 列表组件在数据为空时显示空表格/列表

现在所有组件都不会显示假数据，只有在接口返回真实数据时才会显示相应的内容。
