/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-10 09:30:53
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import React, { useEffect } from 'react';
import { Chart } from '@antv/g2';
import { postRequest } from '@/services/api/api';
import {message} from 'antd';
const Column: React.FC = (number) => {
  const [messageApi, contextHolder] = message.useMessage();
  const handleEdit = async () => {
    try {
      const result = await postRequest('health/get_device_info',{
        deviceCode:number.number
      });
      const { status, msg, data } = result as any;
      if (status === 0) {
        const obj = data.deviceData
        return obj
      } else {
        messageApi.open({
          type: 'error',
          content: msg,
        });
      }
    } catch (error) {
    }
  };
  useEffect(() => {
    handleEdit().then(res=>{
      if(res){
        console.log(res,'res11111');
        // 站点健康状况数据
      // const energyConsumptionData = [
      //   { day: '周一', value: 320, },
      //   { day: '周二', value: 332, },
      //   { day: '周三', value: 301,  },
      //   { day: '周四', value: 334,  },
      //   { day: '周五', value: 390,},
      //   { day: '周六', value: 330,  },
      //   { day: '周日', value: 320,  },
      // ];
    
      // 将数据转换为分组柱状图所需格式
      const chart = new Chart({
        container: 'column',
        autoFit: true,  // 添加自动适应
        // height: 300,
        theme: 'dark',
      });
  
      chart
      .interval()
      .data(res)
      .encode('x', 'component')
      .encode('y', 'hour')
      .axis('x', { title: false }) // 隐藏横坐标轴标题
      .axis('y', { title: false }) // 
      .style('columnWidthRatio', 0.4)  // 使用 columnWidthRatio 调整宽度
      .tooltip((data: { component: string; hour: number }) => ({
        name: data.component,
        value: data.hour,
    }));
      chart.render();  // 添加渲染调用
  
      // 清理函数
      return () => {
        chart.destroy();
      };
      }
      
    })
  }, []);

  return (
    <div id='column' style={{ width: '100%', height: '300px' }}></div>
  );
};

export default Column;
