/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-14 14:18:11
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-01 14:30:05
 * @FilePath: \diy_tfl_pc\src\pages\Home\Operation\components\work.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { Card, Row, Col, Descriptions } from 'antd';
import { EnvironmentOutlined, SettingOutlined, BarChartOutlined } from '@ant-design/icons';

interface WorkProps {
  props?: string; // 添加一个可选的className属性
}

const Work: React.FC<WorkProps> = (props) => {
  const { deviceId = {} } = props;
  console.log(deviceId, 'qqqqqqqq');

  // 修改工作详情数据结构以匹配图片布局
  const workDetails = [
    {
      title: (
        <span>
          <EnvironmentOutlined style={{ marginRight: '8px' }} />
          工作区域
        </span>
      ),
      items: [
        { label: '当前区域', value: deviceId?.position ?? '-' },
        { label: '工作状态', value: deviceId?.workState ?? '-' },
        { label: '开始时间', value: deviceId?.createdAt ?? '-' },
        { label: '钻进深度', value: deviceId?.drillingDepth ?? '-' },
      ]
    },
    {
      title: (
        <span>
          <SettingOutlined style={{ marginRight: '8px' }} />
          工作参数
        </span>
      ),
      items: [
        { label: '工作模式', value: deviceId?.workMode ?? '-' },
        { label: '推进速度', value: deviceId?.pushSpeed ?? '-' },
        { label: '系统压力', value: deviceId?.systemPre ?? '-' },
        { label: '推进功率', value: deviceId?.pushPower ?? '-' },
      ]
    },
    {
      title: (
        <span>
          <BarChartOutlined style={{ marginRight: '8px' }} />
          工作统计
        </span>
      ),
      items: [
        { label: '累计施工深度', value: deviceId?.workDepth ?? '-' },
        { label: '能耗统计', value: deviceId?.powerConsumption ?? '-' },
        { label: '累计运行时长', value: deviceId?.operatingTime ?? '-' },
        { label: '异常次数', value: deviceId?.count ?? '-' },
      ]
    }
  ];

  return (
    <Row gutter={16}>
      {workDetails.map((section, index) => (
        <Col span={8} key={index}>
          <Card
            title={section.title}
            headStyle={{
              fontSize: '16px',
              fontWeight: 'bold',
              background: '#1a1a1a',
              color: '#fff',
              borderBottom: 'none'
            }}
            style={{
              background: '#1a1a1a',
              color: '#fff',
              marginBottom: '16px'
            }}
          >
            <Descriptions
              layout="vertical"
              column={2}
              style={{
                width: '100%'
              }}
              contentStyle={{
                fontSize: '18px',
                color: '#fff'
              }}
              labelStyle={{
                fontSize: '18px',
                color: '#999'
              }}
              items={section.items.map(item => ({
                key: item.label,
                label: item.label,
                children: item.value
              }))}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default Work;
