import { EllipsisOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Tag } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';

const { RangePicker } = DatePicker;

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}


export default () => {
    const ref = useRef();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });


    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '规则名称',
            dataIndex: 'id',
        },
        {
            title: '规则来源',
            dataIndex: 'name',
        },
        {
            title: '触发设备',
            dataIndex: 'name',
        },
        {
            title: '告警数据',
            dataIndex: 'name',
        },
        {
            title: '告警级别',
            dataIndex: 'sign',
            render: (text, record,) => {
                const options = [
                    {
                        label: '标识1',
                        color: 'blue'
                    },
                    {
                        label: '标识2',
                        color: 'red'
                    },
                ]
                const field = options[text]
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
        },
        {
            title: '告警状态',
            dataIndex: 'type',
            render: (text, record,) => {
                const options = [
                    {
                        label: '类型1',
                        color: 'blue'
                    },
                    {
                        label: '类型2',
                        color: 'red'
                    },
                ]
                const field = options[text]
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
        },
        {
            title: '告警时间',
            dataIndex: 'number',
        },
        {
            title: '处理状态',
            dataIndex: 'updateAt',
        },
        {
            title: '操作',
            key: 'option',
            width: 200,
            valueType: 'option',
            render: () =>
                [
                    <a key="link" onClick={() => { }}>历史记录</a>,
                    <a key="link" onClick={() => { }}>主动采集</a>,
                ]
        },
    ];

    const columns1 = [
        {
            title: '变更字段',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '变更前',
            dataIndex: 'age',
            key: 'age',
        },
        {
            title: '变更后',
            dataIndex: 'address',
            key: 'address',
        },
    ];

    const data1 = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];

    useEffect(() => {
    }, []);

    return (
        <>
            {contextHolder}
            <ProTable<TableListItem>
                actionRef={ref}
                columns={columns}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('6666666666666', params, sorter, filter);
                    let postData = {
                        page: params.current,
                        perPage: params.pageSize
                    }
                    const result = await postRequest('monitoring/get_ls', postData);
                    const { data, status, msg } = result
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        total: total,
                        success: true,
                    });
                }}
                toolbar={{
                    search: <Flex>
                        <Input style={{ width: '250px' }}
                            allowClear
                            value={queryForm.keyWord}
                            onChange={(e) => {
                                console.log('e', e.target.value);
                                setQueryForm({ ...queryForm, keyWord: e.target.value })
                                // ref.current.submit();
                                ref.current.reload();
                            }}
                            placeholder="搜索" />
                    </Flex>
                }}
                rowKey="key"
                search={false}
            />
            <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal>
        </>
    );
};