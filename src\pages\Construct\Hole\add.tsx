import React from 'react';
import { Card, message, Breadcrumb } from 'antd';
import { history } from '@umijs/max';
import SubmitForm, { Column } from '@/components/Form';
import { postRequest } from '@/services/api/api';

interface ApiResponse {
  status: number;
  msg?: string;
  data?: any;
}

const HoleAdd: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();

  // 定义表单列配置
  const columns: Column[] = [
    {
      label: '日期',
      dataIndex: 'date',
      type: 'DatePicker',
      rules: [{ required: true, message: '请选择日期' }],
    },
    {
      label: '施工地点',
      dataIndex: 'position',
      rules: [{ required: true, message: '请输入施工地点' }],
    },
    {
      label: '钻机编号',
      dataIndex: 'drillNumber',
      rules: [{ required: true, message: '请输入钻机编号' }],
    },
    {
      label: '钻孔类型',
      dataIndex: 'drillingType',
      rules: [{ required: true, message: '请输入钻孔类型' }],
    },
    {
      label: '班次',
      dataIndex: 'shift',
      rules: [{ required: true, message: '请输入班次' }],
    },
    {
      label: '开孔人员名称',
      dataIndex: 'personnel',
      rules: [{ required: true, message: '请输入开孔人员名称' }],
    },
    {
      label: '孔号',
      dataIndex: 'holeNumber',
      rules: [{ required: true, message: '请输入孔号' }],
    },
    {
      label: '孔深',
      dataIndex: 'holeDepth',
      rules: [{ required: true, message: '请输入孔深' }],
    },
    {
      label: '孔径',
      dataIndex: 'holeDiameter',
      rules: [{ required: true, message: '请输入孔径' }],
    },
    {
      label: '开孔角度',
      dataIndex: 'holeAngle',
      rules: [{ required: true, message: '请输入开孔角度' }],
    },
    {
      label: '方位',
      dataIndex: 'direction',
      rules: [{ required: true, message: '请输入方位' }],
    },
    {
      label: '开孔高度',
      dataIndex: 'holeHeight',
      rules: [{ required: true, message: '请输入开孔高度' }],
    },
    {
      label: '见煤距离',
      dataIndex: 'coalDistance',
      rules: [{ required: true, message: '请输入见煤距离' }],
    },
    {
      label: '见岩距离',
      dataIndex: 'rockDistance',
      rules: [{ required: true, message: '请输入见岩距离' }],
    },
    {
      label: '扩孔起始距离',
      dataIndex: 'reamingStartDistance',
      rules: [{ required: true, message: '请输入扩孔起始距离' }],
    },
    {
      label: '打钻起始距离',
      dataIndex: 'drillingStartDistance',
      rules: [{ required: true, message: '请输入打钻起始距离' }],
    },
    {
      label: '预计出煤量',
      dataIndex: 'estimatedCoalOutput',
      rules: [{ required: true, message: '请输入预计出煤量' }],
    },
    {
      label: '开孔角度误差',
      dataIndex: 'holeAngleError',
      rules: [{ required: true, message: '请输入开孔角度误差' }],
    },
    {
      label: '开孔方位误差',
      dataIndex: 'holeDirectionError',
      rules: [{ required: true, message: '请输入开孔方位误差' }],
    },
    {
      label: '开孔高度误差',
      dataIndex: 'holeHeightError',
      rules: [{ required: true, message: '请输入开孔高度误差' }],
    },
    {
      label: '孔深误差',
      dataIndex: 'holeDepthError',
      rules: [{ required: true, message: '请输入孔深误差' }],
    },
    {
      label: '是否需要轨迹',
      dataIndex: 'ifTrajectoryRequired',
      type: 'Select',
      rules: [{ required: true, message: '请选择是否需要轨迹' }],
      options: [
        { label: '不需要', value: 0 },
        { label: '需要', value: 1 },
      ],
    },
  ];

  const onFinish = async (values: any) => {
    const postData = {
      ...values,
    };

    try {
      const result = await postRequest<ApiResponse>('drill/post_add', postData);
      const { status, msg } = result;
      
      if (status === 0) {
        messageApi.open({
          type: 'success',
          content: '提交成功',
        });
        history.go(-1);
      } else {
        messageApi.open({
          type: 'error',
          content: msg,
        });
      }
    } catch (error) {
      messageApi.open({
        type: 'error',
        content: '提交失败',
      });
    }
  };

  return (
    <>
      {contextHolder}
      <Breadcrumb
        items={[
          { title: '首页' },
          { title: '施工管理' },
          { title: '打孔管理' },
          { title: '新增打孔计划' },
        ]}
      />
      <Card style={{ marginTop: '24px' }}>
        <SubmitForm
          columns={columns}
          onFinish={onFinish}
        />
      </Card>
    </>
  );
};

export default HoleAdd; 