/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-03 11:01:38
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import { getRequest } from '@/services/api/api';
import { Card, Col, Image, Row, Statistic, Table, message } from 'antd';
import React, { useEffect, useState } from 'react';
import Column from './components/column';
import Line from './components/line';
import Pie from './components/pie';
import styles from './index.less';
// 异常数据类型定义
export type AbnormalItem = {
  key: string;
  time: string;
  stationId: string;
  abnormalType: string;
  status: '处理中' | '已解决' | '待处理' | '特处理';
};

// 添加图表数据类型定义
type StationHealthData = {
  taskSumYes: number;
  taskNo: number;
  taskSum: number;
};

const TaskDashboard: React.FC = () => {
  // 状态定义
  const [totalDistance, setTotalDistance] = useState('');
  const [distanceChange, setDistanceChange] = useState('');
  const [stationCount, setStationCount] = useState('');
  const [operationRate, setOperationRate] = useState('');
  const [taskCount, setTaskCount] = useState('');
  const [taskCompletionRate, setTaskCompletionRate] = useState('');
  const [abnormalCount, setAbnormalCount] = useState('');
  const [abnormalChange, setAbnormalChange] = useState('');
  const [abnormalData, setAbnormalData] = useState<AbnormalItem[]>([]);
  // 新增能耗指标
  const [energyUsage, setEnergyUsage] = useState('');
  const [operatingSum, setOperatingSum] = useState('');
  const [operatingRate, setOperatingRate] = useState('');
  // 饼状图
  const [stationHealthData, setStationHealthData] = useState<StationHealthData>({
    taskSumYes: 0,
    taskNo: 0,
    taskSum: 0,
  });
  // 树状图
  const [energyConsumptionData, setEnergyConsumptionData] = useState<any>([]);
  // 折线图
  const [lineData, setLineData] = useState<any>([]);
  const handleEdit = async () => {
    try {
      const result = await getRequest('dataBoard/get_ls');
      const { status, msg, data } = result as any;
      if (status === 0) {
        // 总进尺
        setTotalDistance(data.footageSum);
        setDistanceChange(data.footageEd);

        // 运行站机数
        setStationCount(data.runSum);
        setOperationRate(data.runRate);
        // 总任务数
        setTaskCount(data.taskSum);
        setTaskCompletionRate(data.taskRate);
        // 异常时间
        setAbnormalCount(data.abnormalSum);
        setAbnormalChange(data.abnormalEd);
        setAbnormalData(data.abnormalDateList);
        // 能耗指标 - 如API中有此数据则使用，否则使用默认值
        setOperatingSum(data.operatingSum);
        setOperatingRate(data.operatingRate);

        setStationHealthData({
          taskSumYes: data.taskSumYes,
          taskNo: data.taskNo,
          taskSum: data.taskSum,
        });
        // 树状图
        setEnergyConsumptionData(JSON.parse(data.expendSum));
        // 折线图
        setLineData(JSON.parse(data.runTime));
      } else {
        message.error(msg);
      }
    } catch (error) { }
  };
  // 模拟获取数据
  useEffect(() => {
    handleEdit();
  }, []);
  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'time',
    },
    {
      title: '钻机名称',
      dataIndex: 'deviceName',
      key: 'stationId',
    },
    {
      title: '异常类型',
      dataIndex: 'name',
      key: 'name',
      // render: (status: number) => {
      //   let text = '';
      //   if (status === 0) {
      //     text = '型号告警';
      //   } else if (status === 1) {
      //     text = '部件告警';
      //   } else {
      //     text = '未知告警';
      //   }
      //   return <span>{text}</span>;
      // },
    },
    {
      title: '状态',
      dataIndex: 'type',
      // key: 'status',
      render: (status: number) => {
        let color = 'green';
        let text = '';
        if (status === 0) {
          color = '#EF4444';
          text = '未处理';
        } else if (status === 1) {
          color = '#22C55E';
          text = '已处理';
        }
        return <span style={{ color: color }}>{text}</span>;
      },
    },
  ];
  return (
    <div className={styles.dashboardContainer}>
      {/* <Breadcrumb
        items={[{ title: '首页' }, { title: '任务看板' }]}
        className={styles.breadcrumb}
      /> */}

      <Row gutter={[16, 16]} style={{ display: 'flex', flexWrap: 'wrap' }}>
        <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="总进尺"
                value={totalDistance}
                precision={1}
                suffix="米"
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/chizi.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>
                较昨日 {distanceChange} 米{' '}
                <Image width={14} src="/icons/yishang.png" preview={false} />
              </span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="运行钻机数"
                value={stationCount}
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/zuanji.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>运行率 {operationRate}%</span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="总任务数"
                value={taskCount}
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/renwu.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>任务完成率: {taskCompletionRate}%</span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={4.8} style={{ flex: '1 1 0%', minWidth: '19%' }}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="异常事件"
                value={abnormalCount}
                suffix="起"
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div style={{ marginLeft: '20px' }}>
                <span>
                  <Image width={24} src="/icons/yichang.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>
                较昨日 {abnormalChange} 起{' '}
                <Image width={14} src="/icons/yishang.png" preview={false} />
              </span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={4} lg={4}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="设备开机数量"
                value={operatingSum}
                suffix="台"
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/operatingSum.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>开机率 {operatingRate}%</span>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} md={12}>
          <Card
            title="孔任务完成情况统计"
            bordered={false}
            className={styles.chartCard}
            headStyle={{
              fontSize: '22px',
              fontWeight: 'normal',
              fontFamily: 'PingFang SC',
              color: '#ffffffd9',
            }}
          >
            <Pie data={stationHealthData} />
          </Card>
        </Col>
        <Col xs={30} md={12}>
          <Card
            title="能源消耗统计"
            bordered={false}
            className={styles.chartCard}
            headStyle={{
              fontSize: '22px',
              fontWeight: 'normal',
              fontFamily: 'PingFang SC',
              color: '#ffffffd9',
            }}
          >
            <Column data={energyConsumptionData} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} md={15}>
          <Card
            title="近期异常数据"
            bordered={false}
            className={styles.tableCard}
            headStyle={{
              fontSize: '22px',
              fontWeight: 'normal',
              fontFamily: 'PingFang SC',
              color: '#ffffffd9',
            }}
          >
            <Table columns={columns} dataSource={abnormalData} pagination={false} size="middle" />
          </Card>
        </Col>
        <Col xs={24} md={9}>
          <Card
            title="设备运行时长"
            bordered={false}
            className={styles.chartCard}
            headStyle={{
              fontSize: '22px',
              fontWeight: 'normal',
              fontFamily: 'PingFang SC',
              color: '#ffffffd9',
            }}
          >
            <Line data={lineData} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TaskDashboard;
