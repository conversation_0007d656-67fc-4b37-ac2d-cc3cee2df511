import { <PERSON><PERSON>yOutlined, DingdingOutlined, <PERSON>baoOutlined } from '@ant-design/icons';
import { List } from 'antd';
import React, { Fragment, useState } from 'react';
import { Button, Modal, QRCode, Flex } from 'antd';

const BindingView: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const getData = () => [
    {
      title: '绑定钉钉',
      description: '当前未绑定钉钉账号',
      actions: [<a key="Bind" onClick={showModal}>绑定</a>],
      avatar: <DingdingOutlined style={{ color: "#2595E8" }} className="dingding" />,
    },
  ];

  return (
    <Fragment>
      <List
        itemLayout="horizontal"
        dataSource={getData()}
        renderItem={(item) => (
          <List.Item actions={item.actions}>
            <List.Item.Meta
              avatar={item.avatar}
              title={item.title}
              description={item.description}
            />
          </List.Item>
        )}
      />
      <Modal title="绑定钉钉" open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
        <Flex vertical='vertical' justify='center' align='center'>
          <QRCode
            errorLevel="H"
            value="https://ant.design/"
            bordered={false}
            icon="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
            color='#000'
            bgColor='#fff'
            style={{ marginTop: '50px',padding:'5px' }}
            size={150}
          />
          <div style={{ marginTop: '16px',fontSize: '14px' }}>使用钉钉扫码绑定</div>
        </Flex >
      </Modal>
    </Fragment>
  );
};

export default BindingView;
