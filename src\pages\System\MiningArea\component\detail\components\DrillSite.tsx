import React, { useRef, useState, useEffect } from 'react';
import { Button, Flex, message, Select, Tag, Drawer, Form, Input, DatePicker, Modal } from 'antd';
import { PlusOutlined, CloseOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { postRequest, getRequest } from '@/services/api/api';
import dayjs from 'dayjs';

interface DrillSiteProps {
    noodlesId?: string;
    selectedTunnel?: {
        id: number;
        name: string;
    };
}

interface DrillSiteItem {
    id: number;
    drillsiteId: string;
    name: string;
    location: string;
    type: string;
    startTime: number;
    endTime: number;
    number: number;
    length: string;
    designDepth: string;
    actualDepth: string;
    diameter: string;
    responsiblePerson: string;
    status: number;
    remark: string;
    lanewayIds: number;
    lanewayName: string;
    createdAt: number;
    updateAt: number;
    corpId: string;
}

interface SelectOption {
    label: string;
    value: string;
}

const DrillSite: React.FC<DrillSiteProps> = ({ noodlesId, selectedTunnel }) => {
    const ref = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
    });
    const [options, setOptions] = useState<SelectOption[]>([]);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [form] = Form.useForm();
    const [isEdit, setIsEdit] = useState(false);
    const [currentRecord, setCurrentRecord] = useState<DrillSiteItem>();
    const [detailVisible, setDetailVisible] = useState(false);
    const [detailRecord, setDetailRecord] = useState<DrillSiteItem>();

    const fetchOptions = async () => {
        try {
            const result = await postRequest('laneway/get_ls', {
                page: 1,
                perPage: 999,
                parentId: noodlesId,
            });
            const { data, status, msg } = result as any;
            if (status === 0 && data?.items) {
                const newOptions = data.items.map((item: any) => ({
                    label: item.name,
                    value: String(item.id)
                }));
                setOptions(newOptions);
                if (selectedTunnel?.id) {
                    setQueryForm({ keyWord: String(selectedTunnel.id) });
                } else if (newOptions.length > 0) {
                    setQueryForm({ keyWord: newOptions[0].value });
                }
                ref.current?.reload();
            } else {
                messageApi.error(msg || '获取选项失败');
            }
        } catch (error) {
            messageApi.error('获取选项失败');
        }
    };

    useEffect(() => {
        if (noodlesId) {
            fetchOptions();
        }
    }, [noodlesId]);

    useEffect(() => {
        if (selectedTunnel?.id) {
            setQueryForm({ keyWord: String(selectedTunnel.id) });
            ref.current?.reload();
        }
    }, [selectedTunnel]);

    const handleAdd = async (values: any) => {
        try {
            const formData = {
                ...values,
                startTime: values.startTime?.format('YYYY-MM-DD'),
                endTime: values.endTime?.format('YYYY-MM-DD'),
                lanewayIds: queryForm.keyWord,
            };

            const result = await postRequest('drillsite/post_add', formData);
            const { status, msg } = result as any;
            if (status === 0) {
                messageApi.success('添加成功');
                setDrawerVisible(false);
                form.resetFields();
                ref.current?.reload();
            } else {
                messageApi.error(msg || '添加失败');
            }
        } catch (error) {
            messageApi.error('添加失败');
        }
    };

    const handleEdit = async (values: any) => {
        try {
            const formData = {
                ...values,
                startTime: values.startTime?.format('YYYY-MM-DD'),
                endTime: values.endTime?.format('YYYY-MM-DD'),
                lanewayIds: queryForm.keyWord,
                id: currentRecord?.id,
            };

            const result = await postRequest('drillsite/post_modify', formData);
            const { status, msg } = result as any;
            if (status === 0) {
                messageApi.success('修改成功');
                setDrawerVisible(false);
                setIsEdit(false);
                form.resetFields();
                ref.current?.reload();
            } else {
                messageApi.error(msg || '修改失败');
            }
        } catch (error) {
            messageApi.error('修改失败');
        }
    };

    const handleDelete = (record: DrillSiteItem) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该钻场吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const result = await getRequest('drillsite/post_del', {
                        id: record.id
                    });
                    const { status, msg } = result as any;
                    if (status === 0) {
                        messageApi.success('删除成功');
                        ref.current?.reload();
                    } else {
                        messageApi.error(msg || '删除失败');
                    }
                } catch (error) {
                    messageApi.error('删除失败');
                }
            }
        });
    };

    const showEditDrawer = (record: DrillSiteItem) => {
        setIsEdit(true);
        setCurrentRecord(record);
        setDrawerVisible(true);
        form.setFieldsValue({
            ...record,
            startTime: record.startTime ? dayjs(record.startTime) : undefined,
            endTime: record.endTime ? dayjs(record.endTime) : undefined,
        });
    };

    const showDetailDrawer = (record: DrillSiteItem) => {
        setDetailRecord(record);
        setDetailVisible(true);
    };

    const columns: ProColumns<DrillSiteItem>[] = [
        {
            title: '钻场编号',
            dataIndex: 'drillsiteId',
            width: 120,
            render: (text, record) => (
                <a onClick={() => showDetailDrawer(record)}>
                    {text}
                </a>
            ),
        },
        {
            title: '钻场名称',
            dataIndex: 'name',
            width: 120,
        },
        {
            title: '位置',
            dataIndex: 'location',
            width: 120,
        },
        {
            title: '类型',
            dataIndex: 'type',
            width: 100,
        },
        {
            title: '开始时间',
            dataIndex: 'startTime',
            width: 120,
        },
        {
            title: '结束时间',
            dataIndex: 'endTime',
            width: 120,
        },
        {
            title: '钻孔数量',
            dataIndex: 'number',
            width: 100,
        },
        {
            title: '总长度',
            dataIndex: 'length',
            width: 100,
        },
        {
            title: '设计深度',
            dataIndex: 'designDepth',
            width: 100,
        },
        {
            title: '实际深度',
            dataIndex: 'actualDepth',
            width: 100,
        },
        {
            title: '直径',
            dataIndex: 'diameter',
            width: 100,
        },
        {
            title: '责任人',
            dataIndex: 'responsiblePerson',
            width: 100,
        },
        {
            title: '使用状态',
            dataIndex: 'status',
            width: 100,
            render: (text) => {
                const options = [
                    {
                        label: '未使用',
                        color: 'default'
                    },
                    {
                        label: '使用中',
                        color: 'blue'
                    },
                ]
                const index = typeof text === 'number' && text >= 0 && text < options.length ? Number(text) : 0;
                const field = options[index];
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
        },
        {
            title: '备注',
            dataIndex: 'remark',
            ellipsis: true,
            width: 120,
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 120,
        },
        {
            title: '修改时间',
            dataIndex: 'updateAt',
            width: 120,
        },
        {
            title: '操作',
            width: 120,
            valueType: 'option',
            fixed: 'right',
            render: (_, record) => [
                <Button
                    key="edit"
                    type="link"
                    onClick={() => showEditDrawer(record)}
                >
                    编辑
                </Button>,
                <Button
                    key="delete"
                    type="link"
                    onClick={() => handleDelete(record)}
                >
                    删除
                </Button>,
            ],
        },
    ];

    return (
        <>
            {contextHolder}
            <ProTable<DrillSiteItem>
                columns={columns}
                actionRef={ref}
                scroll={{ x: 'max-content' }}
                sticky
                search={false}
                dateFormatter="string"
                toolbar={{
                    search: <Flex>
                        <Select
                            style={{ width: '250px' }}
                            allowClear
                            value={queryForm.keyWord || undefined}
                            onChange={(value) => {
                                setQueryForm({ keyWord: value || '' });
                                ref.current?.reload();
                            }}
                            options={options}
                            placeholder="请选择巷道" 
                        />
                    </Flex>
                }}
                toolBarRender={() => [
                    <Button
                        key="add"
                        type="primary"
                        onClick={() => {
                            setDrawerVisible(true);
                        }}
                    >
                        <PlusOutlined /> 新增
                    </Button>,
                ]}
                request={async (params) => {
                    try {
                        const result = await postRequest('drillsite/get_ls', {
                            page: params.current,
                            perPage: params.pageSize,
                            parentId: queryForm.keyWord,
                        });
                        const { data, status, msg } = result as any;
                        if (status === 0) {
                            return {
                                data: data?.items || [],
                                success: true,
                                total: data?.total || 0,
                            };
                        } else {
                            messageApi.error(msg || '获取数据失败');
                            return {
                                data: [],
                                success: false,
                                total: 0,
                            };
                        }
                    } catch (error) {
                        messageApi.error('获取数据失败');
                        return {
                            data: [],
                            success: false,
                            total: 0,
                        };
                    }
                }}
            />
            <Drawer
                title={isEdit ? "修改钻场" : "添加钻场"}
                width={600}
                open={drawerVisible}
                onClose={() => {
                    setDrawerVisible(false);
                    setIsEdit(false);
                    form.resetFields();
                }}
                closable={false}
                extra={
                    <Button type="text" onClick={() => {
                        setDrawerVisible(false);
                        setIsEdit(false);
                        form.resetFields();
                    }}>
                        <CloseOutlined style={{ fontSize: 16 }} />
                    </Button>
                }
                footer={
                    <div style={{ textAlign: 'right' }}>
                        <Button onClick={() => {
                            setDrawerVisible(false);
                            setIsEdit(false);
                            form.resetFields();
                        }} style={{ marginRight: 8 }}>
                            取消
                        </Button>
                        <Button type="primary" onClick={() => form.submit()}>
                            提交
                        </Button>
                    </div>
                }
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={isEdit ? handleEdit : handleAdd}
                >
                    <Form.Item
                        name="drillsiteId"
                        label="钻场编号"
                    >
                        <Input placeholder="请输入钻场编号" />
                    </Form.Item>
                    <Form.Item
                        name="name"
                        label="钻场名称"
                    >
                        <Input placeholder="请输入钻场名称" />
                    </Form.Item>
                    <Form.Item
                        name="location"
                        label="位置"
                    >
                        <Input placeholder="请输入位置" />
                    </Form.Item>
                    <Form.Item
                        name="type"
                        label="类型"
                    >
                        <Input placeholder="请输入类型" />
                    </Form.Item>
                    <Form.Item
                        name="startTime"
                        label="开始时间"
                    >
                        <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item
                        name="endTime"
                        label="结束时间"
                    >
                        <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                    <Form.Item
                        name="number"
                        label="钻孔数量"
                    >
                        <Input placeholder="请输入钻孔数量" />
                    </Form.Item>
                    <Form.Item
                        name="length"
                        label="总长度"
                    >
                        <Input placeholder="请输入总长度" />
                    </Form.Item>
                    <Form.Item
                        name="designDepth"
                        label="设计深度"
                    >
                        <Input placeholder="请输入设计深度" />
                    </Form.Item>
                    <Form.Item
                        name="actualDepth"
                        label="实际深度"
                    >
                        <Input placeholder="请输入实际深度" />
                    </Form.Item>
                    <Form.Item
                        name="diameter"
                        label="直径"
                    >
                        <Input placeholder="请输入直径" />
                    </Form.Item>
                    <Form.Item
                        name="responsiblePerson"
                        label="责任人"
                    >
                        <Input placeholder="请输入责任人" />
                    </Form.Item>
                    <Form.Item
                        name="status"
                        label="使用状态"
                        initialValue={0}
                    >
                        <Select
                            options={[
                                { label: '未使用', value: 0 },
                                { label: '使用中', value: 1 },
                            ]}
                            placeholder="请选择使用状态"
                        />
                    </Form.Item>
                    <Form.Item
                        name="remark"
                        label="备注"
                    >
                        <Input.TextArea rows={4} placeholder="请输入备注" />
                    </Form.Item>
                </Form>
            </Drawer>
            <Drawer
                title={`钻场详情 - ${detailRecord?.name || ''}`}
                width={600}
                open={detailVisible}
                onClose={() => setDetailVisible(false)}
                closable={false}
                extra={
                    <Button type="text" onClick={() => setDetailVisible(false)}>
                        <CloseOutlined style={{ fontSize: 16 }} />
                    </Button>
                }
            >
                <div>
                    <p>钻场编号：{detailRecord?.drillsiteId}</p>
                    <p>钻场名称：{detailRecord?.name}</p>
                    <p>位置：{detailRecord?.location}</p>
                    <p>类型：{detailRecord?.type}</p>
                    <p>开始时间：{detailRecord?.startTime}</p>
                    <p>结束时间：{detailRecord?.endTime}</p>
                    <p>钻孔数量：{detailRecord?.number}</p>
                    <p>总长度：{detailRecord?.length}</p>
                    <p>设计深度：{detailRecord?.designDepth}</p>
                    <p>实际深度：{detailRecord?.actualDepth}</p>
                    <p>直径：{detailRecord?.diameter}</p>
                    <p>责任人：{detailRecord?.responsiblePerson}</p>
                    <p>使用状态：
                        <Tag color={detailRecord?.status === 1 ? 'blue' : 'default'}>
                            {detailRecord?.status === 1 ? '使用中' : '未使用'}
                        </Tag>
                    </p>
                    <p>创建时间：{detailRecord?.createdAt}</p>
                    <p>修改时间：{detailRecord?.updateAt}</p>
                    <p>备注：{detailRecord?.remark}</p>
                </div>
            </Drawer>
        </>
    );
};

export default DrillSite; 