import React from 'react';
import { Button, Modal, message,Input,Select,DatePicker,Tag,Breadcrumb } from 'antd';
import { history } from 'umi';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest,getRequest } from '@/services/api/api';
import { setTempData } from '@/utils/storage';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { RangePicker } = DatePicker;


// 定义表格数据类型
interface TableListItem {
  id: number;
  constructionNumber: string;
  constructionName: string;
  constructionUnit: string;
  constructionPerson: string;
  constructionCrews: string;
  results:number;
  startDate:string;
  endDate:string;
  actualStarttime:string;
  actualEndtime:string;
  constructionProgress:string;
  acceptancePersonnel:string;
  acceptanceDate:string;
  remark:string;
}
interface ApiResponse<T = any> {
    status: number;
    msg?: string;
    data?: T;
    total?: number;
  }
  const tableListDataSource: TableListItem[] = [];
  
  // 生成20个不同的静态数据
  const constructionNames = ['井筒施工', '巷道掘进', '通风系统安装', '排水系统施工', '供电系统安装', '运输系统建设', '支护工程', '钻探工程', '防尘设施安装', '消防系统建设'];
  const constructionUnits = ['一一矿业施工队', '二二工程队', '三三建设集团', '四四矿建队', '五五工程公司'];
  const constructionPersons = ['张明', '李强', '王伟', '赵刚', '周勇', '吴峰', '尉迟'];
  const acceptancePersonnels = ['徐星辰', '陈光', '刘洋', '孙明', '钱伟'];

  for (let i = 0; i < 20; i += 1) {
    const randomProgress = Math.floor(Math.random() * 100);
    const randomResult = Math.floor(Math.random() * 2);
    
    tableListDataSource.push({
      id: i,
      constructionNumber: `CONSTRUCTION${String(i + 1).padStart(3, '0')}`,
      constructionName: constructionNames[i % constructionNames.length],
      constructionUnit: constructionUnits[i % constructionUnits.length],
      constructionPerson: constructionPersons[i % constructionPersons.length],
      constructionCrews: `${constructionPersons[(i + 1) % constructionPersons.length]}、${constructionPersons[(i + 2) % constructionPersons.length]}`,
      results: randomResult,
      startDate: `2024-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')}`,
      endDate: `2024-${String((i + 2) % 12 + 1).padStart(2, '0')}-${String((i + 5) % 28 + 1).padStart(2, '0')}`,
      actualStarttime: `2024-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')} 08:00:00`,
      actualEndtime: `2024-${String((i + 2) % 12 + 1).padStart(2, '0')}-${String((i + 5) % 28 + 1).padStart(2, '0')} 18:00:00`,
      constructionProgress: `${randomProgress}`,
      acceptancePersonnel: acceptancePersonnels[i % acceptancePersonnels.length],
      acceptanceDate: `2024-${String((i + 3) % 12 + 1).padStart(2, '0')}-${String((i + 6) % 28 + 1).padStart(2, '0')}`,
      remark: i % 3 === 0 ? '请注意施工安全' : i % 3 === 1 ? '按期完成施工任务' : '确保工程质量',
    });
  }
  
const Construction: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();
  
  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
   
    {
      title: '钻机名称',
      dataIndex: 'drillName',
      fixed: 'left',
      ellipsis: true,
      search: false,
    },
    {
      title: '计划任务名称',
      dataIndex: 'parentName',
      ellipsis: true,
      search: false,
    },
    {
      title: '孔号',
      dataIndex: 'holeNumber',
      ellipsis: true,
      search: false,
    },
    {
      title: '施工位置',
      dataIndex: 'constructionLocation',
      ellipsis: true,
      width: 120,
      search: false,
    },
    {
      title: '施工地点',
      dataIndex: 'position',
      ellipsis: true,
      width: 120,
      search: false,
    },
    {
      title: '孔深（m）',
      dataIndex: 'holeDepth',
      ellipsis: true,
      search: false,
    },
    {
      title: '孔径（mm）',
      dataIndex: 'holeDiameter',
      ellipsis: true,
      search: false,
    },
    {
      title: '开孔角度（°）',
      dataIndex: 'holeAngle',
      ellipsis: true,
      search: false,
    },
    {
        title: '方位（°）',
        dataIndex: 'direction',
        ellipsis: true,
        search: false,
    },
    {
        title: '开孔高度（m）',
        dataIndex: 'holeHeight',
        ellipsis: true,
        search: false,
    },
    {
        title: '见煤距离（m）',
        dataIndex: 'coalDistance',
        ellipsis: true,
        search: false,
    },
    {
        title: '见岩石距离（m）',
        dataIndex: 'rockDistance',
        ellipsis: true,
        search: false,
    },
    // {
    //     title: '扩孔起始距离',
    //     dataIndex: 'reamingStartDistance',
    //     ellipsis: true,
    //     search: false,
    // },
    // {
    //     title: '打钻起始距离',
    //     dataIndex: 'drillingStartDistance',
    //     ellipsis: true,
    //     search: false,
    // },
    // {
    //     title: '预计出煤量',
    //     dataIndex: 'estimatedCoalOutput',
    //     ellipsis: true,
    //     search: false,
    // },
    {
        title: '开孔角度误差（°）',
        dataIndex: 'holeAngleError',
        ellipsis: true,
        search: false,
    },
    {
        title: '开孔方位误差（°）',
        dataIndex: 'holeDirectionError',
        ellipsis: true,
        search: false,
    },
    // {
    //     title: '开孔高度误差',
    //     dataIndex: 'holeHeightError',
    //     ellipsis: true,
    //     search: false,
    // },
    {
        title: '孔深误差（m）',
        dataIndex: 'holeDepthError',
        ellipsis: true,
        search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            history.push(`/project/Construction/modify/?id=${record.id}`);
          }}
        >
          详情
        </a>,
      ],
    },
    {
      title: '',
      dataIndex: 'keyWordTwo',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        const label = item.dataIndex;
        const status = form.getFieldValue(label);
        const onchange = (value) => {
          form.setFieldsValue({ [label]: value });
        };
        return (
          <>
            <Input
              value={status}
              onChange={(e) => onchange(e.target.value)}
              placeholder="请输入计划名称"
            />
          </>
        );
      },
    },
    {
      title: '',
      dataIndex: 'addTimeRange',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
        const label = item.dataIndex;
        const status = form.getFieldValue(label);
        const onchange = (value) => {
          form.setFieldsValue({ [label]: value });
        };
        return (
          <>
            <RangePicker
              value={status}
              style={{ width: '300px' }}
              onChange={(value) => onchange(value)}
              picker="date"
              format="YYYY-MM-DD"
              placeholder={['开始时间', '结束时间']}
            />
          </>
        );
      },
    },
  ];

  return (
    <>
    <Breadcrumb style={{ marginBottom: '20px' }}
        items={[
          { title: '首页' },
          { title: '施工管理', },
          { title: '施工记录' },
        ]}
      />
    {contextHolder}

    <ProTable<TableListItem>
      headerTitle="施工记录"
      actionRef={actionRef}
      rowKey="id"
      request={async (params, sorter, filter) => {
        const { current, pageSize, keyWordTwo, addTimeRange, ...rest } = params;
                    let postData = {
                        page: current,
                        perPage: pageSize,
                        status:6,
                        ...(keyWordTwo ? { keyWordTwo: keyWordTwo } : {}),
                        ...(addTimeRange ? { dateStart: addTimeRange?.[0], dateEnd: addTimeRange?.[1] } : {}),
                    }
        const result = await postRequest('drill/get_ls', postData);
        const { data, status, msg } = result
        let dataSource
        let total
        if (status === 0) {
            console.log('施工列表',data);
            
            dataSource = data.items
            total = data.total
        } else {
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
        return Promise.resolve({
            data: dataSource,
            total: total,
            success: true,
        });
    }}
      columns={columns}
      search={{
        defaultCollapsed: false,
        labelWidth: 0,
        span: 4,
      }}
      scroll={{ x: 2400 }}
    //   options={false}
     
    />
   
    </>
  );
  
};

export default Construction;
