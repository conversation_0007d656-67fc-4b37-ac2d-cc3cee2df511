import React, { useEffect, useRef, useState } from 'react';
import { postRequest } from '@/services/api/api';
import EzVideoPlayer from '@/pages/Project/Construction/component/EzVideoPlayer';
import VideoErrorBoundary from '@/pages/Project/Construction/component/VideoErrorBoundary';

// 定义萤石云直播API返回数据类型
interface LiveAddressResponse {
  status: number;
  msg: string;
  data: {
    id: string;
    url: string; // 直播流地址 (m3u8格式)
    expireTime: string;
  };
}

// 定义设备参数类型
interface DeviceParams {
  deviceCode: string; // 设备code
}

const Monitor: React.FC<{ data?: DeviceParams | null }> = ({ data }) => {
  // 萤石云直播URL状态
  const [ezOpenUrl, setEzOpenUrl] = useState<string>('');
  // 加载状态
  const [loading, setLoading] = useState(true);
  // 错误状态
  const [error, setError] = useState<string | null>(null);
  // 直播数据
  const [liveData, setLiveData] = useState<LiveAddressResponse['data'] | null>(null);

  // 获取萤石云直播地址
  useEffect(() => {
    const fetchLiveAddress = async () => {
      try {
        setLoading(true);
        setError(null);
        setEzOpenUrl('');
        setLiveData(null);

        // 检查必需参数
        if (!data) {
          throw new Error('请先选择设备');
        }

        if (!data.deviceCode) {
          throw new Error('缺少设备code参数');
        }

        console.log('调用萤石云直播接口，设备code:', data.deviceCode);

        // 调用萤石云直播API (POST请求，deviceCode作为query参数)
        const response = await postRequest(`/api/ys/get_live_address?deviceCode=${data.deviceCode}`, {}) as LiveAddressResponse;
        console.log('萤石云直播API响应:', response);

        if (response && response.status === 0 && response.data?.url) {
          const liveUrl = response.data.url;
          console.log('获取到萤石云直播地址:', liveUrl);

          // 将m3u8 URL转换为ezopen协议URL
          // 萤石云的m3u8 URL需要转换为ezopen协议才能在EzVideoPlayer中使用
          const ezOpenLiveUrl = liveUrl.replace('https://open.ys7.com/v3/openlive/', 'ezopen://open.ys7.com/');
          console.log('转换后的ezopen直播URL:', ezOpenLiveUrl);

          setEzOpenUrl(ezOpenLiveUrl);
          setLiveData(response.data);
        } else {
          const errorMsg = response?.msg || '获取直播地址失败';
          throw new Error(errorMsg);
        }
      } catch (err) {
        console.error('获取萤石云直播地址失败:', err);
        setError(err instanceof Error ? err.message : '获取直播地址失败');
      } finally {
        setLoading(false);
      }
    };

    // 如果有data数据且包含设备code，则调用API
    if (data?.deviceCode) {
      fetchLiveAddress();
    } else if (data === null) {
      // 如果data为null，设置初始状态
      setLoading(false);
      setError(null);
      setEzOpenUrl('');
      setLiveData(null);
    }
  }, [data]);

  // 处理萤石云播放器的抓拍事件
  const handleEzCapture = (type: 'image' | 'video', data: any) => {
    console.log('萤石云播放器抓拍事件:', { type, data });
    // 这里可以处理抓拍的图片或视频
  };

  // 渲染萤石云直播播放器
  return (
    <div style={{ width: '100%', height: '100%', padding: '20px' }}>
      <div style={{ width: '100%', margin: '0 auto' }}>
        {!data && (
          <div style={{
            width: '100%',
            height: '580px',
            backgroundColor: '#f9f9f9',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            color: '#999'
          }}>
            请先选择设备查看实时监控
          </div>
        )}

        {data && loading && (
          <div style={{
            width: '100%',
            height: '580px',
            backgroundColor: '#f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            color: '#666'
          }}>
            正在获取直播地址...
          </div>
        )}

        {data && error && !loading && (
          <div style={{
            width: '100%',
            height: '580px',
            backgroundColor: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            fontSize: '16px',
            color: '#ff4d4f'
          }}>
            <div>直播加载失败</div>
            <div style={{ fontSize: '14px', marginTop: '8px', color: '#999' }}>
              {error}
            </div>
          </div>
        )}

        {data && !loading && !error && ezOpenUrl && (
          <VideoErrorBoundary>
            <EzVideoPlayer
              key={ezOpenUrl} // 强制重新创建组件
              ezOpenUrl={ezOpenUrl}
              width="100%"
              height="580px"
              style={{
                borderRadius: '8px',
                overflow: 'hidden'
              }}
              onCapture={handleEzCapture}
              isLive={true} // 直播模式，隐藏日期选择器
            />
          </VideoErrorBoundary>
        )}

        {/* 显示直播信息 */}
        {/* {liveData && !loading && !error && (
          <div style={{
            marginTop: '12px',
            padding: '12px',
            backgroundColor: '#f6f8fa',
            borderRadius: '6px',
            fontSize: '14px',
            color: '#666'
          }}>
            <div><strong>直播ID:</strong> {liveData.id}</div>
            <div><strong>过期时间:</strong> {liveData.expireTime}</div>
            <div><strong>直播状态:</strong> <span style={{ color: '#52c41a' }}>● 在线</span></div>
          </div>
        )} */}
      </div>
    </div>
  );
};

export default Monitor;
