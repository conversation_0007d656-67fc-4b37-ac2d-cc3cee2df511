import React from 'react';
import { message,DatePicker} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest} from '@/services/api/api';
import type { DatePickerProps } from 'antd';
import dayjs from 'dayjs';
// 定义表格数据类型
interface TableListItem {
  id: number;
  constructionNumber: string;
  constructionName: string;
  constructionUnit: string;
  constructionPerson: string;
  constructionCrews: string;
  results:number;
  startDate:string;
  endDate:string;
  actualStarttime:string;
  actualEndtime:string;
  constructionProgress:string;
  acceptancePersonnel:string;
  acceptanceDate:string;
  remark:string;
}
  
const Construction: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();
  const weekFormat = 'YYYY-MM-DD';
  const getDateRange = (date: dayjs.Dayjs) => {
    const startDate = date.startOf('week').format(weekFormat);
    const endDate = date.endOf('week').format(weekFormat);
    return `${startDate}~${endDate}`;
}
  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
   
    {
      title: '人员',
      dataIndex: 'name',
      fixed: 'left',
      ellipsis: true,
      search: false,
    },
    {
      title: '有效工作',
      dataIndex: 'effectiveHour',
      ellipsis: true,
      search: false,
    },
    {
      title: '用电量',
      dataIndex: 'useElectricity',
      ellipsis: true,
      search: false,
    },
    {
        title: '用水量',
        dataIndex: 'useWater',
        ellipsis: true,
        search: false,
    },
    {
        title: '封孔深度',
        dataIndex: 'sealingDepth',
        ellipsis: true,
        search: false,
    },
    {
        title: '水泥量',
        dataIndex: 'cementContent',
        ellipsis: true,
        search: false,
    },
    {
        title: '累计钻孔米',
        dataIndex: 'footageNum',
        ellipsis: true,
        search: false,
    },
    {
        title: '退钻钻杆根数',
        dataIndex: 'drillPipeNum',
        ellipsis: true,
        search: false,
    },
    {
        title: '终孔数量',
        dataIndex: 'finishNum',
        ellipsis: true,
        search: false,
    },
    {
        title: '',
        dataIndex: 'keyWord',
        hideInTable: true,
        renderFormItem: (schema, config, form) => {
            const label = schema.dataIndex as string;
            const status = form.getFieldValue(label);
            const weekFormat = 'YYYY-MM-DD';
            
            const getDateRange = (date: dayjs.Dayjs) => {
                const startDate = date.startOf('week').format(weekFormat);
                const endDate = date.endOf('week').format(weekFormat);
                return `${startDate}~${endDate}`;
            };

            const customWeekStartEndFormat: DatePickerProps['format'] = (value) =>
                `${dayjs(value).startOf('week').format(weekFormat)} ~ ${dayjs(value)
                    .endOf('week')
                    .format(weekFormat)}`;

            if (!status) {
                const currentDate = dayjs();
                form.setFieldsValue({ [label]: getDateRange(currentDate) });
            }

            return (
                <DatePicker 
                    value={status ? dayjs(status.split('~')[0]) : dayjs()} 
                    picker="week"
                    format={customWeekStartEndFormat}
                    onChange={(date) => {
                        if (date) {
                            form.setFieldsValue({ [label]: getDateRange(date)})
                        }
                    }}
                />
            );
        },
    },
  ];

  return (
    <>
    {contextHolder}
    <ProTable<TableListItem>
      headerTitle="周报表"
      actionRef={actionRef}
      rowKey="id"
      request={async (params) => {
        const { current, pageSize, keyWord } = params;
        const postData: any = {
            page: current,
            perPage: pageSize,
        };
        
        if (keyWord) {
            postData.dateStart = keyWord.split('~')[0];
            postData.dateEnd = keyWord.split('~')[1];
        }else{
            const currentDate = dayjs();
            postData.dateStart = getDateRange(currentDate).split('~')[0];
            postData.dateEnd = getDateRange(currentDate).split('~')[1];
        }
        
        try {
            const result = await postRequest('report/get_week_report', postData);
            const { data, status, msg } = result;
            
            if (status === 0) {
                return {
                    data: data.items,
                    total: data.total,
                    success: true,
                };
            } else {
                messageApi.error(msg);
                return {
                    data: [],
                    total: 0,
                    success: false,
                };
            }
        } catch (error) {
            messageApi.error('请求失败');
            return {
                data: [],
                total: 0,
                success: false,
            };
        }
    }}
      columns={columns}
    //   search={false}
    //   scroll={{ x: 2400 }}
    //   options={false}
     
    />
   
    </>
  );
  
};

export default Construction;

