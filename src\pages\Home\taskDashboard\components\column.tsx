/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-31 15:19:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import React, { useEffect, useState } from 'react';
import { Chart } from '@antv/g2';
interface ColumnProps {
  data: [];
}
const Column: React.FC<ColumnProps> = ({ data }) => {
  useEffect(() => {
      if(data){
        try {
            const energyConsumptionData = Object.entries(data).map(([day, values]) => ({
              day,
              ...values
            })).sort((a, b) => {
              const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
              return days.indexOf(a.day) - days.indexOf(b.day);
            });
            // 将数据转换为分组柱状图所需格式
            const transformedData = energyConsumptionData.reduce((acc: any[], cur) => {
              acc.push({ day: cur.day, type: '用电量', value: cur.用电量 });
              acc.push({ day: cur.day, type: '用水量', value: cur.用水量 });
              return acc;
            }, []);
  
            const chart = new Chart({
              container: 'column',
              autoFit: true,  // 添加自动适应
              height: 300,
              theme: 'dark',
            });
  
            chart
              .legend('color', { position: 'top', layout: { justifyContent: 'center' },itemMarkerSize:20 })
              .interval()
              .data(transformedData)
              .encode('x', 'day')
              .encode('y', 'value')
              .encode('color', 'type')
              .scale('color', {
                range: ['#3b82f6', '#10b981'],
              })
              .transform({ type: 'dodgeX' })
              .interaction('elementHighlight', { background: true })
              .axis('x', { title: false }) // 隐藏横坐标轴标题
              .axis('y', { title: false }) // 
              .tooltip({
                title: 'day',
                items: [
                  (data: any) => ({
                    name: '用电量',
                    value: energyConsumptionData.find(d => d.day === data.day)?.用电量,
                    color: '#3b82f6',
                  }),
                  (data: any) => ({
                    name: '用水量',
                    value: energyConsumptionData.find(d => d.day === data.day)?.用水量,
                    color: '#10b981',
                  })
                ]
              });
            chart.render();  // 添加渲染调用
  
            // 清理函数
            return () => {
              chart.destroy();
            };
        } catch (error) {
        }
      }



  }, [data]);

  return (
    <div id='column' style={{ width: '100%', height: '300px' }}></div>
  );
};

export default Column;
