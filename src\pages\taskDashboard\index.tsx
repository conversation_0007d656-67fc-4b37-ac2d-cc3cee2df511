/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-05 13:22:26
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import { Column, Line, Pie } from '@ant-design/plots';
import { Breadcrumb, Card, Col, Image, Row, Statistic, Table, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

// 异常数据类型定义
export type AbnormalItem = {
  key: string;
  time: string;
  stationId: string;
  abnormalType: string;
  status: '处理中' | '已解决' | '待处理' | '特处理';
};

const TaskDashboard: React.FC = () => {
  // 状态定义
  const [totalDistance, setTotalDistance] = useState(12456.6);
  const [distanceChange, setDistanceChange] = useState(156);
  const [stationCount, setStationCount] = useState(24);
  const [operationRate, setOperationRate] = useState(92);
  const [taskCount, setTaskCount] = useState(235);
  const [taskCompletionRate, setTaskCompletionRate] = useState(85.6);
  const [abnormalCount, setAbnormalCount] = useState(12);
  const [abnormalChange, setAbnormalChange] = useState(2);
  const [abnormalData, setAbnormalData] = useState<AbnormalItem[]>([]);

  // 模拟获取数据
  useEffect(() => {
    // 这里可以替换为实际的API调用
    setAbnormalData([
      {
        key: '1',
        time: '2024-01-20 14:30',
        stationId: 'DZ-2024',
        abnormalType: '泵压异常',
        status: '处理中',
      },
      {
        key: '2',
        time: '2024-01-20 13:15',
        stationId: 'DZ-1856',
        abnormalType: '转速超限',
        status: '已解决',
      },

      {
        key: '3',
        time: '2024-01-20 12:45',
        stationId: 'DZ-1932',
        abnormalType: '温度过高',
        status: '已解决',
      },
      {
        key: '4',
        time: '2024-01-20 11:20',
        stationId: 'DZ-2103',
        abnormalType: '液压系统故障',
        status: '特处理',
      },
    ]);
  }, []);

  // 站点健康状况数据
  const stationHealthData = [
    { type: '状态良好', value: 65 },
    { type: '需要维护', value: 20 },
    { type: '故障', value: 15 },
  ];

  // 能源消耗数据
  const energyConsumptionData = [
    { day: '周一', 用电量: 320, 用水量: 120 },
    { day: '周二', 用电量: 332, 用水量: 132 },
    { day: '周三', 用电量: 301, 用水量: 101 },
    { day: '周四', 用电量: 334, 用水量: 134 },
    { day: '周五', 用电量: 390, 用水量: 90 },
    { day: '周六', 用电量: 330, 用水量: 230 },
    { day: '周日', 用电量: 320, 用水量: 210 },
  ];

  // 将数据转换为分组柱状图所需格式
  const transformedData = energyConsumptionData.reduce((acc: any[], cur) => {
    acc.push({ day: cur.day, type: '用电量', value: cur.用电量 });
    acc.push({ day: cur.day, type: '用水量', value: cur.用水量 });
    return acc;
  }, []);

  const configColumn = {
    data: transformedData, // 使用转换后的数据
    width: 662,
    height: 300,
    xField: 'day',       // 横坐标字段
    yField: 'value',     // 纵坐标字段
    colorField: 'type',   // 分组字段
    group: true,          // 启用分组
    theme: 'dark',
    legend: {
      position: 'top',
      itemSpacing: 100,
      layout: 'horizontal',
      align: 'center',
      itemWidth: 'auto',
    },
    columnStyle: {
      radius: [20, 20, 20, 20],
      // columnWidthRatio: 0.4,  // 柱子宽度比例，值越小柱子越细
      // marginRatio: 20,  
    },
    // 增加分组间距
    dodgePadding: 20,         // 控制不同分组（用电量和用水量）之间的间距
    columnWidthRatio: 0.3,
  };

  // 设备运行时长数据
  const operationTimeData = [
    { time: '00:00', value: 18 },
    { time: '04:00', value: 16 },
    { time: '08:00', value: 19 },
    { time: '12:00', value: 17 },
    { time: '16:00', value: 18 },
    { time: '20:00', value: 17 },
  ];
  // 添加设备运行时长折线图配置
  const lineConfig = {
    data: operationTimeData,
    xField: 'time',
    yField: 'value',
    width: 500,
    height: 300,
    // smooth: true,
    color: '#1890ff',
    area: {
      tooltip: false,
      style: {
        fill: 'l(270) 0:#1890ff00 0.5:#1890ff33 1:#1890ff66',
        // fillOpacity: 0.7,
      },
    },
    // point: {
    //   size: 5,
    //   shape: 'circle',
    //   style: {
    //     fill: '#ffffff',
    //     stroke: '#ffffff',
    //     lineWidth: 2,
    //   },
    // },
    // xAxis: {
    //   grid: null,
    //   line: {
    //     style: {
    //       stroke: 'rgba(255, 255, 255, 0.15)',
    //     },
    //   },
    //   label: {
    //     autoRotate: false,
    //     rotate: 0,
    //     offset: 10,
    //     autoHide: false,
    //     autoEllipsis: false
    //   },
    //   tickLine: {
    //     alignTick: true,
    //     length: 5,
    //   }
    // },
    // yAxis: {
    //   min: 0,
    //   max: 20,
    //   grid: {
    //     line: {
    //       style: {
    //         stroke: 'rgba(255, 255, 255, 0.15)',
    //         lineDash: [4, 4],
    //       },
    //     },
    //   },
    // },
    // appendPadding: [10, 20, 10, 10],
    theme: 'dark',
  };
  const config = {
    data: stationHealthData,
    angleField: 'value',
    colorField: 'type',
    legend: {
      position: 'top',
      layout: 'horizontal',
      align: 'center',
    },
    width: 562,
    height: 300,
    innerRadius: 0.6,
    style: {
      // stroke: '#fff',
      inset: 1,
      radius: 10,
    },
    scale: {
      color: {
        domain: ['状态良好', '需要维护', '故障'],
        range: ['#10b981', '#fbbf24', '#ef4444'],
      },
    },
    theme: 'dark',
  };
  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
    },
    {
      title: '站点编号',
      dataIndex: 'stationId',
      key: 'stationId',
    },
    {
      title: '异常类型',
      dataIndex: 'abnormalType',
      key: 'abnormalType',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'green';
        if (status === '处理中') {
          color = 'orange';
        } else if (status === '待处理') {
          color = 'red';
        } else if (status === '特处理') {
          color = 'red';
        }
        return <Tag color={color}>{status}</Tag>;
      },
    },
  ];

  return (
    <div className={styles.dashboardContainer}>
      <Breadcrumb
        items={[{ title: '首页' }, { title: '任务看板' }]}
        className={styles.breadcrumb}
      />

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="总进尺"
                value={totalDistance}
                precision={1}
                suffix="米"
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/chizi.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>
                较昨日 {distanceChange} 米 <Image width={14} src="/icons/zongshang.png" preview={false} />
              </span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="运行站机数"
                value={stationCount}
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/zuanji.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>运行率 {operationRate}%</span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="总任务数"
                value={taskCount}
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/renwu.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>任务完成率: {taskCompletionRate}%</span>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className={styles.dashboardCard}>
            <div className={styles.cardHeader}>
              <Statistic
                title="异常事件"
                value={abnormalCount}
                suffix="起"
                valueStyle={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: 30 }}
              />
              <div>
                <span>
                  <Image width={24} src="/icons/yichang.png" preview={false} />
                </span>
              </div>
            </div>
            <div className={styles.changeInfo}>
              <span>
                较昨日 {abnormalChange} 起 <Image width={14} src="/icons/yishang.png" preview={false} />
              </span>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} md={12}>
          <Card title="站机部件健康状况" bordered={false} className={styles.chartCard}>
            <Pie {...config} />
          </Card>
        </Col>
        <Col xs={30} md={12}>
          <Card title="能源消耗统计" bordered={false} className={styles.chartCard}>
            <Column {...configColumn} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24} md={15}>
          <Card title="近期异常数据" bordered={false} className={styles.tableCard}>
            <Table columns={columns} dataSource={abnormalData} pagination={false} size="middle" />
          </Card>
        </Col>
        <Col xs={24} md={9}>
          <Card title="设备运行时长" bordered={false} className={styles.chartCard}>
            <Line {...lineConfig} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TaskDashboard;
