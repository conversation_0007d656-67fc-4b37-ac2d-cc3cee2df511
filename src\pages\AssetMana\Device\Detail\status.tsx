import React, { useEffect, useState, useRef } from 'react';
import { Input, Pagination, Card, Row, Col, Flex, message, Tag, Button, InputRef } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { postRequest } from '@/services/api/api';

interface StatusProps {
  number: string;
}

interface DeviceData {
  id: string;
  title: string;
  type: number;
  val: string | number | boolean;
  unit?: string;
  updatedAt: string;
}

interface ApiResponse<T> {
  status: number;
  msg?: string;
  data: {
    items: T[];
    total: number;
  };
}

const DeviceStatusPage: React.FC<StatusProps> = ({ number }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const [deviceList, setDeviceList] = useState<DeviceData[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [keyword, setKeyword] = useState('');
  const [polling, setPolling] = useState(true);
  const [loading, setLoading] = useState(false);
  const pollingTimer = useRef<ReturnType<typeof setInterval> | null>(null);
  const searchTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const POLLING_INTERVAL = 5000;
  const SEARCH_DELAY = 500; // 搜索延迟时间，单位毫秒
  const typeNames = ['数值', '文本', '时间', '日期', '频谱']
  const inputRef = useRef<InputRef>(null);

  const fetchDeviceData = async (isPolling = false, isSearch = false) => {
    try {
      if (isSearch || !isPolling) {
        setLoading(true);
      }

      const params: any = {
        page: currentPage,
        pageSize: pageSize,
        number: number,
      };

      if (keyword.trim()) {
        params.keyWord = keyword.trim();
      }

      const response = await postRequest<ApiResponse<DeviceData>>('device_data/get_ls', params);
      const { data, status, msg } = response;
      if (status === 0) {
        setDeviceList(data.items);
        setTotal(data.total);
      } else {
        if (!isPolling) {
          messageApi.open({
            type: 'error',
            content: msg,
          });
        }
      }
    } catch (error) {
      if (!isPolling) {
        message.error('获取设备数据失败');
      }
    } finally {
      if (isSearch || !isPolling) {
        setLoading(false);
      }
    }
  };

  const startPolling = () => {
    if (!polling) return;

    if (pollingTimer.current) {
      clearInterval(pollingTimer.current);
    }

    pollingTimer.current = setInterval(() => {
      fetchDeviceData(true);
    }, POLLING_INTERVAL);
  };

  const stopPolling = () => {
    if (pollingTimer.current) {
      clearInterval(pollingTimer.current);
      pollingTimer.current = null;
    }
  };

  const togglePolling = () => {
    setPolling(prev => !prev);
  };

  const handleSearch = () => {
    stopPolling();
    setCurrentPage(1);
    fetchDeviceData(false, true);
    startPolling();
  };

  const handleReset = () => {
    setKeyword('');
    setCurrentPage(1);
     // 使用一个新的变量确保没有关键字参数
  const params: any = {
    page: 1,  // 直接使用1而不是currentPage，因为我们刚设置了它
    pageSize: pageSize,
    number: number,
  };
  
  // 手动调用API，确保不会传递keyWord参数
  setLoading(true);
  
  postRequest<ApiResponse<DeviceData>>('device_data/get_ls', params)
    .then(response => {
      const { data, status, msg } = response;
      if (status === 0) {
        setDeviceList(data.items);
        setTotal(data.total);
      } else {
        messageApi.open({
          type: 'error',
          content: msg,
        });
      }
    })
    .catch(error => {
      message.error('获取设备数据失败');
    })
    .finally(() => {
      setLoading(false);
      startPolling();  // 请求完成后重新开始轮询
    });
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);
  };

  useEffect(() => {
    return () => {
      if (searchTimer.current) {
        clearTimeout(searchTimer.current);
      }
      stopPolling();
    };
  }, []);

  useEffect(() => {
    if (searchTimer.current) {
      clearTimeout(searchTimer.current);
    }
    fetchDeviceData(false);
    startPolling();

    return () => {
      stopPolling();
    };
  }, [currentPage, pageSize, number, polling]);

  const onPageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  return (
    <div>
      {contextHolder}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
        <div style={{ position: 'relative', width: 400, display: 'flex', gap: 8 }}>
          <Input
            ref={inputRef}
            placeholder="请输入变量名称"
            style={{ width: '100%' }}
            value={keyword}
            onChange={onInputChange}
            onPressEnter={handleSearch}
            suffix={loading && <LoadingOutlined style={{ color: '#1890ff' }} />}
          />
          <Button
            onClick={handleReset}
            style={{
              borderRadius: '4px',
              fontSize: '14px',
              height: '32px',
              width: '80px',
              border: '1px solid #1677ff',
              color: '#1677ff',
              background: 'transparent'
            }}
          >
            重置
          </Button>
          <Button
            type="primary"
            onClick={handleSearch}
            style={{
              borderRadius: '4px',
              fontSize: '14px',
              height: '32px',
              width: '80px'
            }}
          >
            查询
          </Button>
        </div>
        
        <Tag
          color={polling ? "green" : "red"}
          style={{ cursor: 'pointer' }}
          onClick={togglePolling}
        >
          {polling ? "自动刷新已开启" : "自动刷新已关闭"}
        </Tag>
      </div>

      <div style={{ marginTop: 20 }}>
        <Row gutter={[24, 24]}>
          {deviceList.map(item => (
            <Col key={item.id} span={6}>
              <Card loading={loading}>
                <div>
                  <Flex>
                    <div style={{ fontSize: '18px', fontWeight: 700, marginBottom: 16 }}>{item.title}</div>
                  </Flex>
                  <Flex justify='space-between' style={{ marginBottom: 16 }}>
                    {
                      item.type === 2 ?
                        <div style={{ fontSize: '18px', fontWeight: 700 }}>
                          {item.val ? '开' : '关'}
                        </div> : <div style={{ fontSize: '18px', fontWeight: 700 }}>
                          {item.val ? item.val : '-'}
                          <span style={{ fontSize: 12, fontWeight: 500, marginLeft: 5 }}>
                            {item.unit}
                          </span>
                        </div>
                    }
                    <div><Tag color="blue">{typeNames[item.type]}</Tag></div>
                  </Flex>
                  <Flex justify='space-between'>
                    <div></div>
                    <div style={{ color: '#a1a1a1', fontSize: '12px' }}>{item.updatedAt}</div>
                  </Flex>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      <Flex style={{ marginTop: 20 }} justify='flex-end'>
        <Pagination
          total={total}
          pageSize={pageSize}
          current={currentPage}
          onChange={onPageChange}
          showSizeChanger
          showQuickJumper
          showTotal={(t) => `共 ${t} 条`}
        />
      </Flex>
    </div>
  );
};

export default DeviceStatusPage;
