/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-24 14:50:02
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import React, { useEffect, useState } from 'react';
import { Descriptions, message, Spin } from 'antd';
import { postRequest } from '@/services/api/api';

interface SealingProps {
  number: string | number; // 父级ID
}

interface SealingData {
  name: string;
  account: string;
  cardNum: string;
  deviceCode: string;
  holeNumber: string;
  sealingDepth: string | number;
  cementContent: string | number;
  createdAt: string;
}

// 封控记录组件
const Sealing: React.FC<SealingProps> = ({ number }) => {
  const [items, setItems] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchSealingRecord = async () => {
      try {
        setLoading(true);
        const result = await postRequest('drill/get_sealing_record', {
          parentId: number
        });
        
        const { data, status, msg } = result;
        
        if (status === 0 && data) {
          const formattedItems = [
            { label: '打孔人员', children: data.name },
            { label: '人员账号', children: data.account },
            { label: '卡号', children: data.cardNum },
            { label: '设备编号', children: data.deviceCode },
            { label: '孔号', children: data.holeNumber },
            { label: '封控深度', children: data.sealingDepth },
            { label: '水泥量', children: data.cementContent },
            { label: '创建时间', children: data.createdAt },
          ];
          
          setItems(formattedItems);
        } else {
          message.error(msg || '获取封控记录失败');
        }
      } catch (error) {
        console.error('获取封控记录出错:', error);
        message.error('获取封控记录失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    if (number) {
      fetchSealingRecord();
    }
  }, [number]);

  return (
    <Spin spinning={loading}>
      <Descriptions 
        bordered={false} 
        layout="vertical" 
        items={items} 
        column={4} 
        contentStyle={{ fontSize: '18px' }}
        labelStyle={{ fontSize: '18px' }} 
      />
    </Spin>
  );
};

export default Sealing;
