/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\TeamEfficiencyTable.tsx
 * @Description: 班组效能对比表格组件 - 展示各班组的任务完成率、已完成任务数、平均进尺、异常次数等关键指标
 */

import { Card, Table, Empty } from 'antd';
import { TableOutlined } from '@ant-design/icons';
import React from 'react';
import styles from '../newTaskDashboard.less';

// API返回的班组效能数据结构
interface ApiTeamEfficiencyData {
  任务完成率: string;  // "50%"
  平均孔深: string;    // "150"
  已完成任务数: number; // 1
  异常次数: number;    // 0
}

// 表格显示的数据结构
interface TeamEfficiencyData {
  team: string;           // 班组名称
  taskComplete: string;   // 任务完成率 "50%"
  completedTasks: number; // 已完成任务数
  avgDistance: string;    // 平均孔深
  abnormalCount: number;  // 异常次数
}

interface TeamEfficiencyTableProps {
  // 班组效能数据 - 可以是JSON字符串或已解析的数据
  teamEfficiencyData?: string | TeamEfficiencyData[];
  // 是否显示分页
  showPagination?: boolean;
  // 表格大小
  tableSize?: 'small' | 'middle' | 'large';
}

const TeamEfficiencyTable: React.FC<TeamEfficiencyTableProps> = ({
  teamEfficiencyData,
  showPagination = false,
  tableSize = 'middle',
}) => {
  // 解析班组效能数据
  const parseTeamEfficiencyData = (data: string | TeamEfficiencyData[] | undefined): TeamEfficiencyData[] => {
    if (!data) return [];

    // 如果已经是数组格式，直接返回
    if (Array.isArray(data)) return data;

    try {
      // 解析JSON字符串
      const parsedData: Record<string, ApiTeamEfficiencyData> = JSON.parse(data);
      console.log('🏢 解析班组效能数据:', parsedData);

      // 转换为表格数据格式
      return Object.entries(parsedData).map(([teamName, teamData]) => ({
        team: teamName,
        taskComplete: teamData.任务完成率,
        completedTasks: teamData.已完成任务数,
        avgDistance: teamData.平均孔深,
        abnormalCount: teamData.异常次数,
      }));
    } catch (error) {
      console.error('🏢 解析班组效能数据失败:', error);
      return [];
    }
  };

  const tableData = parseTeamEfficiencyData(teamEfficiencyData);

  // 表格列定义
  const teamColumns = [
    {
      title: '班组名称',
      dataIndex: 'team',
      key: 'team',
      width: '20%',
    },
    {
      title: '任务完成率',
      dataIndex: 'taskComplete',
      key: 'taskComplete',
      width: '20%',
      render: (val: string) => val || '0%', // 直接显示字符串，已包含%符号
    },
    {
      title: '已完成任务数',
      dataIndex: 'completedTasks',
      key: 'completedTasks',
      width: '20%',
      render: (val: number) => val || 0,
    },
    {
      title: '平均孔深（m）',
      dataIndex: 'avgDistance',
      key: 'avgDistance',
      width: '20%',
      render: (val: string) => val || '0',
    },
    {
      title: '异常次数',
      dataIndex: 'abnormalCount',
      key: 'abnormalCount',
      width: '20%',
      render: (val: number) => val || 0,
    },
  ];

  return (
    <Card
      title="班组效能对比"
      bordered={false}
      className={styles.tableCard}
      headStyle={{
        fontSize: '22px',
        fontWeight: 'normal',
        fontFamily: 'PingFang SC',
        color: '#ffffff',
      }}
    >
      <Table
        columns={teamColumns}
        dataSource={tableData}
        pagination={showPagination ? undefined : false}
        size={tableSize}
        rowKey="team"
        scroll={{ x: 'max-content' }}
        locale={{
          emptyText: (
            <Empty
              image={<TableOutlined style={{ fontSize: 48, color: '#666' }} />}
              description={
                <span style={{ color: '#999', fontSize: '14px' }}>
                  暂无班组效能数据
                </span>
              }
            />
          ),
        }}
      />
    </Card>
  );
};

export default TeamEfficiencyTable;
