# 设备管理页面摄像头配置功能

## 🎯 **功能概述**
在设备管理页面的表格列表中新增了"摄像头配置"按钮，位于"配置账号"按钮后面，提供摄像头序列号和通道号的配置功能。

## 📊 **功能实现详情**

### **1. 新增状态管理**
```tsx
// 摄像头配置相关状态
const [cameraConfigModalVisible, setCameraConfigModalVisible] = useState(false);
const [currentCameraDevice, setCurrentCameraDevice] = useState({ id: '', deviceName: '' });
const [cameraForm] = Form.useForm();
```

### **2. 弹框显示函数（含数据预填充）**
```tsx
// 显示摄像头配置弹框
const showCameraConfigModal = async (record: any) => {
    setCurrentCameraDevice({ id: record.id, deviceName: record.deviceName });

    try {
        // 调用设备详情接口获取现有配置
        const result = await getRequest('device/get_info', { id: record.id });
        const { status, msg, data } = result as any;

        if (status === 0 && data) {
            // 如果有摄像头配置参数，解析并填充表单
            if (data.ysParam) {
                const ysParamData = JSON.parse(data.ysParam);
                cameraForm.setFieldsValue({
                    serialNumber: ysParamData.deviceSerial || '',
                    channelNumber: ysParamData.channelNo || ''
                });
            } else {
                cameraForm.resetFields();
            }
        } else {
            messageApi.warning('获取设备信息失败，将显示空白表单');
            cameraForm.resetFields();
        }
    } catch (error) {
        messageApi.error('获取设备信息异常');
        cameraForm.resetFields();
    }

    setCameraConfigModalVisible(true);
};
```

### **3. 弹框处理函数**
```tsx
// 摄像头配置取消处理
const handleCameraConfigCancel = () => {
    setCameraConfigModalVisible(false);
    cameraForm.resetFields();
};

// 摄像头配置确认处理
const handleCameraConfigConfirm = async () => {
    try {
        const formValues = await cameraForm.validateFields();
        console.log('摄像头配置数据:', formValues);

        // 构造摄像头配置参数
        const ysParam = JSON.stringify({
            channelNo: formValues.channelNumber,
            deviceSerial: formValues.serialNumber
        });

        // 调用摄像头配置API
        const result = await postRequest('device/post_modify', {
            id: currentCameraDevice.id,
            ysParam: ysParam
        });

        messageApi.success('摄像头配置成功');
        setCameraConfigModalVisible(false);
        cameraForm.resetFields();
    } catch (error) {
        if (error.errorFields) {
            messageApi.error('请完成所有必填字段');
        } else {
            messageApi.error('摄像头配置失败');
        }
    }
};
```

### **4. 表格操作列新增按钮**
```tsx
// 摄像头配置
<a
    key="camera-config"
    onClick={() => { showCameraConfigModal(record) }}
    style={{ marginLeft: '24px' }}
>
    摄像头配置
</a>
```

### **5. 摄像头配置弹框组件**
```tsx
<Modal
    title="摄像头配置"
    open={cameraConfigModalVisible}
    onOk={handleCameraConfigConfirm}
    onCancel={handleCameraConfigCancel}
    okText="确认"
    cancelText="取消"
>
    <Form form={cameraForm} layout="vertical" style={{ marginTop: '24px' }}>
        <Form.Item
            label="序列号："
            name="serialNumber"
            rules={[{ required: true, message: '请输入序列号' }]}
        >
            <Input
                placeholder='请输入序列号'
                style={{ backgroundColor: '#2a2a2a', color: '#fff', border: '1px solid #555' }}
            />
        </Form.Item>
        <Form.Item
            label="通道号："
            name="channelNumber"
            rules={[{ required: true, message: '请输入通道号' }]}
        >
            <Input
                placeholder='请输入通道号'
                style={{ backgroundColor: '#2a2a2a', color: '#fff', border: '1px solid #555' }}
            />
        </Form.Item>
    </Form>
</Modal>
```

## 🎨 **UI设计特点**

### **按钮位置**
- 位于表格操作列中
- 紧跟在"配置账号"按钮后面
- 使用相同的样式和间距

### **弹框设计**
- 标题：摄像头配置
- 布局：垂直表单布局
- 样式：与配置账号弹框保持一致的深色主题
- 按钮：确认/取消

### **表单字段**
1. **序列号**：必填字段，用于设备识别
2. **通道号**：必填字段，用于视频通道配置

## 🔧 **技术实现要点**

### **状态管理**
- 使用独立的状态管理摄像头配置弹框
- 表单使用独立的Form实例
- 当前设备信息存储在状态中

### **表单验证**
- 两个字段都设置为必填
- 提供友好的错误提示信息
- 表单重置功能

### **样式一致性**
- 输入框样式与配置账号保持一致
- 使用深色主题配色
- 边距和布局与现有组件统一

## 📝 **使用流程**

1. **打开弹框**：点击设备列表中的"摄像头配置"按钮
2. **自动加载**：系统自动调用设备详情接口，预填充现有配置
3. **编辑信息**：
   - 设备序列号：如果已配置会自动填充，否则需要手动输入
   - 通道号：如果已配置会自动填充，否则需要手动输入
4. **提交配置**：点击"确认"按钮提交配置
5. **取消操作**：点击"取消"按钮关闭弹框

## 🧪 **测试说明**

### **调试信息**
配置过程中会在控制台输出以下调试信息：

**1. 打开弹框时的设备详情获取：**
```javascript
🎥 获取设备详情，设备ID: 123
🎥 设备详情API响应: {
  status: 0,
  msg: "success",
  data: {
    ysParam: "{\"channelNo\":\"1\",\"deviceSerial\":\"33010175992677797274:33010042991117112440\"}"
  }
}
🎥 解析的摄像头配置: {
  channelNo: "1",
  deviceSerial: "33010175992677797274:33010042991117112440"
}
```

**2. 提交配置时的API调用：**
```javascript
🎥 摄像头配置请求参数: {
  id: 123,
  ysParam: "{\"channelNo\":\"1\",\"deviceSerial\":\"33010175992677797274:33010042991117112440\"}",
  parsedYsParam: {
    channelNo: "1",
    deviceSerial: "33010175992677797274:33010042991117112440"
  }
}
🎥 摄像头配置API响应: { status: 0, msg: "success" }
```

### **测试步骤**
1. 打开浏览器开发者工具
2. 点击摄像头配置按钮
3. 填写测试数据并提交
4. 查看控制台输出和API响应

## 🔌 **API接口说明**

### **1. 获取设备详情接口**
- **接口地址**: `/device/get_info`
- **请求方法**: GET
- **参数**: `{ id: 设备ID }`
- **用途**: 获取现有摄像头配置，用于表单预填充

### **2. 摄像头配置接口**
- **接口地址**: `/device/post_modify`
- **请求方法**: POST
- **Content-Type**: application/json

### **请求参数**
```json
{
  "id": 123,                    // 设备ID (integer, 必需)
  "ysParam": "{\"channelNo\": \"1\", \"deviceSerial\": \"33010175992677797274:33010042991117112440\"}"
}
```

### **ysParam参数说明**
ysParam是一个JSON字符串，包含以下字段：
- **channelNo**: 通道号 (string)
- **deviceSerial**: 设备序列号 (string)

### **参数示例**
```javascript
const ysParam = JSON.stringify({
    channelNo: "1",
    deviceSerial: "33010175992677797274:33010042991117112440"
});
```

## 🚀 **后续开发**

### **功能扩展**
可以考虑添加：
1. 序列号格式验证
2. 通道号范围验证
3. 配置历史记录
4. 批量配置功能

现在设备管理页面已经成功添加了摄像头配置功能，与配置账号功能保持一致的用户体验！
