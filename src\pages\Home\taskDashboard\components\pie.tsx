/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-03 11:26:01
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import { Chart } from '@antv/g2';
import React, { useEffect } from 'react';

interface TaskData {
  taskSumYes: number;
  taskNo: number;
  taskSum: number;
}

interface ChartDataItem {
  type: string;
  percent: number;
  color: string;
}

interface PieProps {
  data: TaskData;
}

const Pie: React.FC<PieProps> = ({ data: taskData }) => {
  useEffect(() => {
    // 用于在数据加载前设定默认值
    const taskNoCount = taskData?.taskNo ?? 0;
    const taskYesCount = taskData?.taskSumYes ?? 0;
    const taskSum = taskData.taskSum;
    const chartData: ChartDataItem[] = [
      { type: '未完成', percent: taskNoCount, color: '#EAB308' },
      { type: '已完成', percent: taskYesCount, color: '#22C55E' },
    ];

    const chart = new Chart({
      container: 'container',
      autoFit: true,
      height: 300,
      theme: 'dark',
    });

    const facetRect = chart
      .facetRect()
      .data(chartData)
      .encode('x', 'type')
      .axis(false)
      .legend(false)
      .view()
      .attr('frame', false)
      .coordinate({ type: 'theta', innerRadius: 0.5, outerRadius: 0.8 });

    facetRect
      .interval()
      .encode('y', taskSum)
      .scale('y', { zero: true })
      .style('fill', '#e8e8e8')
      .tooltip(false)
      .animate(false);
    

    facetRect
      .interval()
      .encode('y', 'percent')
      .encode('color', 'color')
      .scale('color', { type: 'identity' })
      .tooltip(false)
      .animate('enter', { type: 'waveIn', duration: 1000 });

    facetRect
      .text()
      .encode('text', 'type')
      .style('textAlign', 'center')
      .style('textBaseline', 'middle')
      .style('fontSize', 20)
      .style('fill', 'rgba(255, 255, 255, 0.85)')
      // .style('color','255, 255, 255')
      
      .style('x', '50%')
      .style('y', '50%')
      .style('dy', -20);

    facetRect
      .text()
      .encode('text', (d: ChartData) => `${d.percent} 个`)
      // .encode('text', 'percent')
      .style('textAlign', 'center')
      .style('textBaseline', 'middle')
      .style('fontSize', 20)
      .style('fontWeight', 500)
      .style('Color', '#ffffff')
      .style('fill', 'rgba(255, 255, 255, 0.85)')
      .style('x', '50%')
      .style('y', '50%')
      .style('dy', 20);
      facetRect.interaction('tooltip', false);
    chart.render();

    // 清除函数
    return () => {
      chart.destroy();
    };
  }, [taskData]);

  return <div id="container"></div>;
};

export default Pie;
