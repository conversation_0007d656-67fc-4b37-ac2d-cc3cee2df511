import React, { useRef, useEffect } from 'react';
import { message } from 'antd';
import { history } from 'umi';
import { Form,Breadcrumb,Typography,Card,Divider,Space,Button } from 'antd';
import { postRequest } from '@/services/api/api';
import ThreeForm, { Column, ThreeFormRef } from '@/components/ThreeForm';

const { Title } = Typography;
interface FormData {
  constructionNumber: string;
  constructionName: string;
  constructionUnit: string;
  constructionPerson: string;
  constructionCrews: string;
  startDate: string;
  endDate: string;
  actualStarttime: string;
  actualEndtime: string;
  constructionProgress: string;
  acceptancePersonnel: string;
  acceptanceDate: string;
  remark: string;
}

interface ApiResponse {
  status: number;
  msg?: string;
  data?: any;
}

const DrillAdd: React.FC = () => {
  const [form] = Form.useForm();
  const formRef = useRef<ThreeFormRef>(null);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    return () => {
      form.resetFields();
    };
  }, [form]);

  const columns: Column[] = [
    {
      label: '打孔计划名称',
      dataIndex: 'planName',
      type: 'text',
      rules: [{ required: true, message: '请输入打孔计划名称' }],
    },
    {
      label: '计划类型',
      dataIndex: 'type',
      type: 'text',
      // type: 'select',
      rules: [{ required: true, message: '请选择计划类型' }],
      // options: [
      //   { label: '日常计划', value: '日常计划' },
      //   { label: '应急计划', value: '应急计划' },
      //   { label: '临时计划', value: '临时计划' },
      // ],
    },
    {
      label: '计划施工地点',
      dataIndex: 'position',
      type: 'text',
      rules: [{ required: true, message: '请输入计划施工地点' }],
    },

    {
      label: '计划开始日期',
      dataIndex: 'startTime',
      type: 'date',
      rules: [{ required: true, message: '请选择计划开始日期' }],
    },
    {
      label: '计划结束日期',
      dataIndex: 'endTime',
      type: 'date',
      rules: [{ required: true, message: '请选择计划结束日期' }],
    },
    // {
    //   label: '实际开始日期',
    //   dataIndex: 'actualStarttime',
    //   type: 'date',
    //   rules: [{ required: true, message: '请选择实际开始日期' }],
    // },
    // {
    //   label: '实际结束日期',
    //   dataIndex: 'actualEndtime',
    //   type: 'date',
    //   rules: [{ required: true, message: '请选择实际结束日期' }],
    // },
    {
      label: '打孔数量',
      dataIndex: 'drillNum',
      type: 'text',
      rules: [{ required: true, message: '请输入打孔数量' }],
    },   
    {
      label: '负责人员',
      dataIndex: 'responsiblePersonnel',
      type: 'text',
      rules: [{ required: true, message: '请输入负责人员' }],
    },
  ];

  const onFinish = async (values: FormData) => {
    try {
      const result = await postRequest<ApiResponse>('drillPlan/post_add', values);
      if (result.status === 0) {
        messageApi.open({
          type: 'success',
          content: '添加成功',
        });
        form.resetFields();
        history.go(-1)
      } else {
        messageApi.error(result.msg || '添加失败');
      }
    } catch (error) {
      messageApi.error('添加失败');
    }
  };

  return (
    <>
      {contextHolder}
      <Breadcrumb
        items={[
          { title: '首页' },
          { title: '施工管理' },
          { title: '打孔计划' },
          { title: '添加打孔计划' }
        ]}
      />

      <Card style={{ marginTop: '24px' }}>
        <Title level={5}>{'添加打孔计划'}</Title>
        <Divider />
        <ThreeForm
          form={form}
          columns={columns}
          onFinish={onFinish}
        />
        <Form.Item style={{ textAlign: 'center', marginTop: '40px' }}>
        <Space>
          <Button onClick={() => history.go(-1)}>取消</Button>
          <Button type="primary" htmlType="submit" onClick={()=>onFinish}>
            提交
          </Button>
        </Space>
      </Form.Item>
      </Card>
    </>
  );
};

export default DrillAdd; 