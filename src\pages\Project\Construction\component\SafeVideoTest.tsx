import React, { useState } from 'react';
import { Card, Button, Input, Space, message, Switch } from 'antd';
import SafeVideoPlayer from './SafeVideoPlayer';
import EzVideoPlayer from './EzVideoPlayer';

/**
 * 安全视频播放器测试组件
 * 用于对比SafeVideoPlayer和EzVideoPlayer的稳定性
 */
const SafeVideoTest: React.FC = () => {
  const [ezOpenUrl, setEzOpenUrl] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [useSafePlayer, setUseSafePlayer] = useState<boolean>(true);

  // 测试URL
  const testUrls = [
    'ezopen://open.ys7.com/33010175992677797274:33010042991117112440/1.rec?begin=20250701000000&end=20250702235959',
    'ezopen://open.ys7.com/test1/test1.live',
    'ezopen://open.ys7.com/test2/test2.live',
    'ezopen://open.ys7.com/test3/test3.live'
  ];

  const handlePlay = () => {
    if (!ezOpenUrl.trim()) {
      message.warning('请输入有效的 ezopen URL');
      return;
    }

    if (!ezOpenUrl.startsWith('ezopen://')) {
      message.error('URL 必须以 ezopen:// 开头');
      return;
    }

    setIsPlaying(true);
    message.success('开始播放视频');
  };

  const handleStop = () => {
    setIsPlaying(false);
    message.info('停止播放');
  };

  const handleUrlChange = (url: string) => {
    setEzOpenUrl(url);
    if (isPlaying) {
      message.info('URL已更新，播放器将重新加载');
    }
  };

  const handleCapture = (type: 'image' | 'video', data: any) => {
    console.log('抓拍事件:', { type, data });
    message.success(`${type === 'image' ? '图片' : '视频'}抓拍成功`);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card title="安全视频播放器测试" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          
          {/* 播放器类型选择 */}
          <div>
            <label style={{ marginRight: '12px' }}>播放器类型:</label>
            <Switch
              checked={useSafePlayer}
              onChange={setUseSafePlayer}
              checkedChildren="安全播放器"
              unCheckedChildren="原始播放器"
            />
            <span style={{ marginLeft: '12px', color: '#666' }}>
              {useSafePlayer ? '使用iframe隔离的安全播放器' : '使用原始EzVideoPlayer'}
            </span>
          </div>

          {/* URL输入 */}
          <div>
            <label style={{ display: 'block', marginBottom: '8px' }}>视频URL:</label>
            <Input.TextArea
              value={ezOpenUrl}
              onChange={(e) => setEzOpenUrl(e.target.value)}
              placeholder="请输入 ezopen:// 协议的视频URL"
              rows={3}
              style={{ marginBottom: '12px' }}
            />
          </div>

          {/* 快速测试URL */}
          <div>
            <label style={{ display: 'block', marginBottom: '8px' }}>快速测试:</label>
            <Space wrap>
              {testUrls.map((url, index) => (
                <Button
                  key={index}
                  size="small"
                  onClick={() => handleUrlChange(url)}
                  type={ezOpenUrl === url ? 'primary' : 'default'}
                >
                  测试URL {index + 1}
                </Button>
              ))}
            </Space>
          </div>

          {/* 控制按钮 */}
          <Space>
            <Button type="primary" onClick={handlePlay} disabled={!ezOpenUrl.trim()}>
              开始播放
            </Button>
            <Button onClick={handleStop} disabled={!isPlaying}>
              停止播放
            </Button>
            <Button 
              onClick={() => {
                setEzOpenUrl('');
                setIsPlaying(false);
              }}
            >
              清空
            </Button>
          </Space>

          {/* 状态显示 */}
          <div style={{ 
            padding: '12px', 
            background: '#f6f8fa', 
            borderRadius: '6px',
            fontSize: '14px'
          }}>
            <div><strong>当前状态:</strong> {isPlaying ? '播放中' : '已停止'}</div>
            <div><strong>播放器类型:</strong> {useSafePlayer ? 'SafeVideoPlayer (iframe隔离)' : 'EzVideoPlayer (直接DOM)'}</div>
            <div><strong>当前URL:</strong> {ezOpenUrl || '未设置'}</div>
          </div>
        </Space>
      </Card>

      {/* 视频播放器 */}
      {isPlaying && ezOpenUrl && (
        <Card 
          title={`视频播放 - ${useSafePlayer ? 'SafeVideoPlayer' : 'EzVideoPlayer'}`} 
          style={{ marginTop: '24px' }}
        >
          <div style={{ width: '100%', height: '500px' }}>
            {useSafePlayer ? (
              <SafeVideoPlayer
                ezOpenUrl={ezOpenUrl}
                onCapture={handleCapture}
              />
            ) : (
              <EzVideoPlayer
                ezOpenUrl={ezOpenUrl}
                width="100%"
                height="100%"
                style={{
                  border: '1px solid #d9d9d9',
                  borderRadius: '8px'
                }}
                onCapture={handleCapture}
              />
            )}
          </div>
          
          {/* 使用说明 */}
          <div style={{ 
            marginTop: '16px', 
            padding: '12px', 
            background: '#f0f9ff', 
            borderRadius: '6px',
            fontSize: '14px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>测试说明:</div>
            <div>• 快速切换不同的测试URL，观察是否出现DOM错误</div>
            <div>• 对比两种播放器的稳定性和性能</div>
            <div>• SafeVideoPlayer使用iframe隔离，应该更稳定</div>
            <div>• 查看浏览器控制台的错误信息</div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SafeVideoTest;
