import React, { useRef, useEffect } from 'react';
import { ProForm, ProFormText, ProFormSelect, ProFormInstance, ProFormDatePicker, ProFormTextArea, ProFormUploadButton } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import { history } from '@umijs/max';
import { postRequest } from '@/services/api/api';

export interface Column {
    label: string;
    dataIndex: string;
    type?: 'Select' | 'DatePicker' | 'TextArea' | 'Upload';
    rules?: any[];
    fieldProps?: any;
    api?: string;
    options?: { label: string; value: number | string }[];
}

interface CustomFormProps {
    initialValues?: any;
    columns: Column[];
    onFinish: (values: any) => void;
}

const SubmitForm: React.FC<CustomFormProps> = ({ initialValues = {}, columns, onFinish }) => {
    const formRef = useRef<ProFormInstance>();
    const [messageApi] = message.useMessage();

    const handleSubmit = () => {
        formRef.current?.validateFieldsReturnFormatValue?.().then((values) => {
            onFinish(values);
        });
    };

    useEffect(() => {
        formRef?.current?.setFieldsValue({ ...initialValues });
    }, [initialValues]);

    return (
        <ProForm
            formRef={formRef}
            initialValues={initialValues}
            submitter={{
                render: () => {
                    return [
                        <Button htmlType="button" onClick={() => { history.go(-1) }} key="cancel">
                            取消
                        </Button>,
                        <Button htmlType="button" onClick={handleSubmit} key="submit" type="primary">
                            提交
                        </Button>,
                    ];
                },
            }}
        >
            {columns.map((item) => {
                if (item.type === 'Select') {
                    return (
                        <ProFormSelect
                            key={item.dataIndex}
                            width="md"
                            name={item.dataIndex}
                            label={item.label}
                            fieldProps={item.fieldProps}
                            request={async (params) => {
                                if (item.api) {
                                    let values: any = { page: 1, perPage: 10 }
                                    if (params.keyWords) {
                                        values = { ...values, keyword: params.keyword }
                                    }
                                    const result: any = await postRequest(item.api, values);
                                    const { data, status, msg } = result
                                    if (status === 0) {
                                        return data.items
                                    } else {
                                        messageApi.error(msg);
                                        return []
                                    }
                                } else {
                                    return item.options || []
                                }
                            }}
                            placeholder={`请选择${item.label}`}
                            rules={item.rules}
                        />
                    );
                } else if (item.type === 'DatePicker') {
                    return (
                        <ProFormDatePicker
                            key={item.dataIndex}
                            width="md"
                            name={item.dataIndex}
                            label={item.label}
                            placeholder={`请选择${item.label}`}
                            rules={item.rules}
                        />
                    );
                }
                else if (item.type === 'TextArea') {
                    return (
                        <ProFormTextArea
                            key={item.dataIndex}
                            width="md"
                            name={item.dataIndex}
                            label={item.label}
                            placeholder={`请输入${item.label}`}
                            rules={item.rules}
                        />
                    );
                }
                else if (item.type === 'Upload') {
                    return (
                        <ProFormUploadButton
                            key={item.dataIndex}
                            name={item.dataIndex}
                            label={item.label}
                            max={1}
                            fieldProps={{
                                name: 'file',
                                listType: 'text',
                                ...item.fieldProps,
                            }}
                            rules={item.rules}
                        />
                    );
                }
                return (
                    <ProFormText
                        key={item.dataIndex}
                        width="md"
                        name={item.dataIndex}
                        label={item.label}
                        placeholder={`请输入${item.label}`}
                        rules={item.rules}
                    />
                );
            })}
        </ProForm>
    );
};

export default SubmitForm;
