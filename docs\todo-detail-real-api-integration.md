# 待办详情真实API数据集成

## 🎯 功能概述

根据真实的API返回数据结构，完善待办详情页面的数据解析和显示逻辑，实现真正的数据驱动界面。

## 📊 API数据结构分析

### 原始API响应
```json
{
  "status": 0,
  "msg": "request:ok",
  "data": {
    "id": 44,
    "title": "祁强提交的开孔申请",
    "submitter": "祁强",
    "account": "***********",
    "status": 0,
    "dynamicFields": "[{\"title\": \"操作员\", \"value\": \"祁强\"}, {\"title\": \"孔号\", \"value\": \"1-6#\"}, {\"title\": \"钻机名称\", \"value\": \"钻机10号\"}, ...]",
    "drillParams": "{\"偏差值\": {...}, \"实际参数\": {...}, \"设计参数\": {...}}",
    "deviceCode": "rig_gateway_test011",
    "createdAt": "2025-04-14 16:51:32",
    "updateAt": "2025-07-08 16:42:46",
    // ... 其他字段
  }
}
```

### 关键字段说明
- **deviceCode**: 设备序列号，用于获取直播地址
- **dynamicFields**: JSON字符串，包含动态表单字段
- **drillParams**: JSON字符串，包含钻孔的设计参数、实际参数和偏差值
- **status**: 审批状态 (0:待审批 1:已同意 2：已拒绝)
- **todoType**: 待办类型 (0开孔申请 1验孔申请 2封孔提醒 等)

## 🔧 技术实现

### 1. 数据类型定义

#### API原始数据类型
```typescript
interface TodoApiResponse {
    id: number;
    title: string;
    submitter: string;
    account: string;
    status: number; // 0:待审批 1:已同意 2：已拒绝
    dynamicFields: string; // JSON字符串
    refuse: string | null;
    createdAt: string;
    updateAt: string;
    approvalAt: string | null;
    correlationId: number;
    approver: string;
    approverAccount: string;
    ids: string; // JSON字符串
    type: number; // 1-待办 2-通知
    todoType: number; // 0开孔申请 1验孔申请 2封孔提醒 等
    drillParams: string; // JSON字符串
    deviceCode: string; // 设备序列号
    imageUrls: string | null;
    videoUrls: string | null;
}
```

#### 钻孔参数类型
```typescript
interface DrillParams {
    设计参数: {
        孔深: string;
        方位角: string;
        开孔角度: string;
        开孔高度: string;
        见岩距离: string;
        见煤距离: string;
    };
    实际参数: {
        孔深: string;
        方位角: string;
        开孔角度: string;
        开孔高度: string;
        见岩距离: string;
        见煤距离: string;
    };
    偏差值: {
        孔深: string;
        方位角: string;
        开孔角度: string;
        开孔高度: string;
        见岩距离: string;
        见煤距离: string;
    };
}
```

#### 动态字段类型
```typescript
interface DynamicField {
    title: string;
    value: string;
}
```

### 2. 数据解析逻辑

#### 主要解析函数
```typescript
const fetchTodoDetail = async () => {
    try {
        // 调用API
        const response = await getRequest(`/todo/get_info?id=${todoId}`) as {
            status: number;
            msg: string;
            data: TodoApiResponse;
        };

        if (response.status === 0 && response.data) {
            const apiData = response.data;
            
            // 解析动态字段
            let dynamicFields: DynamicField[] = [];
            try {
                dynamicFields = JSON.parse(apiData.dynamicFields);
            } catch (error) {
                console.error('解析动态字段失败:', error);
            }
            
            // 解析钻孔参数
            let drillParams: DrillParams | null = null;
            try {
                drillParams = JSON.parse(apiData.drillParams);
            } catch (error) {
                console.error('解析钻孔参数失败:', error);
            }
            
            // 数据转换...
        }
    } catch (error) {
        console.error('获取待办详情出错:', error);
        message.error('获取待办详情失败');
    }
};
```

#### 动态字段提取
```typescript
// 从动态字段中提取信息
const getFieldValue = (title: string): string => {
    const field = dynamicFields.find(f => f.title === title);
    return field ? field.value : '';
};

// 使用示例
const drillName = getFieldValue('钻机名称') || '未知钻机';
const holeNumber = getFieldValue('孔号') || '未知孔号';
```

#### 钻孔参数转换
```typescript
// 转换为组件需要的数据格式
const todoData: TodoDetailData = {
    id: apiData.id,
    title: apiData.title,
    submitter: apiData.submitter,
    drillName: getFieldValue('钻机名称') || '未知钻机',
    deviceCode: apiData.deviceCode,
    holeNumber: getFieldValue('孔号') || '未知孔号',
    
    // 从钻孔参数中提取数据
    planHoleAngle: drillParams ? parseFloat(drillParams.设计参数.开孔角度) : 0,
    actualHoleAngle: drillParams ? parseFloat(drillParams.实际参数.开孔角度) : 0,
    planAzimuth: drillParams ? parseFloat(drillParams.设计参数.方位角) : 0,
    actualAzimuth: drillParams ? parseFloat(drillParams.实际参数.方位角) : 0,
    planHeight: drillParams ? parseFloat(drillParams.设计参数.开孔高度) : 0,
    actualHeight: drillParams ? parseFloat(drillParams.实际参数.开孔高度) : 0,
    planCoalDistance: drillParams ? parseFloat(drillParams.设计参数.见煤距离) : 0,
    actualCoalDistance: drillParams ? parseFloat(drillParams.实际参数.见煤距离) : 0,
    planRockDistance: drillParams ? parseFloat(drillParams.设计参数.见岩距离) : 0,
    actualRockDistance: drillParams ? parseFloat(drillParams.实际参数.见岩距离) : 0,
    planDepth: drillParams ? parseFloat(drillParams.设计参数.孔深) : 0,
    actualDepth: drillParams ? parseFloat(drillParams.实际参数.孔深) : 0,
    
    createTime: apiData.createdAt,
    updateTime: apiData.updateAt,
    // ... 其他字段
};
```

### 3. 直播功能集成

#### 设备代码获取
```typescript
// 设置数据后，使用真实的deviceCode获取直播地址
setTodoData(todoData);

// 如果有设备code，获取直播地址
if (todoData.deviceCode) {
    fetchLiveAddress(todoData.deviceCode);
}
```

#### 直播地址调用
```typescript
// 使用真实的设备代码调用直播接口
const response = await postRequest(
    `/api/ys/get_live_address?deviceCode=${todoData.deviceCode}`, 
    {}
) as LiveAddressResponse;
```

## 🔄 数据流程

### 1. 完整的数据处理流程
```
用户打开待办详情
    ↓
调用 /todo/get_info?id={todoId}
    ↓
解析 dynamicFields (JSON字符串)
    ↓
解析 drillParams (JSON字符串)
    ↓
提取钻机名称、孔号等信息
    ↓
转换钻孔参数 (字符串 → 数字)
    ↓
构建 TodoDetailData 对象
    ↓
设置组件状态
    ↓
使用 deviceCode 获取直播地址
    ↓
渲染页面内容
```

### 2. 错误处理流程
```
API调用失败 → 显示错误消息
JSON解析失败 → 使用默认值 + 错误日志
数据转换失败 → 使用默认值 + 错误日志
直播获取失败 → 显示直播错误状态
```

## 📊 数据映射表

### 动态字段映射
| API字段 | 组件字段 | 说明 |
|---------|----------|------|
| dynamicFields[title="钻机名称"].value | drillName | 钻机名称 |
| dynamicFields[title="孔号"].value | holeNumber | 孔号 |
| dynamicFields[title="操作员"].value | - | 操作员信息 |

### 钻孔参数映射
| API字段 | 组件字段 | 转换 |
|---------|----------|------|
| drillParams.设计参数.开孔角度 | planHoleAngle | parseFloat() |
| drillParams.实际参数.开孔角度 | actualHoleAngle | parseFloat() |
| drillParams.设计参数.方位角 | planAzimuth | parseFloat() |
| drillParams.实际参数.方位角 | actualAzimuth | parseFloat() |
| drillParams.设计参数.孔深 | planDepth | parseFloat() |
| drillParams.实际参数.孔深 | actualDepth | parseFloat() |

### 基本信息映射
| API字段 | 组件字段 | 说明 |
|---------|----------|------|
| id | id | 待办ID |
| title | title | 待办标题 |
| submitter | submitter | 提交人 |
| deviceCode | deviceCode | 设备代码（用于直播） |
| createdAt | createTime | 创建时间 |
| updateAt | updateTime | 更新时间 |

## 🎯 功能特点

### 1. 真实数据驱动
- ✅ **动态解析**: 自动解析JSON字符串字段
- ✅ **类型转换**: 字符串数字自动转换为数值类型
- ✅ **错误容错**: JSON解析失败时使用默认值
- ✅ **数据验证**: 确保数据完整性

### 2. 设备直播集成
- ✅ **真实设备**: 使用API返回的真实deviceCode
- ✅ **自动获取**: 数据加载完成后自动获取直播地址
- ✅ **错误处理**: 直播获取失败时的友好提示

### 3. 灵活的数据结构
- ✅ **动态字段**: 支持动态表单字段的解析
- ✅ **钻孔参数**: 完整的设计值、实际值、偏差值
- ✅ **扩展性**: 易于添加新的字段映射

## 🧪 测试验证

### 1. 数据解析测试
- 检查动态字段是否正确解析
- 验证钻孔参数的数值转换
- 确认错误处理机制

### 2. 直播功能测试
- 验证deviceCode是否正确传递
- 检查直播地址获取是否成功
- 测试播放器是否正常工作

### 3. 边界情况测试
- JSON解析失败的处理
- 缺失字段的默认值处理
- 网络异常的错误处理

## 📁 相关文件

- `src/pages/Todo/components/TodoDetail.tsx` - 主要修改文件
- `docs/todo-detail-real-api-integration.md` - 本文档

## 🎉 总结

通过这次改造，待办详情页面实现了：

- 🎯 **真实数据集成**: 使用真实的API数据而非模拟数据
- 🎯 **智能数据解析**: 自动解析复杂的JSON字符串字段
- 🎯 **设备直播功能**: 使用真实的deviceCode获取直播地址
- 🎯 **完善的错误处理**: 多层次的错误处理和容错机制

现在待办详情页面可以显示真实的钻孔数据，并提供真正的设备直播功能了！🚀
