/**
 * 全局错误处理工具
 * 用于捕获和处理未被组件捕获的JavaScript错误
 */

// 错误类型定义
interface ErrorInfo {
  message: string;
  stack?: string;
  filename?: string;
  lineno?: number;
  colno?: number;
  timestamp: number;
  userAgent: string;
  url: string;
}

// 错误处理配置
const ERROR_CONFIG = {
  // 是否在控制台显示详细错误
  showDetailInConsole: process.env.NODE_ENV === 'development',
  // 是否显示用户友好的错误提示
  showUserFriendlyMessage: true,
  // 错误上报的最大数量（防止错误风暴）
  maxErrorCount: 10,
  // 相同错误的最小间隔时间（毫秒）
  minErrorInterval: 5000,
};

// 错误计数器
let errorCount = 0;
const errorCache = new Map<string, number>();

/**
 * 格式化错误信息
 */
function formatError(error: Error | ErrorEvent | string): ErrorInfo {
  const now = Date.now();
  
  if (error instanceof Error) {
    return {
      message: error.message,
      stack: error.stack,
      timestamp: now,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
  }
  
  if (error instanceof ErrorEvent) {
    return {
      message: error.message,
      stack: error.error?.stack,
      filename: error.filename,
      lineno: error.lineno,
      colno: error.colno,
      timestamp: now,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
  }
  
  return {
    message: String(error),
    timestamp: now,
    userAgent: navigator.userAgent,
    url: window.location.href,
  };
}

/**
 * 检查是否应该处理这个错误
 */
function shouldHandleError(errorInfo: ErrorInfo): boolean {
  // 检查错误数量限制
  if (errorCount >= ERROR_CONFIG.maxErrorCount) {
    console.warn('🚨 错误数量超过限制，停止处理新错误');
    return false;
  }
  
  // 检查相同错误的间隔时间
  const errorKey = `${errorInfo.message}-${errorInfo.filename}-${errorInfo.lineno}`;
  const lastErrorTime = errorCache.get(errorKey);
  
  if (lastErrorTime && (errorInfo.timestamp - lastErrorTime) < ERROR_CONFIG.minErrorInterval) {
    console.warn('🚨 相同错误触发过于频繁，跳过处理');
    return false;
  }
  
  errorCache.set(errorKey, errorInfo.timestamp);
  return true;
}

/**
 * 显示用户友好的错误提示
 */
function showUserFriendlyError(errorInfo: ErrorInfo) {
  // 检查错误是否与萤石云播放器相关
  const isVideoPlayerError = 
    errorInfo.message.includes('EZUIKitPlayer') ||
    errorInfo.message.includes('insertBefore') ||
    errorInfo.message.includes('ezuikit') ||
    errorInfo.stack?.includes('EZUIKitPlayer');
  
  if (isVideoPlayerError) {
    console.warn('🎬 检测到视频播放器相关错误，已被组件错误边界处理');
    return;
  }
  
  // 对于其他类型的错误，可以显示通用提示
  if (ERROR_CONFIG.showUserFriendlyMessage) {
    // 这里可以集成antd的message或notification
    // 但要避免在错误处理中引入可能出错的依赖
    console.error('🚨 应用程序出现错误:', errorInfo.message);
  }
}

/**
 * 上报错误到服务器（可选）
 */
function reportError(errorInfo: ErrorInfo) {
  // 这里可以实现错误上报逻辑
  // 例如发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // fetch('/api/error-report', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorInfo)
    // }).catch(() => {
    //   // 忽略上报失败
    // });
  }
}

/**
 * 全局错误处理函数
 */
function handleGlobalError(error: Error | ErrorEvent | string) {
  const errorInfo = formatError(error);
  
  if (!shouldHandleError(errorInfo)) {
    return;
  }
  
  errorCount++;
  
  if (ERROR_CONFIG.showDetailInConsole) {
    console.group('🚨 全局错误处理');
    console.error('错误信息:', errorInfo);
    console.error('原始错误:', error);
    console.groupEnd();
  }
  
  // 显示用户友好提示
  showUserFriendlyError(errorInfo);
  
  // 上报错误
  reportError(errorInfo);
}

/**
 * 初始化全局错误处理
 */
export function initGlobalErrorHandler() {
  // 捕获JavaScript运行时错误
  window.addEventListener('error', (event) => {
    handleGlobalError(event);
  });
  
  // 捕获Promise未处理的rejection
  window.addEventListener('unhandledrejection', (event) => {
    handleGlobalError(new Error(`Unhandled Promise Rejection: ${event.reason}`));
    
    // 阻止默认的控制台错误输出
    event.preventDefault();
  });
  
  console.log('✅ 全局错误处理已初始化');
}

/**
 * 手动处理错误
 */
export function handleError(error: Error | string, context?: string) {
  const errorWithContext = context 
    ? new Error(`[${context}] ${error instanceof Error ? error.message : error}`)
    : error;
    
  handleGlobalError(errorWithContext);
}

/**
 * 重置错误计数器（用于测试或特殊情况）
 */
export function resetErrorCounter() {
  errorCount = 0;
  errorCache.clear();
  console.log('✅ 错误计数器已重置');
}

// 导出配置，允许外部修改
export { ERROR_CONFIG };
