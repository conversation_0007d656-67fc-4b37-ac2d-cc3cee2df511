import { EllipsisOutlined, InboxOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormText,
    ProTable,
    ProForm,
    ProFormSelect,
    ProFormCascader
} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Breadcrumb, Cascader, Button, Select, Tag, Upload, Form } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';
import { OutTable, ExcelRenderer } from 'react-excel-renderer';
import axios from 'axios';
import type { GetProp, UploadFile, UploadProps } from 'antd';

const { RangePicker } = DatePicker;

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
    taskNum?: number;
    taskFinishNum?: number;
    curatorName?: string;
    curatorAccount?: string;
    [key: string]: any; // 允许其他动态属性
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}

interface PersonalItem {
    id: number;
    name: string;
    account: string;
    [key: string]: any;
}

interface PersonalResponse {
    data: PersonalItem[];
    status: number;
    msg: string;
}

// 定义接口类型
interface DrillPlanChild {
  id: string | number;
  name: string;
}

interface DrillPlan {
  id: string | number;
  name: string;
  children: DrillPlanChild[];
}

interface CascaderOption {
  value: string | number;
  label: string;
  children?: CascaderOption[];
}

export default () => {
    const ref = useRef();
    const { Option } = Select;
    const [form] = Form.useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryType, setQueryType] = useState('0');
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });
    const [importModalVisible, setImportModalVisible] = useState(false);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [importLoading, setImportLoading] = useState(false);
    const [excelCols, setExcelCols] = useState([]);
    const [excelRows, setExcelRows] = useState([]);
    const [drillPlanOptions, setDrillPlanOptions] = useState<DrillPlanOption[]>([]);
    const [personalList, setPersonalList] = useState<{ label: string; value: number }[]>([]);
    const [currentRecord, setCurrentRecord] = useState<TableListItem | null>(null);

    const selectBefore = (
        <Select value={queryType} onChange={(value) => setQueryType(value)} >
            <Option value="0">单个查询</Option>
            <Option value="1">多个查询</Option>
        </Select>
    );
    // 获取任务列表
    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };
    interface Option {
        value: string;
        label: string;
        children?: Option[];
      }
      
      const options: Option[] = [
        {
          value: 'zhejiang',
          label: 'Zhejiang',
          children: [
            {
              value: 'hangzhou',
              label: 'Hangzhou',
              children: [
                {
                  value: 'xihu',
                  label: 'West Lake',
                },
              ],
            },
          ],
        },
        {
          value: 'jiangsu',
          label: 'Jiangsu',
          children: [
            {
              value: 'nanjing',
              label: 'Nanjing',
              children: [
                {
                  value: 'zhonghuamen',
                  label: 'Zhong Hua Men',
                },
              ],
            },
          ],
        },
      ];
    const handleCancel = () => {
        setIsModalOpen(false);
    };
// 获取钻场/drillsite/get_all_area
const fetchDrillSite = async () => {
    const result = await getRequest('drillsite/get_all_area');
    const { data, status, msg } = result as any;    
    if (status === 0 && data) {

          const transformedData = data.map((item: any) => ({
            value: item.id,
            label: item.name,
            children: item.children?.map((child: any) => ({
                value: child.id,
                label: child.name,
                children: child.children?.map((subChild: any) => ({
                    value: subChild.id,
                    label: subChild.name
                }))
            }))
        }));
        setDrillPlanOptions(transformedData);
    } else {
        messageApi.error(msg);
    }
}
    const columns: ProColumns<TableListItem>[] = [
        // {
        //     title: '任务编号',
        //     dataIndex: 'taskCode',
        //     fixed: 'left',
        //     ellipsis: true,
        //     search: false,
        // },
        {
            title: '钻场名称',
            dataIndex: 'drillSite',
            ellipsis: true,
            search: false,
        },
        {
            title:'设计与完成比例',
            dataIndex:'taskRatio',
            ellipsis: true,
            search: false,
            render: (_, record) => {
                const designNum = record.taskNum || 0;
                const finishNum = record.taskFinishNum || 0;
                return `${designNum}:${finishNum}`;
            },
        },
        {
            title:'钻机数量',
            dataIndex:'deviceNum',
            ellipsis: true,
            search: false,
        },
        {
            title: '负责人',
            dataIndex: 'curatorName',
            ellipsis: true,
            search: false,
        },
        {
            disable: true,
            title: '状态',
            dataIndex: 'status',
            filters: true,
            onFilter: true,
            ellipsis: true,
            valueType: 'select',
            search: false,
            valueEnum: {
                0: {
                    text: '进行中',
                    status: 'Processing',
                },
                1: {
                    text: '已完成',
                    status: 'Success',
                },
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            ellipsis: true,
            search: false,
        },
        {
            title: '操作',
            valueType: 'option',
            fixed: 'right',
            render: (_, record) => [
                <a
                    key="detail"
                    onClick={() => {
                        form.setFieldsValue({
                            curatorId: record.curatorName,
                            curatorName: record.curatorName,
                            curatorAccount: record.curatorAccount,
                        });
                        setCurrentRecord(record);
                        setImportModalVisible(true);
                    }}
                >
                    调整
                </a>,
                <a
                    key="detail"
                    onClick={() => {
                        history.push(`/project/Deliver/modify/?id=${record.id}`);
                        // history.push(`/project/Deliver/modify/?id=1`);
                    }}
                >
                    详情
                </a>,
            ],
        },
        {
            title: '',
            dataIndex: 'keyWord',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onChange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (
                      <Cascader options={drillPlanOptions} onChange={onChange} placeholder="选择钻场" />
                );
            },
        },
        // {
        //     title: '',
        //     dataIndex: 'keyWord',
        //     hideInTable: true,
        //     renderFormItem: (item, config, form) => {
        //         const label = item.dataIndex
        //         const status = form.getFieldValue(label);
        //         const onchange = (value) => {
        //             form.setFieldsValue({ [label]: value })
        //         }
        //         return (<>
        //             <Input
        //                 value={status}
        //                 onChange={(e) => onchange(e.target.value)}
        //                 placeholder='请输入任务名称'
        //             />
        //         </>);
        //     },
        // },

        // {
        //     title: '',
        //     dataIndex: 'dateRange',
        //     hideInTable: true,
        //     renderFormItem: (item, config, form) => {
        //         const label = item.dataIndex
        //         const status = form.getFieldValue(label);
        //         const onchange = (value) => {
        //             form.setFieldsValue({ [label]: value })
        //         }
        //         return (<>
        //             <RangePicker
        //                 value={status}
        //                 style={{ width: '300px' }}
        //                 onChange={(value) => onchange(value)}
        //                 picker="date"
        //                 format="YYYY-MM-DD"
        //             />
        //         </>);
        //     },
        // },
    ];

    const columns1 = [
        {
            title: '变更字段',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '变更前',
            dataIndex: 'age',
            key: 'age',
        },
        {
            title: '变更后',
            dataIndex: 'address',
            key: 'address',
        },
    ];

    const data1 = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];

    const handleExcelUpload = (file: File) => {
        ExcelRenderer(file, (error, response) => {
            if (error) {
                messageApi.error('文件解析失败');
                return;
            }

            setExcelCols(response.cols);
            setExcelRows(response.rows);
        });
    };

    const uploadProps: UploadProps = {
        onRemove: (file) => {
            const index = fileList.indexOf(file);
            const newFileList = fileList.slice();
            newFileList.splice(index, 1);
            setFileList(newFileList);
            setExcelCols([]);
            setExcelRows([]);
        },
        beforeUpload: (file) => {
            setFileList([...fileList, file]);
            handleExcelUpload(file);
            return false;
        },
        fileList,
        accept: '.xlsx,.xls',
        maxCount: 1
    };

    const handleImport = async () => {
        setImportLoading(true);
        try {
            const formData = new FormData();
            formData.append('file', fileList[0] as any);
            formData.append('upload_type', 'local');
            formData.append('file_name', fileList[0].name);
            fetch(`${VITE_APP_BASE_URL}drilltask/post_import`, {
                method: 'POST',
                headers: {
                    'authorization': sessionStorage.getItem('token'),
                    // 'authorization': 'eyJhbGciOiJIUzI1NiJ9.eyJpbmZvIjoie1wiYWNjb3VudFwiOlwiMTUxMzczODQ0ODRcIixcImNvcnBJZFwiOlwidGZsTGM0cHZFTGtlRHM0OW1lWTY5alF5QlZYdTlUVWttd0NcIixcIm5hbWVcIjpcIumprOS_iuadsFwiLFwicHJvZmlsZVBpY1wiOlwiaHR0cHM6Ly93d3cuYmluZy5jb20vdGg_aWRcXHUwMDNkT0lQLnlwLUQtS0hJM2Uybk40ZU1CSmNFVkFBQUFBXFx1MDAyNndcXHUwMDNkOTlcXHUwMDI2aFxcdTAwM2QxMDBcXHUwMDI2Y1xcdTAwM2Q4XFx1MDAyNnJzXFx1MDAzZDFcXHUwMDI2cWx0XFx1MDAzZDkwXFx1MDAyNm9cXHUwMDNkNlxcdTAwMjZwaWRcXHUwMDNkMy4xXFx1MDAyNnJtXFx1MDAzZDJcIn0ifQ.T7YqVBJoycD1pxuD27HaX2fLm_pmxyG9TJX39ftX41I',
                    'serviceType': 2,
                },
                body: formData,
            })
                .then((res) => res.json())
                .then((res) => {
                    if (res.status === 0) {
                        messageApi.success('导入成功');
                        setImportModalVisible(false);
                        setExcelCols([]);
                        setExcelRows([]);
                        setFileList([]);
                        ref.current?.reload();
                    } else {
                        messageApi.error(res.msg || '导入失败');
                    }
                })
                .catch(() => {
                    messageApi.error('导入失败');
                })
                .finally(() => {
                });
        } catch (error) {
            messageApi.error('导入失败');
        } finally {
            setImportLoading(false);
        }
    };

    // 获取人员列表
    const fetchPersonalList = async () => {
        try {
            const result = await getRequest('personal/get_all') as PersonalResponse;
            if (result.status === 0 && result.data) {
                const options = result.data.map((item: PersonalItem) => ({
                    label: item.name,
                    value: item.id,
                    account: item.account,
                }));
                setPersonalList(options);
            }
        } catch (error) {
            messageApi.error('获取人员列表失败');
        }
    };
// 下载导入的模板
const downloadTemplate = () => {
    const fileUrl = './xlsx/drill.xlsx';
    
    // 显示加载状态
    setImportLoading(true);
    
    // 使用fetch获取文件内容
    fetch(fileUrl, {
        // 可以添加必要的headers，如果需要的话
        // headers: {
        //   'authorization': sessionStorage.getItem('token') || '',
        // },
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.blob();
        })
        .then(blob => {
            // 创建blob URL
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '钻孔设计模版.xlsx';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url); // 释放URL对象
            document.body.removeChild(a);
        })
        .catch(error => {
            messageApi.error('下载模板失败');
            console.error('下载错误:', error);
        })
        .finally(() => {
            setImportLoading(false);
        });
}
    // 处理负责人选择
    const handlePersonalChange = (value: number, option: any) => {
        form.setFieldsValue({
            curatorId: value,
            curatorName: option.label,
            curatorAccount: option.account,
        });
    };

    useEffect(() => {
        if (importModalVisible) {
            fetchPersonalList();
        }
    }, [importModalVisible]);

    // 添加一个辅助函数来转换选项
    const transformToCascaderOptions = (data: any): CascaderOption[] => {
      const transform = (item: any): CascaderOption => ({
        value: item.id,
        label: item.name,
        children: item.children?.map(transform)
      });
      
      return data.map(transform);
    };
    const getFullPath = (options: any[], selectedKeys: (string | number)[]) => {
        const result: string[] = [];
        let currentOptions = options;
        
        for (const key of selectedKeys) {
            const found = currentOptions.find(item => item.value === key);
            if (found) {
                result.push(found.label);
                currentOptions = found.children || [];
            }
        }
        
        return result;
    };
    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页' },
                    { title: '施工管理' },
                    { title: '下发任务' },
                ]}
            />
            <ProTable<TableListItem>
                style={{ marginTop: '24px' }}
                headerTitle='下发任务'
                actionRef={ref}
                columns={columns}
                // scroll={{ x: 2600 }}
                request={async (params, sorter, filter) => {
                    fetchDrillSite();
                    const { current, pageSize, keyWord, dateRange, ...rest } = params;
                    if (keyWord !== undefined && keyWord.length !== 3) {
                        messageApi.error('请正确选择钻场信息');
                        return {
                            data: [],
                            total: 0,
                            success: false,
                        };
                    }
                
                    let postData = {
                        page: current,
                        perPage: pageSize,
                        keyWord: keyWord ? keyWord[2] : '',
                        startTime: dateRange?.[0],
                        endTime: dateRange?.[1],
                    }
                    const result = await postRequest('drilltask/get_ls', postData);
                    const { data, status, msg } = result as any
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                rowKey="key"
                toolBarRender={() => [
                    <Button
                        key="import"
                        type="primary"
                        onClick={() => {
                            setImportModalVisible(true);
                        }}
                        style={{ marginRight: 8 }}
                    >
                        下发任务
                    </Button>,
                    // <Button
                    //     key="add"
                    //     type="primary"
                    //     onClick={() => history.push('/construct/hole/add')}
                    // >
                    //     新建打孔计划
                    // </Button>,
                ]}
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,

                    className: 'search-form'
                }}
                form={{
                    initialValues: {
                        sort: 0
                    }
                }}
            />

            <Modal
                title="下发任务"
                open={importModalVisible}
                footer={null}
                onCancel={() => {
                    setImportModalVisible(false);
                    setExcelCols([]);
                    setExcelRows([]);
                    setFileList([]);
                    setCurrentRecord(null);
                    form.resetFields();
                }}
                width={650}
            >
                <ProForm
                    form={form}
                    submitter={{
                        render: (props) => {
                            return (
                                <div style={{ textAlign: 'right', marginTop: 16 }}>
                                    <Button
                                        style={{ marginRight: 8 }}
                                        onClick={() => {
                                            setImportModalVisible(false);
                                            setExcelCols([]);
                                            setExcelRows([]);
                                            setFileList([]);
                                            setCurrentRecord(null);
                                            form.resetFields();
                                        }}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        loading={importLoading}
                                        disabled={fileList.length === 0}
                                        onClick={() => props.submit()}
                                    >
                                        完成
                                    </Button>
                                </div>
                            );
                        },
                    }}
                    onFinish={async (values) => {

                        // 获取完整的选择路径名称
                        const fullPath = currentRecord?currentRecord:getFullPath(drillPlanOptions, values.taskName);
                        // 验证选择路径是否完整
                    if (!currentRecord && fullPath.length !== 3) {
                        messageApi.error('请完整选择钻场信息（采面/巷道/钻场）');
                        setImportLoading(false);
                        return;
                    }
                        setImportLoading(true);
                        try {
                            const api = currentRecord ? 'drilltask/post_modify' : 'drilltask/post_add'
                            const result = await postRequest(api, {
                                ...(currentRecord ? { id: currentRecord.id } : {}),
                                miningFace:currentRecord?fullPath.miningFace:fullPath[0],//采面
                                faceId:currentRecord?fullPath.faceId:values.taskName[0],
                                lane:currentRecord?fullPath.lane:fullPath[1],//巷道
                                laneId:currentRecord?fullPath.laneId:values.taskName[1],
                                drillSite:currentRecord?fullPath.drillSite:fullPath[2],//钻场
                                drillSiteId:currentRecord?fullPath.drillSiteId:values.taskName[2],
                                curatorName: values.curatorName,
                                curatorAccount: values.curatorAccount,
                            });
                            if (result.status === 0) {
                                const formData = new FormData();
                                formData.append('file', fileList[0]);
                                formData.append('id', result.data.insertId);
                                const res = await fetch(`${VITE_APP_BASE_URL}drilltask/post_import`, {
                                    method: 'POST',
                                    headers: {
                                        'authorization':sessionStorage.getItem('token'),
                                        'serviceType': '1'
                                    },
                                    body: formData
                                });
                                const resData = await res.json();
                                if (resData.status === 0) {
                                    messageApi.success(currentRecord ? '修改成功' : '添加成功');
                                    setImportModalVisible(false);
                                    setExcelCols([]);
                                    setExcelRows([]);
                                    setFileList([]);
                                    setCurrentRecord(null);
                                    form.resetFields();
                                    ref.current?.reload();
                                } else {
                                    messageApi.error(resData.msg || '添加失败');
                                }
                            } else {
                                messageApi.error(result.msg || '添加失败');
                            }
                        } catch (error) {
                            messageApi.error('添加失败');
                            console.error('Error:', error);
                        } finally {
                            setImportLoading(false);
                        }
                    }}
                >
                    {!currentRecord && (
                        <ProFormCascader
                        name="taskName"
                        label="钻场名称"
                        placeholder="选择钻场"
                        fieldProps={{
                          showSearch: true, // 启用搜索功能
                          changeOnSelect: true, // 允许选择任意级别
                        }}
                        rules={[
                          { 
                            required: true, 
                            message: '请选择钻场名称' 
                          }
                        ]}
                        request={async () => drillPlanOptions}
                      />
                    )}
                    <ProForm.Item
                        name="file"
                        label="上传工艺卡"
                        rules={[{ required: true, message: '请上传上传工艺卡' }]}
                        tooltip="注意：调整任务时请将无需调整的任务在文档中去掉再进行导入！"
                    >
                        <Upload.Dragger {...uploadProps}>
                            <p className="ant-upload-drag-icon">
                                <InboxOutlined />
                            </p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                            <p className="ant-upload-hint">支持 .xlsx, .xls 格式的文件</p>
                        </Upload.Dragger>
                    </ProForm.Item>
                    <div style={{display:"flex",justifyContent:"space-between"}}><span>钻孔设计模版.doc</span> <span><Button color="default" size="middle" onClick={downloadTemplate} variant="outlined">
            下载模板
          </Button>
</span> </div>
                    <ProFormSelect
                        name="curatorId"
                        label="负责人"
                        placeholder="请选择负责人"
                        options={personalList}
                        fieldProps={{
                            onChange: handlePersonalChange
                        }}
                        rules={[{ required: true, message: '请选择负责人' }]}
                    />
                    <ProFormText name="curatorName" hidden />
                    <ProFormText name="curatorAccount" hidden />
                </ProForm>
            </Modal>
            <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal>
        </>
    );
};