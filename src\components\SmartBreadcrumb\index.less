.smartBreadcrumb {
  .breadcrumbItem {
    cursor: pointer;
    transition: color 0.3s ease;
    
    &:hover {
      color: #1890ff;
    }
    
    &.active {
      color: #1890ff;
      font-weight: 500;
    }
  }
  
  .historyNav {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 12px;
    color: #999;
    max-width: 100%;
    
    .historyLabel {
      white-space: nowrap;
      flex-shrink: 0;
      min-width: fit-content;
    }
    
    .historyContainer {
      display: flex;
      align-items: center;
      gap: 8px;
      overflow-x: auto;
      flex: 1;
      padding-bottom: 2px;
      
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 2px;
        
        &:hover {
          background: #bfbfbf;
        }
      }
      
      /* Firefox 滚动条样式 */
      scrollbar-width: thin;
      scrollbar-color: #d9d9d9 transparent;
    }
    
    .historyItem {
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;
      white-space: nowrap;
      flex-shrink: 0;
      border: 1px solid transparent;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 6px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
        border-color: #d9d9d9;
      }

      &.active {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff;
        text-decoration: underline;

        &:hover {
          background-color: rgba(24, 144, 255, 0.15);
          border-color: #40a9ff;
        }
      }

      .historyTitle {
        flex: 1;
      }

      .closeIcon {
        opacity: 0.7; // 稍微透明一些，但一直显示
        transition: all 0.2s ease;
        font-size: 10px;
        padding: 2px;
        border-radius: 2px;
        color: #999;

        &:hover {
          opacity: 1;
          background-color: rgba(255, 77, 79, 0.1);
          color: #ff4d4f;
        }
      }
    }

    .clearAllIcon {
      margin-left: 8px;
      padding: 4px;
      border-radius: 4px;
      color: #999;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 12px;
      flex-shrink: 0;

      &:hover {
        background-color: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }
    }
  }
}

// 暗色主题适配
.dark {
  .smartBreadcrumb {
    .historyNav {
      color: #999;
      
      .historyContainer {
        &::-webkit-scrollbar-thumb {
          background: #434343;
          
          &:hover {
            background: #595959;
          }
        }
        
        scrollbar-color: #434343 transparent;
      }
      
      .historyItem {
        &:hover {
          background-color: rgba(255, 255, 255, 0.04);
          border-color: #434343;
        }

        &.active {
          background-color: rgba(24, 144, 255, 0.2);
          color: #40a9ff;

          &:hover {
            background-color: rgba(24, 144, 255, 0.25);
            border-color: #40a9ff;
          }
        }

        .closeIcon {
          color: #999;

          &:hover {
            background-color: rgba(255, 77, 79, 0.2);
            color: #ff7875;
          }
        }
      }

      .clearAllIcon {
        color: #999;

        &:hover {
          background-color: rgba(255, 77, 79, 0.2);
          color: #ff7875;
        }
      }
    }
  }
}
