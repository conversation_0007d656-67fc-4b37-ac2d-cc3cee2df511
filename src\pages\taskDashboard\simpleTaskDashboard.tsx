/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-06-19 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-06-19 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\simpleTaskDashboard.tsx
 * @Description: 简化版任务看板页面，展示核心指标和基础图表
 */

import { getRequest } from '@/services/api/api';
import { Row, Col, message } from 'antd';
import React, { useEffect, useState } from 'react';
import MetricsCards from './components/MetricsCards';
import GoalAnalysisChart from './components/GoalAnalysisChart';
import EquipmentPieChart from './components/EquipmentPieChart';
import AbnormalStatsChart from './components/AbnormalStatsChart';
// import RadarComparisonChart from './components/RadarComparisonChart';
// import EquipmentHealthRanking from './components/EquipmentHealthRanking';
import TeamEfficiencyTable from './components/TeamEfficiencyTable';
import styles from './newTaskDashboard.less';

const SimpleTaskDashboard: React.FC = () => {
  // 核心指标状态定义
  const [totalDistance, setTotalDistance] = useState('');
  const [distanceChange, setDistanceChange] = useState('');
  const [stationCount, setStationCount] = useState('');
  const [operationRate, setOperationRate] = useState('');
  const [taskCount, setTaskCount] = useState('');
  const [taskCompletionRate, setTaskCompletionRate] = useState('');
  const [abnormalCount, setAbnormalCount] = useState('');
  const [abnormalChange, setAbnormalChange] = useState('');
  const [operatingSum, setOperatingSum] = useState('');
  const [operatingRate, setOperatingRate] = useState('');

  // 新增状态 - 用于图表组件
  const [goalAnalysisData, setGoalAnalysisData] = useState<any[]>([]);
  const [equipmentStatusData, setEquipmentStatusData] = useState<any[]>([]);
  const [abnormalStatsData, setAbnormalStatsData] = useState<any[]>([]);
  const [utilizationRate, setUtilizationRate] = useState(0);
  const [efficiency, setEfficiency] = useState(0);
  const [teamEfficiencyData, setTeamEfficiencyData] = useState<string>('');

  // 数据验证函数
  const validateAndProcessData = (data: any) => {
    console.log('🔍 开始数据验证和处理...');

    // 验证必要字段是否存在
    const requiredFields = [
      'footageSum', 'footageEd', 'target', 'runSum', 'runRate',
      'operatingSum', 'operatingRate', 'taskSum', 'taskRate',
      'abnormalNum', 'abnormalEd', 'useRatio', 'singleUnitEfficiency'
    ];

    const missingFields = requiredFields.filter(field => data[field] === undefined);
    if (missingFields.length > 0) {
      console.warn('⚠️ 缺少必要字段:', missingFields);
    }

    // 验证异常事件字段
    const abnormalFields = [
      'sensorAbnormalNum', 'drillSequenceAbnormalNum', 'drillBreakNum',
      'pressureDrillNum', 'gasAbnormalNum', 'smokeNum', 'sprayHoleNum', 'rockDeviationNum'
    ];

    const missingAbnormalFields = abnormalFields.filter(field => data[field] === undefined);
    if (missingAbnormalFields.length > 0) {
      console.warn('⚠️ 缺少异常事件字段:', missingAbnormalFields);
    }

    console.log('✅ 数据验证完成');
    return true;
  };

  // 获取数据的方法 - 使用新接口 /dataBoard/get_ls_v1
  const handleEdit = async () => {
    try {
      console.log('🚀 开始调用新任务看板接口: /dataBoard/get_ls_v1');
      const result = await getRequest('dataBoard/get_ls_v1');

      // 打印完整的接口返回数据
      console.log('📊 新任务看板接口返回数据:', JSON.stringify(result, null, 2));

      const { status, msg, data } = result as any;

      if (status === 0) {
        console.log('✅ 接口调用成功，开始解析数据...');
        console.log('📋 接口返回的data字段:', JSON.stringify(data, null, 2));

        // 验证数据
        validateAndProcessData(data);

        // 打印每个字段的值
        console.log('🔍 数据字段解析:');
        console.log('  - footageSum (总进尺/米):', data.footageSum);
        console.log('  - footageEd (较昨日):', data.footageEd);
        console.log('  - target (实际进尺):', data.target);
        console.log('  - runSum (运行钻机数):', data.runSum);
        console.log('  - runRate (运行率):', data.runRate);
        console.log('  - taskSum (总任务数):', data.taskSum);
        console.log('  - taskRate (任务完成率):', data.taskRate);
        console.log('  - abnormalNum (异常事件):', data.abnormalNum);
        console.log('  - abnormalEd (较昨日):', data.abnormalEd);
        console.log('  - operatingSum (设备开机数量):', data.operatingSum);
        console.log('  - operatingRate (设备开机率):', data.operatingRate);
        console.log('  - openNum (设备打开状态数量-运行):', data.openNum);
        console.log('  - closeNum (设备关闭状态数量-停机):', data.closeNum);
        console.log('  - eroNum (设备损坏状态数量-维修):', data.eroNum);
        console.log('  - useRatio (设备利用率):', data.useRatio);
        console.log('  - singleUnitEfficiency (单机效率):', data.singleUnitEfficiency);
        console.log('  - teamEfficiency (班组效能):', data.teamEfficiency);

        // 核心指标数据设置
        setTotalDistance(data.footageSum || '0');
        setDistanceChange(data.footageEd || '0');
        setStationCount(data.runSum?.toString() || '0');
        setOperationRate(data.runRate || '0');
        setTaskCount(data.taskSum?.toString() || '0');
        setTaskCompletionRate(data.taskRate || '0');
        setAbnormalCount(data.abnormalNum?.toString() || '0');
        setAbnormalChange(data.abnormalEd?.toString() || '0');
        setOperatingSum(data.operatingSum?.toString() || '0');
        setOperatingRate(data.operatingRate || '0');

        // 图表组件数据处理
        // 1. 目标达成分析数据 - 修正字段含义
        const goalData = [
          { name: '总进尺', value: parseFloat(data.target || '0') },
          { name: '实际进尺', value: parseFloat(data.footageSum || '0') },
        ];
        setGoalAnalysisData(goalData);

        // 2. 设备状态数据 - 重新对接字段映射
        const equipmentData = [
          { status: '打开', count: data.openNum || 0, color: '#1E40AF' },      // 运行 = 设备打开状态
          { status: '关闭', count: data.closeNum || 0, color: '#C2C2C2' },     // 停机 = 设备关闭状态
          { status: '损坏', count: data.eroNum || 0, color: '#FF9200' },       // 维修 = 设备损坏状态
        ];
        setEquipmentStatusData(equipmentData);

        // 3. 异常事件统计数据
        const abnormalData = [
          { type: '传感器异常', count: data.sensorAbnormalNum || 0, color: '#007fff' },
          { type: '钻杆顺序异常', count: data.drillSequenceAbnormalNum || 0, color: '#FF9200' },
          { type: '钻杆断裂', count: data.drillBreakNum || 0, color: '#6c63f0' },
          { type: '压钻', count: data.pressureDrillNum || 0, color: '#8B5CF6' },
          { type: '瓦斯异常', count: data.gasAbnormalNum || 0, color: '#00b042' },
          { type: '冒烟', count: data.smokeNum || 0, color: '#FF5722' },
          { type: '喷孔', count: data.sprayHoleNum || 0, color: '#9C27B0' },
          { type: '见岩偏差过大', count: data.rockDeviationNum || 0, color: '#C2C2C2' },
        ];
        setAbnormalStatsData(abnormalData);

        // 4. 设备利用率和单机效率
        setUtilizationRate(parseFloat(data.useRatio || '0'));
        setEfficiency(parseFloat(data.singleUnitEfficiency || '0'));

        // 5. 班组效能数据处理 - 直接传递JSON字符串给组件处理
        const teamEfficiencyJson = data.teamEfficiency || '';
        setTeamEfficiencyData(teamEfficiencyJson);
        console.log('📊 班组效能JSON数据:', teamEfficiencyJson);

        // 打印处理后的图表数据
        console.log('📈 处理后的图表数据:');
        console.log('  - 目标达成分析数据:', goalData);
        console.log('  - 设备状态数据:', equipmentData);
        console.log('  - 异常事件统计数据:', abnormalData);
        console.log('  - 设备利用率:', parseFloat(data.useRatio || '0'));
        console.log('  - 单机效率:', parseFloat(data.singleUnitEfficiency || '0'));

        console.log('✨ 数据设置完成，状态已更新');
      } else {
        console.error('❌ 接口调用失败:', { status, msg });
        message.error(msg);
      }
    } catch (error) {
      console.error('💥 获取数据失败:', error);
      console.error('错误详情:', JSON.stringify(error, null, 2));
    }
  };



  // 组件挂载时获取数据
  useEffect(() => {
    handleEdit();
  }, []);



  return (
    <div className={styles.dashboardContainer}>
      {/* 核心指标卡片区域 */}
      <MetricsCards
        totalDistance={totalDistance}
        distanceChange={distanceChange}
        stationCount={stationCount}
        operationRate={operationRate}
        taskCount={taskCount}
        taskCompletionRate={taskCompletionRate}
        abnormalCount={abnormalCount}
        abnormalChange={abnormalChange}
        operatingSum={operatingSum}
        operatingRate={operatingRate}
      />

      {/* 数据可视化图表区域 - 第一行三个模块 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        {/* 目标达成分析 */}
        <Col xs={24} md={8}>
          <GoalAnalysisChart goalAnalysisData={goalAnalysisData} />
        </Col>

        {/* 设备投入产出比 */}
        <Col xs={24} md={8}>
          <EquipmentPieChart
            equipmentStatusData={equipmentStatusData}
            utilizationRate={utilizationRate}
            efficiency={efficiency}
          />
        </Col>

        {/* 异常事件统计 */}
        <Col xs={24} md={8}>
          <AbnormalStatsChart abnormalStatsData={abnormalStatsData} />
        </Col>
      </Row>

      {/* 高级分析区域 - 第二行两个模块 */}
      {/* <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        钻机效率雷达对比
        <Col xs={24} md={12}>
          <RadarComparisonChart />
        </Col>

        设备健康评分排行榜
        <Col xs={24} md={12}>
          <EquipmentHealthRanking />
        </Col>
      </Row> */}

      {/* 班组效能对比 - 第三行独占一行 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col xs={24}>
          <TeamEfficiencyTable teamEfficiencyData={teamEfficiencyData} />
        </Col>
      </Row>
    </div>
  );
};

export default SimpleTaskDashboard;
