const mockCards = [
  {
    id: 1,
    cardNumber: 'CARD001',
    cardStatus: 1,
    employeeId: 1001,
    employeeName: '张三',
    employeeAccount: 'z<PERSON><PERSON>',
    bindTime: '2024-01-15 09:30:00',
    unbindTime: null,
    bindOperatorAccount: 'admin',
    bindOperatorName: '管理员',
    remark: '正常使用中',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-15 09:30:00',
  },
  {
    id: 2,
    cardNumber: 'CARD002',
    cardStatus: 2,
    employeeId: 1002,
    employeeName: '李四',
    employeeAccount: 'lisi',
    bindTime: '2024-01-12 14:20:00',
    unbindTime: '2024-01-20 16:45:00',
    bindOperatorAccount: 'admin',
    bindOperatorName: '管理员',
    remark: '卡片丢失，已解绑',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-20 16:45:00',
  },
  {
    id: 3,
    cardNumber: 'CARD003',
    cardStatus: 0,
    employeeId: null,
    employeeName: null,
    employeeAccount: null,
    bindTime: null,
    unbindTime: null,
    bindOperatorAccount: null,
    bindOperatorName: null,
    remark: '备用卡片，暂未分配',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-10 08:00:00',
  },
  {
    id: 4,
    cardNumber: 'CARD004',
    cardStatus: 3,
    employeeId: 1003,
    employeeName: '王五',
    employeeAccount: 'wangwu',
    bindTime: '2024-01-18 11:15:00',
    unbindTime: null,
    bindOperatorAccount: 'admin',
    bindOperatorName: '管理员',
    remark: '卡片损坏，需要更换',
    corpId: 'CORP001',
    createdAt: '2024-01-10 08:00:00',
    updatedAt: '2024-01-18 11:15:00',
  },
  {
    id: 5,
    cardNumber: 'CARD005',
    cardStatus: 1,
    employeeId: 1004,
    employeeName: '赵六',
    employeeAccount: 'zhaoliu',
    bindTime: '2024-01-22 10:00:00',
    unbindTime: null,
    bindOperatorAccount: 'operator1',
    bindOperatorName: '操作员1',
    remark: '新员工卡片',
    corpId: 'CORP001',
    createdAt: '2024-01-20 08:00:00',
    updatedAt: '2024-01-22 10:00:00',
  },
];

let cardIdCounter = 6;

module.exports = {
  // 获取卡片列表
  'POST /card/get_ls': (req, res) => {
    const { page = 1, perPage = 10, cardNumber, cardStatus, employeeName } = req.body;

    let filteredCards = [...mockCards];

    // 卡片编号模糊查询
    if (cardNumber) {
      filteredCards = filteredCards.filter(card =>
        card.cardNumber.toLowerCase().includes(cardNumber.toLowerCase())
      );
    }

    // 卡片状态筛选
    if (cardStatus !== undefined) {
      filteredCards = filteredCards.filter(card => card.cardStatus === cardStatus);
    }

    // 员工姓名模糊查询
    if (employeeName) {
      filteredCards = filteredCards.filter(card =>
        card.employeeName && card.employeeName.includes(employeeName)
      );
    }

    const total = filteredCards.length;
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const items = filteredCards.slice(startIndex, endIndex);

    res.json({
      status: 0,
      msg: 'success',
      data: {
        items,
        total,
        page: parseInt(page),
      },
    });
  },

  // 获取卡片详情
  'GET /card/get_info': (req, res) => {
    const { id } = req.query;
    const card = mockCards.find(item => item.id === parseInt(id));

    if (card) {
      res.json({
        status: 0,
        msg: 'success',
        data: card,
      });
    } else {
      res.json({
        status: 1,
        msg: '卡片不存在',
        data: null,
      });
    }
  },

  // 添加卡片
  'POST /card/post_add': (req, res) => {
    const { cardNumber, cardStatus = 1, employeeId, employeeName, employeeAccount, remark } = req.body;

    // 检查卡片编号是否已存在
    const existingCard = mockCards.find(card => card.cardNumber === cardNumber);
    if (existingCard) {
      res.json({
        status: 1,
        msg: '卡片编号已存在',
        data: null,
      });
      return;
    }

    const newCard = {
      id: cardIdCounter++,
      cardNumber,
      cardStatus,
      employeeId: employeeId || null,
      employeeName: employeeName || null,
      employeeAccount: employeeAccount || null,
      bindTime: employeeId ? new Date().toLocaleString('zh-CN', { hour12: false }) : null,
      unbindTime: null,
      bindOperatorAccount: employeeId ? 'admin' : null,
      bindOperatorName: employeeId ? '管理员' : null,
      remark: remark || null,
      corpId: 'CORP001',
      createdAt: new Date().toLocaleString('zh-CN', { hour12: false }),
      updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
    };

    mockCards.push(newCard);

    res.json({
      status: 0,
      msg: '添加成功',
      data: newCard,
    });
  },

  // 修改卡片
  'POST /card/post_modify': (req, res) => {
    const { id, cardNumber, cardStatus, employeeId, employeeName, employeeAccount, remark } = req.body;

    const cardIndex = mockCards.findIndex(card => card.id === parseInt(id));
    if (cardIndex === -1) {
      res.json({
        status: 1,
        msg: '卡片不存在',
        data: null,
      });
      return;
    }

    // 检查卡片编号是否与其他卡片重复
    const existingCard = mockCards.find(card => card.cardNumber === cardNumber && card.id !== parseInt(id));
    if (existingCard) {
      res.json({
        status: 1,
        msg: '卡片编号已存在',
        data: null,
      });
      return;
    }

    const originalCard = mockCards[cardIndex];
    const updatedCard = {
      ...originalCard,
      cardNumber,
      cardStatus,
      employeeId: employeeId || null,
      employeeName: employeeName || null,
      employeeAccount: employeeAccount || null,
      remark: remark || null,
      updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
    };

    // 如果员工信息发生变化，更新绑定时间
    if (employeeId && (!originalCard.employeeId || originalCard.employeeId !== employeeId)) {
      updatedCard.bindTime = new Date().toLocaleString('zh-CN', { hour12: false });
      updatedCard.bindOperatorAccount = 'admin';
      updatedCard.bindOperatorName = '管理员';
    } else if (!employeeId && originalCard.employeeId) {
      updatedCard.unbindTime = new Date().toLocaleString('zh-CN', { hour12: false });
    }

    mockCards[cardIndex] = updatedCard;

    res.json({
      status: 0,
      msg: '修改成功',
      data: updatedCard,
    });
  },

  // 删除卡片
  'POST /card/post_del': (req, res) => {
    const { id } = req.body;

    const cardIndex = mockCards.findIndex(card => card.id === parseInt(id));
    if (cardIndex === -1) {
      res.json({
        status: 1,
        msg: '卡片不存在',
        data: null,
      });
      return;
    }

    mockCards.splice(cardIndex, 1);

    res.json({
      status: 0,
      msg: '删除成功',
      data: null,
    });
  },

  // 绑定员工
  'POST /card/post_bind': (req, res) => {
    const { id, employeeId, employeeName, employeeAccount } = req.body;

    const cardIndex = mockCards.findIndex(card => card.id === parseInt(id));
    if (cardIndex === -1) {
      res.json({
        status: 1,
        msg: '卡片不存在',
        data: null,
      });
      return;
    }

    const updatedCard = {
      ...mockCards[cardIndex],
      employeeId,
      employeeName,
      employeeAccount,
      bindTime: new Date().toLocaleString('zh-CN', { hour12: false }),
      unbindTime: null,
      bindOperatorAccount: 'admin',
      bindOperatorName: '管理员',
      updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
    };

    mockCards[cardIndex] = updatedCard;

    res.json({
      status: 0,
      msg: '绑定成功',
      data: updatedCard,
    });
  },

  // 解绑员工
  'POST /card/post_unbind': (req, res) => {
    const { id } = req.body;

    const cardIndex = mockCards.findIndex(card => card.id === parseInt(id));
    if (cardIndex === -1) {
      res.json({
        status: 1,
        msg: '卡片不存在',
        data: null,
      });
      return;
    }

    const updatedCard = {
      ...mockCards[cardIndex],
      employeeId: null,
      employeeName: null,
      employeeAccount: null,
      unbindTime: new Date().toLocaleString('zh-CN', { hour12: false }),
      bindOperatorAccount: 'admin',
      bindOperatorName: '管理员',
      updatedAt: new Date().toLocaleString('zh-CN', { hour12: false }),
    };

    mockCards[cardIndex] = updatedCard;

    res.json({
      status: 0,
      msg: '解绑成功',
      data: updatedCard,
    });
  },
};
