import {
  <PERSON><PERSON><PERSON>Outlined,
  LockOutlined,
  MobileOutlined,
  TaobaoOutlined,
  UserOutlined,
  WeiboOutlined,
} from '@ant-design/icons';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';
import { flushSync } from 'react-dom';
import {
  LoginFormPage,
  ProConfigProvider,
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import { QRCode, Flex, Button, Tabs, message, theme } from 'antd';
import type { CSSProperties } from 'react';
import { useState, useRef, useEffect } from 'react';
import loginBackground from '../../../../public/icons/login-background.png'
import tfllogo from '../../../../public/icons/tfllogo.png'
import { getRequest, postRequest } from '@/services/api/api';
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';
import NIM from 'nim-web-sdk-ng';


type LoginType = 'account' | 'qrcode';


const Page = () => {
  const formRef = useRef();
  const [messageApi, contextHolder] = message.useMessage();
  const [loginType, setLoginType] = useState<LoginType>('account');
  const { token } = theme.useToken();
  const { initialState, setInitialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);

  const validateAndGetFormatValue = async () => {
    setLoading(true)
    const values = await formRef.current?.validateFieldsReturnFormatValue?.();
    console.log('校验表单并返回格式化后的所有数据：', values);
    if (!values.account) {
      messageApi.open({
        type: 'error',
        content: '请输入账号!',
      });
    } else if (!values.password) {
      messageApi.open({
        type: 'error',
        content: '请输入密码!',
      });
    } else {
      let publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBKPfRCrkJ367IiaaxUjR0xIyO68pMwPEVWL/OqI78dtFPPW+zQQ1wb7YGk+pMJV2oa64cC5xZCLzPNHV6LaZe4hIxlxrcGed3aB1cLLNz0ujj1OplHD8PA2Hwlcz1bOo6U7VsQA2tYaXS9xGlBGgqNoGD3KHtDNYWiApA8FyJYwIDAQAB'
      let password = encrypt(values.password, publicKey)
      const result = await postRequest('sys/account_login', {
        account: values.account,
        password: password
      });
      const { data, status, msg } = result
      if (status === 0) {
        const { permissions, token, user_info } = data
        
        // 存储 token
        sessionStorage.setItem('token', token);
        sessionStorage.setItem('account', user_info.account);
        sessionStorage.setItem('sign', user_info.sign);
        // 存储用户信息
        const userInfo = {
          name: user_info.name,
          avatar: user_info.profilePic
        };
        sessionStorage.setItem('User', JSON.stringify(userInfo));

        flushSync(() => {
          setInitialState((s) => ({
            ...s,
            currentUser: {
              name: user_info.name,
              avatar: user_info.profilePic,
            },
            permissions,
            token
          }));
        });
        setLoading(false)

        history.push('/home');
      } else {
        setLoading(false)
        messageApi.open({
          type: 'error',
          content: msg,
        });
      }
    }
  };

  const encrypt = (txt, publicKey) => {
    const encryptor = new JSEncrypt();
    encryptor.setPublicKey(publicKey); // 设置公钥
    return encryptor.encrypt(txt); // 对数据进行加密

  }

  useEffect(() => {
    // flushSync(() => {
    //   setInitialState((s) => ({
    //     ...s,
    //     // currentUser: userInfo,
    //     currentUser: {
    //       "name": "管理员",
    //       "avatar": "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
    //     },
    //     permissions: 1
    //   }));
    // });
    // // 走接口保存数据
    // history.push('/assetmana/drilltool');
  }, []);

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 登录
      const msg = await login({ ...values });
      
      if (msg.success) {
        const defaultLoginSuccessMessage = '登录成功！';
        message.success(defaultLoginSuccessMessage);
        
        // 保存用户信息和权限到全局初始状态
        await fetchUserInfo();
        
        // 跳转到登录前的页面或默认页面
        const urlParams = new URL(window.location.href).searchParams;
        history.push(urlParams.get('redirect') || '/');
        return;
      }
      // 如果失败去设置用户错误信息
      console.log(msg);
    } catch (error) {
      const defaultLoginFailureMessage = '登录失败，请重试！';
      message.error(defaultLoginFailureMessage);
    }
  };

  return (
    <>
      {contextHolder}
      <div
        style={{
          backgroundColor: 'white',
          height: '100vh',
        }}
      >
        <LoginFormPage
          formRef={formRef}
          backgroundImageUrl={loginBackground}
          logo={tfllogo}
          // backgroundVideoUrl="https://gw.alipayobjects.com/v/huamei_gcee1x/afts/video/jXRBRK_VAwoAAAAAAAAAAAAAK4eUAQBr"
          title="铁福来云平台"
          containerStyle={{
            backgroundColor: '#111827',
            backdropFilter: 'blur(4px)',
            height: '600px'
          }}
          initialValues={{
            account: '',
            password: '',
          }}
          submitter={{
            render: (props, doms) => {
              return <>
                {loginType === 'account' && <Button
                  type="primary"
                  style={{ width: '100%', height: '40px' }}
                  onClick={validateAndGetFormatValue}
                  // loading={loading}
                >
                  登录
                </Button>}
                {loginType === 'qrcode' && (null)}
              </>
            },
          }}
        >
          <Tabs
            centered
            activeKey={loginType}
            onChange={(activeKey) => setLoginType(activeKey as LoginType)}
            style={{ marginTop: '54px' }}
          >
            <Tabs.TabPane key={'account'} tab={'手机号密码登录'} />
            {/* <Tabs.TabPane key={'qrcode'} tab={'扫码登录'} /> */}
          </Tabs>
          {loginType === 'account' && (
            <div style={{ marginBottom: '60px' }}>
              <ProFormText
                name="account"
                label='手机号'
                rules={[
                  {
                    required: true,
                    message: '请输入手机号',
                  },
                  {
                    pattern: /^\d{11}$/,
                    message: '请输入11位手机号',
                  },
                  ]}
                fieldProps={{
                  size: 'large',
                  prefix: (
                    <UserOutlined
                      style={{
                        color: token.colorText,
                      }}
                      className={'prefixIcon'}
                    />
                  ),
                }}
                placeholder={'请输入11位手机号'}
              />
              <ProFormText.Password
                name="password"
                label='密码'
                rules={[
                  {
                    required: true,
                    message: '请输入密码',
                  },
                ]}
                fieldProps={{
                  size: 'large',
                  prefix: (
                    <LockOutlined
                      style={{
                        color: token.colorText,
                      }}
                      className={'prefixIcon'}
                    />
                  ),
                  onPressEnter: () => {
                    validateAndGetFormatValue();
                  },
                }}
                placeholder={'请输入密码'}
              />
            </div>
          )}
          {loginType === 'qrcode' && (
            <>
              <Flex vertical='vertical' justify='center' align='center'>
                <div style={{ marginBottom: '16px', fontSize: '14px', marginTop: '20px' }}>钉钉扫码登录</div>
                <QRCode
                  errorLevel="H"
                  value="https://ant.design/"
                  bordered={false}
                  icon="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
                  color='#000'
                  bgColor='#fff'
                  style={{ padding: '5px', borderRadius: '8px' }}
                  size={240}
                />
              </Flex >
            </>
          )}
        </LoginFormPage>
      </div>
    </>
  );
};

export default () => {
  return (
    <ProConfigProvider dark>
      <Page />
    </ProConfigProvider>
  );
};