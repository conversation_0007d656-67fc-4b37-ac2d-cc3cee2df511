/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-02 13:38:56
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import React, { useEffect } from 'react';

import { Chart } from '@antv/g2';
import { postRequest } from '@/services/api/api';
const Pie: React.FC = (number) => {
  const handleEdit = async () => {
    try {
      const result = await postRequest('health/get_device_info',{
        deviceCode:number.number
      });
      const { status, msg, data } = result as any;
      if (status === 0) {
        return data.HealthStatus

      } else {
      }
    } catch (error) {
    }
  };
  useEffect(() => {
    handleEdit().then(res=>{
      if(res){
        console.log(res,'res122222212');
        // 站点健康状况数据
        const stationHealthData = [
          { type: '正常', value: res.normalCount},
          { type: '良好', value: res.goodCount},
          { type: '异常', value: res.abnormalCount},
        ];
        const chart = new Chart({
          container: 'pie',
          // height: 300,
          autoFit: true,
          theme: 'dark',
        });
    
        chart.coordinate({ type: 'theta', innerRadius: 0.6 });
        chart
          .interval()
          .transform({ type: 'stackY' })
          .data(stationHealthData)
          .encode('y', 'value')
          .encode('color', ['type'])
          .scale('color', {
            range: ['#52c41a', '#1677ff', '#ef4444'],
          })
          .style('stroke', '#1f1f1f')
          .style('inset', 1)
          .animate('enter', { type: 'waveIn' })
          .label({
            position: 'outside',
            text: (data : { type: string; value: number }) => `${data.type}: ${data.value}%`,
          })
          .legend('color', { position: 'top', layout: { justifyContent: 'center' },itemMarkerSize:20 })
          .tooltip((data: { type: string; value: number }) => ({
              name: data.type,
              value: `${data.value}%`,
          }));
          
    
        chart.render();
    
        // 清理函数
        return () => {
          chart.destroy();
        };
      }
    })
  }, []);

  return (
    <div id='pie'  style={{ width: '100%', height: '300px' }}></div>
  );
};

export default Pie;
