import { EllipsisOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Breadcrumb, Progress, Button, Select, Tag } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';
import { useModel, history, request, useIntl, Helmet } from '@umijs/max';

const { RangePicker } = DatePicker;

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}


export default () => {
    const ref = useRef();
    const { Option } = Select;
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryType, setQueryType] = useState('0');
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });

    const selectBefore = (
        <Select value={queryType} onChange={(value) => setQueryType(value)} >
            <Option value="0">单个查询</Option>
            <Option value="1">多个查询</Option>
        </Select>
    );


    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '设备序列号',
            dataIndex: 'deviceCode',
            search: false,
        },
        {
            title: '设备名称',
            dataIndex: 'deviceName',
            search: false,
        },
        {
            title: '设备型号',
            dataIndex: 'deviceModel',
            search: false,
        },
        {
            title: '配置状态',
            dataIndex: 'configurationState',
            render: (text, record,) => {
                const options = [
                    {
                        label: '启用',
                        color: 'blue'
                    },
                    {
                        label: '禁用',
                        color: 'red'
                    },
                ]
                const field = options[text]
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
            search: false,
        },
        {
            title: '当前状态',
            dataIndex: 'currentState',
            render: (text, record,) => {
                const options = [
                    {
                        label: '打开',
                        color: 'blue'
                    },
                    {
                        label: '关闭',
                        color: ''
                    },
                    {
                        label: '损坏',
                        color: 'volcano'
                    },
                ]
                const field = options[text]
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
            search: false,
        },
        {
            title: '设备负责人',
            dataIndex: 'director',
            search: false,
        },
        {
            title: '创建时间',
            dataIndex: 'creatAt',
            search: false,
        },
        {
            title: '最后更新时间',
            dataIndex: 'updateAt',
            search: false,
        },
        {
            title: '操作',
            dataIndex: 'option',
            valueType: 'option',
            render: (text, record,) => [
                <a
                    key="config"
                    onClick={() => {
                        history.push(`/assetmana/device/detail/?id=${record.id}`)
                    }}
                >
                    设备详情
                </a>,
                <a key="subscribeAlert"
                    onClick={() => { }}
                    style={{ marginLeft: '24px' }}
                >
                    删除设备
                </a>,
            ],
        },
        {
            title: '',
            dataIndex: 'deviceCode',
            colSize: 2,
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (
                    <>
                        <Input
                            value={status}
                            onChange={(e) => onchange(e.target.value)}
                            addonBefore={selectBefore}
                            placeholder={queryType === '0' ? '请输入设备序列号' : '支持以英文逗号,分隔组成的多个序列号'}
                        />
                    </>
                );
            },
        },
        {
            title: '',
            dataIndex: 'deviceName',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (<>
                    <Input
                        value={status}
                        onChange={(e) => onchange(e.target.value)}
                        placeholder='请输入设备名称'
                    />
                </>);
            },
        },
        // {
        //     title: '',
        //     dataIndex: 'text3',
        //     hideInTable: true,
        //     renderFormItem: (item, config, form) => {
        //         const label = item.dataIndex
        //         const status = form.getFieldValue(label);
        //         const onchange = (value) => {
        //             form.setFieldsValue({ [label]: value })
        //         }
        //         return (<>
        //             <Select
        //                 value={status}
        //                 onChange={(value) => onchange(value)}
        //                 placeholder='请选择型号'
        //                 options={[
        //                     { value: '1', label: '无鉴权' },
        //                     { value: '2', label: '自定义鉴权' },
        //                 ]}
        //             />
        //         </>);
        //     },
        // },
        {
            title: '',
            dataIndex: 'currentState',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (<>
                    <Select
                        value={status}
                        onChange={(value) => onchange(value)}
                        placeholder='请选择设备状态'
                        options={[
                            { value: 0, label: '打开' },
                            { value: 1, label: '关闭' },
                            { value: 2, label: '损坏' },
                        ]}
                    />
                </>);
            },
        },
        {
            title: '',
            dataIndex: 'sort',
            hideInTable: true,
            renderFormItem: (item, config, form) => {
                const label = item.dataIndex
                const status = form.getFieldValue(label);
                const onchange = (value) => {
                    form.setFieldsValue({ [label]: value })
                }
                return (<>
                    <Select
                        value={status}
                        onChange={(value) => onchange(value)}
                        placeholder='创建时间'
                        options={[
                            { value: 0, label: '最新创建' },
                            { value: 1, label: '最早创建' },
                        ]}
                    />
                </>);
            },
        },
    ];

    const columns1 = [
        {
            title: '变更字段',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '变更前',
            dataIndex: 'age',
            key: 'age',
        },
        {
            title: '变更后',
            dataIndex: 'address',
            key: 'address',
        },
    ];

    const data1 = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];

    useEffect(() => {
    }, []);

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '资产管理', },
                    { title: '设备管理', },
                ]}
            />
            <ProTable<TableListItem>
                headerTitle='设备管理'
                actionRef={ref}
                columns={columns}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('6666666666666', params, sorter, filter);
                    const { current, pageSize, ...rest } = params;
                    let postData = {
                        page: current,
                        perPage: pageSize
                    }
                    const filteredData = {};
                    for (const key in rest) {
                        if (rest.hasOwnProperty(key)) {
                            const value = rest[key];
                            if (typeof value === 'number' && !isNaN(value) || typeof value === 'string' && value.trim() !== '') {
                                filteredData[key] = value;
                            }
                        }
                    }
                    postData = {
                        ...postData,
                        ...filteredData
                    }
                    const result = await postRequest('device/get_ls', postData);
                    const { data, status, msg } = result
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                rowKey="key"
                toolBarRender={() => [
                    <Button
                        key="button"
                        onClick={() => { }}
                        type="primary"
                    >
                        添加设备
                    </Button>,
                ]}
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,
                    span: 3,
                }}
                options={false}
                form={{
                    initialValues: {
                        sort: 0
                    }
                }}
            />
            <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal>
        </>
    );
};