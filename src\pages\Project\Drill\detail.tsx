import React, { useState, useEffect } from 'react';
import { Button, Steps, theme, Descriptions, Breadcrumb,message } from 'antd';
// import {history} from '@umijs/max';
import { useSearchParams } from 'react-router-dom';
import { getRequest,postRequest } from '@/services/api/api';
interface StepItem {
  title: string;
  content: {
    label: string;
    children: string;
  }[];
}

const DrillDetail: React.FC = () => {
  // Move steps state initialization after getInfo sets isConnect
  const [steps, setSteps] = useState<StepItem[]>([]);
  const [current, setCurrent] = useState(null);
  const [messageApi, contextHolder] = message.useMessage();
  // const {location} = history
   // 添加一个状态来保存最大可达步骤
   const [maxAllowedStep, setMaxAllowedStep] = useState<number>(0);
  const [searchParams] = useSearchParams();
  const id = parseInt(searchParams.get('id') || '0', 10);
  const postData = { id: id };
  
  useEffect(() => {
    getInfo()
  }, [id]);
  
  const getInfo = async () => {
    const result = await getRequest('drill/get_info', postData);
    const { status, msg, data } = result as any;
    if (status === 0) {
      // console.log(data,'data');
      setCurrent(data.status)
      setMaxAllowedStep(data.status); 
      
      // Set isConnect value first
      // const connectValue = data.status === 7 ? '连抽' : '封孔'
      // setIsConnect(connectValue);
      
      // Initialize steps after setting isConnect
      initializeSteps(data.status>=7);
      
      // Now get approval data
      getApprovalData(data.status)
    } else {
      messageApi.error(msg);
    }
  }
  
  // Create a separate function to initialize steps with the correct title
  const initializeSteps = (shouldAddWasteStep: boolean) => {
    const stepsArray = [
      {
        title: '下发任务',
        content: [
          {
            label: '审批名称',
            children: '暂无',
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
      {
        title: '开孔申请',
        content: [
          {
            label: '审批名称',
            children: '暂无',
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
      {
        title: '施工过程',
        content: [
          {
            label: '审批名称',
            children: '暂无',
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
      {
        title: '退钻',
        content: [
          {
            label: '审批名称',
            children: '暂无',
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
      {
        title: '封孔',
        content: [
          {
            label: '审批名称',
            children: '暂无',
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
      {
        title: '终孔',
        content: [
          {
            label: '审批名称',
            children: '暂无',
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
      {
        title: '连抽', // Use the passed value here
        content: [
          {
            label: '审批名称',
            children: '暂无'
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      },
    ];
    
    // Add 废孔 step if data.status === 7
    if (shouldAddWasteStep) {
      stepsArray.push({
        title: '废孔',
        content: [
          {
            label: '审批名称',
            children: '暂无'
          },
          {
            label: '提交人员',
            children: '暂无',
          },
          {
            label: '审批人',
            children: '暂无',
          },
          {
            label: '审批状态',
            children: '暂无',
          },
          {
            label: '创建时间',
            children: '暂无',
          },
        ],
      });
    }
    
    setSteps(stepsArray);
  }
  // 查询具体环节审批数据
  const getApprovalData = async (op) => {
    try {
      const result = await postRequest('drill/get_approve', {
        taskId: id,
        status: op  // 使用 op 而不是 current
      });
      const { status, msg, data } = result as any;
      if (status === 0) {
        const newSteps = [...steps];
        newSteps[op].content.forEach(item => {
          // 使用可选链操作符和空值合并操作符来安全地访问数据
          if(item.label==='审批名称'){
            item.children = data?.title ?? '暂无'
          }
          if(item.label==='提交人员'){
            item.children = data?.submitter ?? '暂无'
          }
          if(item.label==='审批人'){
            item.children = data?.approver ?? '暂无'
          }
          if(item.label==='审批状态'){
            // 根据状态值显示不同颜色的文本
            const statusText = data?.status ?? '暂无';
            let coloredText = statusText;
            
            // 根据状态值设置不同的颜色
            if (data?.status === 0) {
              coloredText = <span style={{ color: '#faad14' }}>{'待审批'}</span>;
            } else if (data?.status === 1) {
              coloredText = <span style={{ color: '#52c41a' }}>{'已同意'}</span>;
            } else if (data?.status === 2) {
              coloredText = <span style={{ color: '#f5222d' }}>{'已拒绝'}</span>;
            }
            
            item.children = coloredText;
          }
          if(item.label==='创建时间'){
            item.children = data?.createdAt ?? '暂无'
          }
        });        
        setSteps(newSteps);
      } else {
        messageApi.error(msg || '获取数据失败');
      }
    } catch (error) {
      console.error('获取审批数据失败:', error);
      messageApi.error('获取审批数据失败');
    }
  }
  const next = () => {
    // setCurrent((prev) => prev + 1);
    if (current < maxAllowedStep) {
      setCurrent((prev) => prev + 1);
      // console.log(current,'prevprevprevprevprevprevprev');
      getApprovalData(current+1)
    }
    
  };

  const prev = () => {
    setCurrent((prev) => prev - 1);
    // console.log(current,'prevprevprevprevprevprevprev');
    getApprovalData(current-1)
  };

  const items = steps.map((item) => ({ key: item.title, title: item.title }));

  const containerStyle = {
    background: '#141414',
    height: '100%',
    padding: '20px',
  };

  const descriptionStyle = {
    textAlign: 'center' as const,
    width: '45%',
    margin: '100px auto',
  };

  return (
    <>
      <Breadcrumb
        style={{ margin: '20px' }}
        items={[
          { title: '首页' },
          { title: '施工管理', },
          { title: '打孔管理' },
          { title: '打孔详情' },
        ]}
      />
      <div style={containerStyle}>
        <span style={{ fontSize: '20px' }}>打孔过程</span>
        <Steps current={current} items={items} style={{ padding: '70px' }} />
        <div style={descriptionStyle}>
          <Descriptions
            bordered
            labelStyle={{ width: '150px' }}
            contentStyle={{ width: '250px' }} 
            column={1}
            items={steps[current]?.content.map(item => ({
              key: item.label,
              label: item.label,
              children: item.children
            })) || []}
          />
        </div>
        <div style={{ textAlign: 'center' }}>
          {current > 0 && (
            <Button style={{ margin: '0 8px' }} onClick={prev}>
              上一步
            </Button>
          )}
          {current !== null && current < steps.length - 1 && (
            <Button type="primary" onClick={next} disabled={current >= maxAllowedStep}  >
              下一步
            </Button>
          )}
        </div>
      </div>
    </>
  );
};
export default DrillDetail;