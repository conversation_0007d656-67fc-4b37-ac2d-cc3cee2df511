/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-31 13:58:05
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import React, { useEffect } from 'react';

import { Chart } from '@antv/g2';
interface LineProps {
  data: [];
}
const Line: React.FC<LineProps> = ({ data }) => {
  useEffect(() => {
    const res = Object.entries(data).map(([key, value]) => ({
      time: key,
      value: value,
      title: '运行时长',
    }));
    if (res) {
      const chart = new Chart({
        container: 'line',
        // height: 300,
        // width: 500,
        autoFit: true,
        theme: 'dark',
      });

      // chart.coordinate({ type: 'theta', innerRadius: 0.6 });
      chart
        .data(res)
        .encode('x', 'time')
        .encode('y', 'value')
        .encode('color', 'title')
        .legend('color', false)
        // .encode('y', 'value', { tooltip: false })
        .scale('x', {
          range: [0, 1],
        })
        .scale('y', {
          domainMin: 0,
          nice: true,
        })
        .axis('x', { title: false }) // 隐藏横坐标轴标题
        .axis('y', { title: false }) // 
        .area()
        .style('fill', 'l(270) 0:#1890ff00 0.5:#1890ff33 1:#1890ff66')
        .tooltip(false)
      // .tooltip((data) => ({
      //   name: '运行时长',
      //   value: data.value,
      // }));

      chart.line().label({
        text: '',
        style: {
          dx: -10,
          dy: -12,
        },
      });
      chart.render();

      // 清理函数
      return () => {
        chart.destroy();
      };

    }
  }, [data]);

  return (
    <div id='line' style={{ width: '100%', height: '300px' }}></div>
  );
};

export default Line;
