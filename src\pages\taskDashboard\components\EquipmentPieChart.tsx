/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\EquipmentPieChart.tsx
 * @Description: 设备投入产出比饼图组件 - 使用G2饼图展示设备状态分布，包含钻机利用率和单机效率指标
 */

import { Card, Empty } from 'antd';
import { PieChartOutlined } from '@ant-design/icons';
import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';
import styles from '../newTaskDashboard.less';

interface EquipmentStatusData {
  status: string;
  count: number;
  color: string;
}

interface EquipmentPieChartProps {
  // 设备状态数据
  equipmentStatusData?: EquipmentStatusData[];
  // 钻机利用率
  utilizationRate?: number;
  // 单机效率
  efficiency?: number;
}

const EquipmentPieChart: React.FC<EquipmentPieChartProps> = ({
  equipmentStatusData,
  utilizationRate = 0,
  efficiency = 0,
}) => {
  const equipmentPieRef = useRef<HTMLDivElement>(null);

  // 默认设备状态数据 - 使用0值，字段映射：运行=openNum，停机=closeNum，维修=eroNum
  const defaultEquipmentStatusData = [
    { status: '运行', count: 0, color: '#1E40AF' },     // openNum - 设备打开状态数量
    { status: '停机', count: 0, color: '#C2C2C2' },     // closeNum - 设备关闭状态数量
    { status: '维修', count: 0, color: '#FF9200' },     // eroNum - 设备损坏状态数量
  ];

  const chartData = equipmentStatusData || defaultEquipmentStatusData;

  // 判断是否有有效数据
  const hasValidData = chartData.some(item => item.count > 0);

  // 渲染设备状态饼图
  const renderEquipmentPieChart = () => {
    if (!equipmentPieRef.current) return;

    const chart = new Chart({
      container: equipmentPieRef.current,
      autoFit: true,
      height: 280,
      theme: 'dark',
    });

    // 中空饼图 - 使用G2内置交互
    chart
      .coordinate({ type: 'theta', innerRadius: 0.6 })
      .interval()
      .data(chartData)
      .encode('y', 'count')
      .encode('color', 'status')
      .scale('color', { range: ['#1E40AF', '#FF9200', '#C2C2C2'] })
      .style({
        stroke: '#141414',
        lineWidth: 2,
      })
      .legend({
        color: {
          position: 'bottom',
          layout: { justifyContent: 'center' },
          itemLabelFill: '#FFFFFF',
          itemLabelFontSize: 12,
          itemMarkerSymbol: 'circle',
          itemMarkerSize: 12,
          itemSpacing: 20,
        },
      })
      .interaction('legendFilter') // 启用图例过滤交互
      .axis(false);

    chart.render();
    return () => chart.destroy();
  };

  useEffect(() => {
    const cleanup = renderEquipmentPieChart();
    return cleanup;
  }, [chartData]);

  return (
    <Card
      title="设备投入产出比"
      bordered={false}
      className={styles.chartCard}
      headStyle={{
        fontSize: '22px',
        fontWeight: 'normal',
        fontFamily: 'PingFang SC',
        color: '#ffffff',
      }}
    >
      <div style={{ paddingTop: '50px', position: 'relative' }}>
        {hasValidData ? (
          <>
            {/* 左上角钻机利用率 */}
            <div
              style={{
                position: 'absolute',
                top: '30px',
                left: '30px',
                textAlign: 'center',
              }}
            >
              <div style={{ color: '#ffffff', fontSize: '14px' }}>钻机利用率</div>
              <div style={{ color: '#1E40AF', fontSize: '20px', fontWeight: 'bold' }}>
                {utilizationRate}%
              </div>
            </div>

            {/* 右上角单机效率 */}
            <div
              style={{
                position: 'absolute',
                top: '30px',
                right: '0px',
                textAlign: 'center',
              }}
            >
              <div style={{ color: '#ffffff', fontSize: '14px' }}>单机效率</div>
              <div style={{ color: '#FF5219', fontSize: '20px', fontWeight: 'bold' }}>
                {efficiency} 米/台
              </div>
            </div>

            {/* G2饼图容器 - 现在使用内置图例交互 */}
            <div ref={equipmentPieRef} style={{ height: '350px' }}></div>
          </>
        ) : (
          /* 无数据状态 */
          <div style={{ height: '350px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Empty
              image={<PieChartOutlined style={{ fontSize: 48, color: '#666' }} />}
              description={
                <span style={{ color: '#999', fontSize: '14px' }}>
                  暂无设备状态数据
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default EquipmentPieChart;
