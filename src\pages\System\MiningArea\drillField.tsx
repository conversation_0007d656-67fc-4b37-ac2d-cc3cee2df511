import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { Input, DatePicker, message, Breadcrumb, <PERSON><PERSON>, Modal, Drawer, Form, Space, Select } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import React, { useRef, useState, useEffect } from 'react';
import { postRequest, getRequest } from '@/services/api/api';

const { RangePicker } = DatePicker;

export type TableListItem = {
    id: number;
    noodlesIds: string | number;
    lanewayIds: string | number;
    name: string;
    status: number;
    length: string;
    width: string;
    thickness: string;
    method: string;
    yield: string;
    startTime: string;
    endTime: string;
    responsiblePerson: string;
    remark: string;
    createdAt: string;
    updateAt: string;
    corpId: string;
    lanewayName?: string;
    noodlesName?: string;
};

interface ApiResponse {
    data: {
        items: TableListItem[];
        total: number;
    };
    status: number;
    msg: string;
}

interface NoodlesItem {
    id: number;
    name: string;
    [key: string]: any;
}

interface NoodlesResponse {
    data: NoodlesItem[];
    status: number;
    msg: string;
}

interface LanewayItem {
    id: number;
    name: string;
    [key: string]: any;
}

interface LanewayResponse {
    data: {
        items: LanewayItem[];
        total: number;
    };
    status: number;0
    msg: string;
}

export default () => {
    const ref = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [currentRecord, setCurrentRecord] = useState<TableListItem | null>(null);
    const [form] = Form.useForm();
    const [noodlesList, setNoodlesList] = useState<{ label: string; value: number }[]>([]);
    const [lanewayList, setLanewayList] = useState<{ label: string; value: number }[]>([]);
    const [selectedNoodles, setSelectedNoodles] = useState<number>();

    // 获取采面列表
    const fetchNoodlesList = async () => {
        try {
            const result = await getRequest('noodles/get_all') as NoodlesResponse;
            if (result.status === 0 && result.data) {
                const options = result.data.map((item: NoodlesItem) => ({
                    label: item.name,
                    value: item.id
                }));
                setNoodlesList(options);
            }
        } catch (error) {
            messageApi.error('获取采面列表失败');
        }
    };

    // 获取巷道列表
    const fetchLanewayList = async (parentId: number) => {
        try {
            const result = await postRequest('laneway/get_ls', {
                perPage: 999,
                parentId
            }) as LanewayResponse;
            if (result.status === 0 && result.data) {
                const options = result.data.items.map((item: LanewayItem) => ({
                    label: item.name,
                    value: item.id
                }));
                setLanewayList(options);
            }
        } catch (error) {
            messageApi.error('获取巷道列表失败');
        }
    };

    const showDrawer = async (record?: TableListItem) => {
        if (record) {
            setCurrentRecord(record);
            // 先获取采面列表
            await fetchNoodlesList();
            // 如果有采面ID，获取对应的巷道列表
            if (record.noodlesIds) {
                setSelectedNoodles(Number(record.noodlesIds));
                await fetchLanewayList(Number(record.noodlesIds));
                // 设置表单值
                form.setFieldsValue({
                    name: record.name,
                    noodlesIds: Number(record.noodlesIds),
                    lanewayIds: Number(record.lanewayIds)
                });
            }
            
        } else {
            setCurrentRecord(null);
            form.resetFields();
        }
        setDrawerVisible(true);
    };

    const onClose = () => {
        form.resetFields();
        setCurrentRecord(null);
        setDrawerVisible(false);
    };

    const onFinish = async (values: any) => {
        try {
            const url = currentRecord ? 'drillsite/post_modify' : 'drillsite/post_add';
            const postData = currentRecord ? { ...values, id: currentRecord.id } : values;

            const result = await postRequest(url, postData);
            const { status, msg } = result as any;
            if (status === 0) {
                messageApi.success(currentRecord ? '修改成功' : '添加成功');
                onClose();
                ref.current?.reload();
            } else {
                messageApi.error(msg || (currentRecord ? '修改失败' : '添加失败'));
            }
        } catch (error) {
            messageApi.error(currentRecord ? '修改失败' : '添加失败');
        }
    };

    const handleDelete = (record: any) => {
        Modal.confirm({
            title: '确认删除',
            content: '确定要删除该钻场吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: async () => {
                try {
                    const result = await getRequest('drillsite/post_del', {
                        id: record.id
                    });
                    const { status, msg } = result as any;
                    if (status === 0) {
                        messageApi.success('删除成功');
                        ref.current?.reload();
                    } else {
                        messageApi.error(msg || '删除失败');
                    }
                } catch (error) {
                    messageApi.error('删除失败');
                }
            }
        });
    };

    useEffect(() => {
        if (drawerVisible) {
            fetchNoodlesList();
        }
    }, [drawerVisible]);

    // 当选择采面时，获取对应的巷道列表
    const handleNoodlesChange = (value: number) => {
        setSelectedNoodles(value);
        form.setFieldValue('lanewayIds', undefined); // 清空巷道选择
        if (value) {
            fetchLanewayList(value);
        } else {
            setLanewayList([]);
        }
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '钻场编号',
            dataIndex: 'drillsiteId',
            search: false,
            width: 120,
        },
        {
            title: '钻场名称',
            dataIndex: 'name',
            search: false,
            width: 150,
        },
        {
            title: '巷道',
            search: false,
            width: 150,
            render: (_, record) => {
                return record.noodlesName + '_' + record.lanewayName
            }
        },
        {
            title: '负责人',
            dataIndex: 'responsiblePerson',
            search: false,
            width: 120,
        },
        {
            title: '操作时间',
            dataIndex: 'createdAt',
            search: false,
            width: 180,
        },
        {
            title: '操作',
            key: 'option',
            width: 150,
            search: false,
            render: (_, record) => [
                <Button
                    key="edit"
                    type="link"
                    onClick={() => showDrawer(record)}
                >
                    修改
                </Button>,
                <Button
                    key="delete"
                    type="link"
                    onClick={() => handleDelete(record)}
                >
                    删除
                </Button>
            ],
        },
        {
            title: '',
            dataIndex: 'keyword',
            colSize: 1,
            hideInTable: true,
            renderFormItem: () => {
                return (
                    <Input
                        placeholder='请输入关键词'
                        style={{ width: '100%' }}
                    />
                );
            },
        },
        {
            title: '',
            dataIndex: 'dateRange',
            colSize: 1,
            hideInTable: true,
            search: {
                transform: (value: any) => {
                    if (value && Array.isArray(value) && value.length === 2) {
                        return {
                            dateStart: value[0],
                            dateEnd: value[1]
                        };
                    }
                    return {};
                }
            },
            renderFormItem: () => {
                return (
                    <RangePicker
                        placeholder={['开始日期', '结束日期']}
                        style={{ width: '100%' }}
                        format="YYYY-MM-DD"
                        showTime={false}
                    />
                );
            },
        },
    ];

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '矿区概览', },
                    { title: '钻场管理', },
                ]}
            />
            <ProTable<TableListItem>
                style={{ marginTop: '16px' }}
                headerTitle='钻场管理'
                actionRef={ref}
                columns={columns}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('params:', params, sorter, filter);
                    const { current, pageSize, keyword, dateStart, dateEnd } = params;
                    const postData = {
                        page: current,
                        perPage: pageSize,
                        keyWord: keyword || undefined,
                        dateStart: dateStart || undefined,
                        dateEnd: dateEnd || undefined
                    }
                    const result = await postRequest('drillsite/get_ls', postData);
                    const { data, status, msg } = result as ApiResponse;
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                rowKey="id"
                toolBarRender={() => [
                    <Button
                        key="button"
                        onClick={() => showDrawer()}
                        type="primary"
                    >
                        添加钻场
                    </Button>,
                ]}
                search={{
                    defaultCollapsed: false,
                    labelWidth: 0,
                    span: 6,
                }}
                form={{
                    initialValues: {
                        sort: 0
                    }
                }}
            />
            <Drawer
                title={
                    <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        width: '100%'
                    }}>
                        <span>{currentRecord ? '修改钻场' : '添加钻场'}</span>
                        <Button
                            type="text"
                            onClick={onClose}
                            icon={<CloseOutlined />}
                            style={{ color: 'rgba(255, 255, 255, 0.45)' }}
                        />
                    </div>
                }
                width={720}
                onClose={onClose}
                open={drawerVisible}
                bodyStyle={{ paddingBottom: 80 }}
                closeIcon={null}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={onFinish}
                >
                    <Form.Item
                        name="name"
                        label="钻场名称"
                        rules={[
                            {
                                required: true,
                                message: '请输入钻场名称',
                            },
                        ]}
                    >
                        <Input placeholder="请输入钻场名称" />
                    </Form.Item>
                    <Form.Item
                        name="noodlesIds"
                        label="归属采面"
                        rules={[
                            {
                                required: true,
                                message: '请选择归属采面',
                            },
                        ]}
                    >
                        <Select
                            placeholder="请选择归属采面"
                            options={noodlesList}
                            onChange={handleNoodlesChange}
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                    <Form.Item
                        name="lanewayIds"
                        label="归属巷道"
                        rules={[
                            {
                                required: true,
                                message: '请选择归属巷道',
                            },
                        ]}
                    >
                        <Select
                            placeholder="请选择归属巷道"
                            options={lanewayList}
                            disabled={!selectedNoodles}
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                </Form>
                <div style={{
                    position: 'absolute',
                    bottom: 0,
                    width: '100%',
                    borderTop: '1px solid rgba(255, 255, 255, 0.08)',
                    padding: '10px 16px',
                    textAlign: 'right',
                    left: 0,
                    background: '#1f1f1f',
                    borderRadius: '0 0 2px 2px',
                }}>
                    <Space>
                        <Button onClick={onClose}>取消</Button>
                        <Button type="primary" onClick={() => form.submit()}>
                            提交
                        </Button>
                    </Space>
                </div>
            </Drawer>
        </>
    );
};
