import React from 'react';
import { Button, Modal, message,Input,Select,DatePicker,Tag } from 'antd';
import { history } from 'umi';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest,getRequest } from '@/services/api/api';
import { setTempData } from '@/utils/storage';
import { ExclamationCircleFilled } from '@ant-design/icons';
const { RangePicker } = DatePicker;


// 定义表格数据类型
interface TableListItem {
  id: number;
  taskNumber: string;
  taskName: string;
  description: string;
  responsibleTeam: string;
  taskDate: string;
  startTime:string;
  endTime:string;
  constructionRequirements:string;
  constructionDrawings:string;
  taskPerson:string;
  taskRecipient:string;
  receiptDate:string;
  remark:string;
}
interface ApiResponse<T = any> {
    status: number;
    msg?: string;
    data?: T;
    total?: number;
  }
  
  
const Task: React.FC = () => {
  const actionRef = React.useRef<ActionType>();
  const [messageApi, contextHolder] = message.useMessage();
  
  // 定义表格列
  const columns: ProColumns<TableListItem>[] = [
   
    {
      title: '任务下发编号',
      dataIndex: 'taskNumber',
      width:160,
      ellipsis: true,
      search: false,
    },
    {
      title: '施工任务名称',
      dataIndex: 'taskName',
      width:160,
      ellipsis: true,
      search: false,
    },
    {
      title: '施工任务描述',
      width:170,
      dataIndex: 'description',
      ellipsis: true,
      search: false,
    },
    {
      title: '责任施工队/个人',
      dataIndex: 'responsibleTeam',
      width:160,
      ellipsis: true,
      search: false,
    },
    {
        title: '任务下发日期',
        dataIndex: 'taskDate',
        width:160,
        ellipsis: true,
        search: false,
    },
    {
        title: '计划开始日期',
        dataIndex: 'startTime',
        width:160,
        ellipsis: true,
        search: false,
    },
    {
        title: '计划完成日期',
        dataIndex: 'endTime',
        width:160,
        ellipsis: true,
        search: false,
    },
    {
        title: '施工要求',
        dataIndex: 'constructionRequirements',
        width:300,
        ellipsis: true,
        search: false,
    },
    {
        title: '施工图纸',
        dataIndex: 'constructionDrawings',
        width:170,
        ellipsis: true,
        search: false,
    },
    {
        title: '任务下发人',
        dataIndex: 'taskPerson',
        width:150,
        ellipsis: true,
        search: false,
       
    },
    {
        title: '任务接受人',
        dataIndex: 'taskRecipient',
        width:120,
        ellipsis: true,
        search: false,
    },
    {
        title: '接受确认日期',
        dataIndex: 'receiptDate',
        width:160,
        ellipsis: true,
        search: false,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      search: false,
      width:120,
      render: (text, record) => {
          const options = [
              {
                  value: 0,
                  label: '待完成',
                  color: 'red',
              },
              {
                  value: 1,
                  label: '已完成',
                  color: 'green',
              },
          ]
          const statusValue = Number(text) || 0;
          const field = options.find(opt => opt.value === statusValue) || { 
              label: '未知状态', 
              color: 'gray' 
          };
          return (
              <Tag color={field?.color}>
                  {field?.label}
              </Tag>
          )
      },
        
  },
    {
        title: '备注',
        dataIndex: 'remark',
        width:300,
        ellipsis: true,
        search: false,
    },
    {
      title: '',
      dataIndex: 'keyWord',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
          const label = item.dataIndex
          const status = form.getFieldValue(label);
          const onchange = (value) => {
              form.setFieldsValue({ [label]: value })
          }
          return (<>
              <Input
                  value={status}
                  onChange={(e) => onchange(e.target.value)}
                  placeholder='请输入施工任务名称'
              />
          </>);
      },
  },

  {
      title: '',
      dataIndex: 'dateRange',
      hideInTable: true,
      renderFormItem: (item, config, form) => {
          const label = item.dataIndex
          const status = form.getFieldValue(label);
          const onchange = (value) => {
              form.setFieldsValue({ [label]: value })
          }
          return (<>
              <RangePicker
                  value={status}
                  style={{width:'300px'}}
                  onChange={(value) => onchange(value)}
                  picker="date"
                  format="YYYY-MM-DD"
              />
          </>);
      },
  },
  ];

  return (
    <>
    {contextHolder}
    <ProTable<TableListItem>
      headerTitle="施工计划表"
      actionRef={actionRef}
      rowKey="id"
      search={{
        defaultCollapsed: false,
        labelWidth: 0,
        span: 4,
      }}
        request={async (params, sorter, filter) => {

        const { current, pageSize, keyWord, dateRange, ...rest } = params;
                    let postData = {
                        page: current,
                        perPage: pageSize,
                        // keyWord: keyWord,
                        // dateStart: dateRange?.[0],
                        // dateEnd: dateRange?.[1],
                    }
        const result = await postRequest('task/get_ls', postData);
        const { data, status, msg } = result
        let dataSource
        let total
        if (status === 0) {
            console.log('施工计划表',data);
            
            dataSource = data.items
            total = data.total
        } else {
            messageApi.open({
                type: 'error',
                content: msg,
            });
        }
        return Promise.resolve({
            data: dataSource,
            total: total,
            success: true,
        });
    }}
      toolBarRender={() => [
        <Button
          type="primary"
          key="add"
          onClick={() => {
            history.push('/project/task/add')
          }}
        >
          添加下发计划
        </Button>,
      ]}
      scroll={{ x: 1300 }}
      columns={columns}
    //   options={false}
     
    />
   
    </>
  );
  
};

export default Task;
