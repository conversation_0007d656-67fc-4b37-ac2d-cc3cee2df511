/**
 * 静态IP管理API服务
 */
import { getRequest, postRequest } from '@/services/api/api';
import { StaticIPItem, StaticIPFormData, StaticIPListParams, ApiResponse, StaticIPListResponse } from './types';

// 静态IP管理API服务
export const staticIPService = {
  /**
   * 获取静态IP列表
   * @param params 查询参数
   * @returns Promise<ApiResponse<StaticIPListResponse>>
   */
  async getList(params: StaticIPListParams = {}): Promise<ApiResponse<StaticIPListResponse>> {
    try {
      const { page = 1, perPage = 10, ipAddress, deviceCode } = params;
      
      const requestParams: any = {
        page,
        perPage,
      };
      
      // 添加IP地址模糊查询参数
      if (ipAddress) {
        requestParams.ipAddress = ipAddress;
      }
      
      // 添加设备编码模糊查询参数
      if (deviceCode) {
        requestParams.deviceCode = deviceCode;
      }
      
      const response = await postRequest<ApiResponse<StaticIPListResponse>>('/staticip/get_ls', requestParams);
      return response;
    } catch (error) {
      console.error('获取静态IP列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取静态IP详情
   * @param id 静态IP ID
   * @returns Promise<ApiResponse<StaticIPItem>>
   */
  async getDetail(id: number): Promise<ApiResponse<StaticIPItem>> {
    try {
      const response = await getRequest<ApiResponse<StaticIPItem>>('/staticip/get_info', { id });
      return response;
    } catch (error) {
      console.error('获取静态IP详情失败:', error);
      throw error;
    }
  },

  /**
   * 添加静态IP
   * @param formData 表单数据
   * @returns Promise<ApiResponse<StaticIPItem>>
   */
  async add(formData: StaticIPFormData): Promise<ApiResponse<StaticIPItem>> {
    try {
      const response = await postRequest<ApiResponse<StaticIPItem>>('/staticip/post_add', formData);
      return response;
    } catch (error) {
      console.error('添加静态IP失败:', error);
      throw error;
    }
  },

  /**
   * 修改静态IP
   * @param id 静态IP ID
   * @param formData 表单数据
   * @returns Promise<ApiResponse<StaticIPItem>>
   */
  async update(id: number, formData: StaticIPFormData): Promise<ApiResponse<StaticIPItem>> {
    try {
      const requestData = {
        id,
        ...formData,
      };
      const response = await postRequest<ApiResponse<StaticIPItem>>('/staticip/post_modify', requestData);
      return response;
    } catch (error) {
      console.error('修改静态IP失败:', error);
      throw error;
    }
  },

  /**
   * 删除静态IP
   * @param id 静态IP ID
   * @returns Promise<ApiResponse<null>>
   */
  async delete(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await postRequest<ApiResponse<null>>('/staticip/post_del', { id });
      return response;
    } catch (error) {
      console.error('删除静态IP失败:', error);
      throw error;
    }
  },
};
