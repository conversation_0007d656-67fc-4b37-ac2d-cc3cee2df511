/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-03 09:22:21
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-04-02 13:37:02
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\index.tsx
 * @Description: 任务看板页面，展示系统运行状态和异常数据
 */

import {Card, Col,Row,Segmented} from 'antd';
import React,{useState} from 'react';
import Pie from './component/pie';
import Column from './component/column';
// 异常数据类型定义
const TaskDashboard: React.FC<{ number: string }> = ({ number }) => {
  const [segmentedValue, setSegmentedValue] = useState('部件时长统计');
  // 状态定义
  return (
    <div>
      <div style={{textAlign:'right'}}>
      <Segmented<string>
        options={['部件时长统计', '设备健康状况']}
          onChange={(value) => {
            console.log(value); // string
            setSegmentedValue(value);
          }}
       
        />
        </div>
      {/* <Row gutter={[16, 16]}> */}
      {segmentedValue==='部件时长统计'&&
        // <Col>
          <Card title="部件时长统计图" bordered={false} headStyle={{ fontSize: '22px',fontWeight: 'normal',fontFamily: 'PingFang SC',color:'#ffffffd9',border:"none"}} >
          <Column number={number}/>
          </Card>
        // </Col>
      }
      {segmentedValue==='设备健康状况'&&
        // <Col>
          <Card title="设备健康状况" bordered={false}  headStyle={{ fontSize: '22px',fontWeight: 'normal',fontFamily: 'PingFang SC',color:'#ffffffd9',border:"none"}}>
          <Pie number={number}/>
          </Card>
        // </Col>
      }
      {/* </Row> */}
    </div>
  );
};

export default TaskDashboard;
