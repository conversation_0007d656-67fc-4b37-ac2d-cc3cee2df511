﻿/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-02-28 17:55:02
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-25 17:07:01
 * @FilePath: \diy_tfl_pc\config\routes.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 c， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  // {
  //   path: '/welcome',
  //   name: 'welcome',
  //   icon: 'smile',
  //   component: './Welcome',
  // },
  // {
  //   path: '/admin',
  //   name: 'admin',
  //   icon: 'crown',
  //   access: 'canAdmin',
  //   routes: [
  //     {
  //       path: '/admin',
  //       redirect: '/admin/sub-page',
  //     },
  //     {
  //       path: '/admin/sub-page',
  //       name: 'sub-page',
  //       component: './Admin',
  //     },
  //   ],
  // },
  // {
  //   name: 'list.table-list',
  //   icon: 'table',
  //   path: '/list',
  //   component: './TableList',
  // },
  {
    path: '/call',
    name: '呼叫',
    icon: 'phone',
    component: './Call',
    hideInMenu: true,
  },
  {
    name: '首页',
    icon: 'home',
    path: '/home',
    // component: './Home',
    routes: [
      {
        path: '/home',
        redirect: '/home/<USER>',
      },
      // 老的任务看板（已注释）
      // {
      //   name: '任务看板',
      //   icon: 'user',
      //   path: '/home/<USER>',
      //   component: './Home/taskDashboard',
      // },
      // 新的任务看板
      {
        name: '任务看板',
        icon: 'user',
        path: '/home/<USER>',
        component: './taskDashboard/simpleTaskDashboard',
      },
      {
        name: '实时作业',
        icon: 'user',
        path: '/home/<USER>',
        component: './Home/Operation',
      },
    ],
  },
  {
    name: '待办',
    icon: 'schedule',
    path: '/todo',
    routes: [
      {
        path: '/todo',
        component: './Todo',
      },
    ],
  },
  {
    path: '/project',
    name: '施工管理',
    icon: 'build',
    routes: [
      // {
      //   path: '/project/construction',
      //   name: '施工列表',
      //   component: './Project/Construction',
      // },
      // {
      //   path: '/project/task',
      //   name: '施工计划',
      //   component: './Project/Task',
      // },
      {
        path: '/project/deliver',
        name: '下发任务',
        component: './Project/Deliver',
      },
      {
        path: '/project/drill',
        name: '打孔管理',
        component: './Project/Drill',
      },
      {
        path: '/project/drill/add',
        name: '添加打孔计划',
        component: './Project/Drill/add',
        hideInMenu: true,
      },
      {
        path: '/project/drill/detail',
        name: '打孔计划详情',
        component: './Project/Drill/detail',
        hideInMenu: true,
      },
      {
        path: '/project/task/add',
        name: '添加下发计划',
        component: './Project/Task/add',
        hideInMenu: true,
      },
      {
        path: '/project/Deliver/modify',
        name: '下发计划详情',
        component: './Project/Deliver/modify',
        hideInMenu: true,
      },
      {
        path: '/project/construction',
        // redirect: '/project/construction',
        name: '施工记录',
        component: './Project/Construction',
      },
      {
        path: '/project/construction/modify',
        name: '施工记录详情',
        component: './Project/Construction/modify',
        hideInMenu: true,
      },
      {
        path: '/project/threed',
        name: '三维轨迹图',
        component: './Project/ThreeD',
        icon: 'radar-chart',
      },
    ],
  },
  {
    name: '资产管理',
    icon: 'database',
    path: '/assetmana',
    routes: [
      {
        name: '设备管理',
        path: '/assetmana/device',
        component: './AssetMana/Device',
      },
      {
        name: '设备详情',
        path: '/assetmana/device/detail',
        component: './AssetMana/Device/Detail',
        hideInMenu: true,
      },
      {
        name: '设备详情',
        path: '/assetmana/device/detail/historRecord',
        component: './AssetMana/Device/Detail/historRecord',
        hideInMenu: true,
      },
      {
        name: '钻具管理',
        path: '/assetmana/drilltool',
        component: './AssetMana/DrillTool',
      },
      // {
      //   name: '资产分组',
      //   path: '/assetmana/group',
      //   component: './AssetMana/Group',
      // },
    ],
  },
  {
    name: '报表',
    icon: 'ProfileOutlined',
    path: '/report',
    component: './report',
  },
  {
    name: '运维中心',
    icon: 'dashboard',
    path: '/opermain',
    component: './Opermain',
  },
  // {
  //   name: '数据洞察',
  //   icon: 'barChart',
  //   path: '/datainsight',
  //   component: './DataInsight',
  // },
  {
    name: '审计日志',
    icon: 'audit',
    path: '/auditLog',
    component: './AuditLog',
  },
  {
    name: '矿区概览',
    icon: 'cluster',
    path: '/miningArea',
    // component: './System/MiningArea',
    routes: [
      {
        name: '采面管理',
        path: '/miningArea/miningFace',
        component: './System/MiningArea/MiningFace',
      },
      {
        name: '巷道管理',
        path: '/miningArea/Roadway',
        component: './System/MiningArea/roadway',
      },
      {
        name: '钻场管理',
        path: '/miningArea/DrillField',
        component: './System/MiningArea/drillField',
      },
      // {
      //   name: '矿区概览',
      //   path: '/miningArea',
      //   component: './System/MiningArea/component/form',
      //   hideInMenu: true, // 在菜单中隐藏
      // },
      // {
      //   name: '矿区概览-详情',
      //   path: '/system/miningArea/detail',
      //   component: './System/MiningArea/component/detail',
      //   hideInMenu: true, // 在菜单中隐藏
      // },
    ],
  },
  {
    name: '系统设置',
    icon: 'setting',
    path: '/system',
    routes: [
      {
        name: '企业配置',
        path: '/system/enterprise',
        component: './System/Enterprise',
      },
      {
        path: '/system/character',
        name: '权限管理',
        icon: 'crown',
        component: './System/Character',
      },
      {
        path: '/system/character/add',
        name: '添加权限',
        component: './System/Character/add',
        hideInMenu: true,
      },
      {
        path: '/system/character/detail',
        name: '权限详情',
        component: './System/Character/detail',
        hideInMenu: true,
      },
      {
        path: '/system/extension',
        name: '分机号管理',
        icon: 'phone',
        component: './System/Extension',
      },
      {
        path: '/system/extension/add',
        name: '添加分机号',
        component: './System/Extension/add',
        hideInMenu: true,
      },
      {
        path: '/system/extension/edit',
        name: '编辑分机号',
        component: './System/Extension/edit',
        hideInMenu: true,
      },
      {
        path: '/system/staticip',
        name: '静态IP管理',
        icon: 'global',
        component: './System/StaticIP',
      },
      {
        path: '/system/staticip/add',
        name: '添加静态IP',
        component: './System/StaticIP/add',
        hideInMenu: true,
      },
      {
        path: '/system/staticip/edit',
        name: '编辑静态IP',
        component: './System/StaticIP/edit',
        hideInMenu: true,
      },
      {
        path: '/system/cardmanagement',
        name: '卡片管理',
        icon: 'idcard',
        component: './System/CardManagement',
      },
      {
        path: '/system/cardmanagement/add',
        name: '添加卡片',
        component: './System/CardManagement/add',
        hideInMenu: true,
      },
      {
        path: '/system/cardmanagement/edit',
        name: '编辑卡片',
        component: './System/CardManagement/edit',
        hideInMenu: true,
      },

      // {
      //   name: '通知提醒',
      //   path: '/system/notification',
      //   component: './System/Notification',
      // },
    ],
  },
  {
    name: '用户信息',
    icon: 'user',
    path: '/userinfo',
    component: './UserInfo',
    hideInMenu: true,
  },
  // 独立的新任务看板路由（已移动到首页下）
  // {
  //   name: '新任务看板',
  //   icon: 'dashboard',
  //   path: '/newTaskDashboard',
  //   component: './taskDashboard/simpleTaskDashboard',
  // },

  // {
  //   name: '测试',
  //   icon: 'table',
  //   path: '/text',
  //   component: './Text',
  // },
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
