/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-24 10:52:59
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-24 11:14:58
 * @FilePath: \diy_tfl_pc\src\pages\Project\Construction\component\approve.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';
import { postRequest } from '@/services/api/api';
// import { history} from '@umijs/max';

export type TableListItem = {
  key: number;
  name: string;
  containers: number;
  creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
  tableListDataSource.push({
    key: i,
    name: 'AppName',
    containers: Math.floor(Math.random() * 20),
    creator: creators[Math.floor(Math.random() * creators.length)],
  });
}

export default (number: any) => {
  const ref = useRef();
  const [messageApi, contextHolder] = message.useMessage();
  const columns: ProColumns<TableListItem>[] = [
    {
      title: '审批名称',
      dataIndex: 'title',
      search: false,
      width: 300,
    },
    {
      title: '提交人',
      dataIndex: 'submitter',
      search: false,
      width: 150,
    },
    {
      title: '审批人',
      dataIndex: 'approver',
      search: false,
      width: 150,
    },
    {
      title: '审批状态',
      dataIndex: 'status',
      width: 150,
      valueEnum: {
        0: { text: '待审批', status: 'Default' },
        1: { text: '已同意', status: 'Success' },
        2: { text: '已拒绝', status: 'Error' },
      },
    },
    {
      title: '审批时间',
      dataIndex: 'approvalAt',
      search: false,
      width: 150,
    },
    {
      title: '审批类型',
      dataIndex: 'todoType',
      search: false,
      width: 150,
      valueEnum: {
        0: { text: '开孔申请' },
        1: { text: '验孔申请' },
        2: { text: '封孔提醒' },
        3: { text: '完工申请' },
        4: { text: '连抽提醒' },
        5: { text: '终孔提醒' },
        6: { text: '交接班提醒' },
        7: { text: '废孔申请' },
      },
    },
    {
      title: '拒绝原因',
      dataIndex: 'refuse',
      search: false,
      width: 150,
    },
  ];
  return (
    <>
      {contextHolder}
      <ProTable<TableListItem>
        actionRef={ref}
        columns={columns}
        request={async (params, sorter, filter) => {
          const { current, pageSize, ...rest } = params;
          let postData = {
            page: current,
            perPage: pageSize,
            taskId: number.number,
          };
          const result = await postRequest('todo/get_ls', postData);
          const { data, status, msg } = result;
          let dataSource;
          let total;
          if (status === 0) {
            dataSource = data.items;
            total = data.total;
          } else {
            messageApi.open({
              type: 'error',
              content: msg,
            });
          }
          return Promise.resolve({
            data: dataSource,
            total: total,
            success: true,
          });
        }}
        rowKey="key"
        search={false}
        options={false}
      />
    </>
  );
};
