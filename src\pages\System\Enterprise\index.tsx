import React, { useState } from 'react';
import { Breadcrumb, Card } from 'antd';
import Member from './components/Member';
import Department from './components/Department';
import Role from './components/Role';

const App: React.FC = () => {
    const [activeTabKey2, setActiveTabKey2] = useState<string>('member');

    const onTab2Change = (key: string) => {
        setActiveTabKey2(key);
    };

    const tabListNoTitle = [
        {
            key: 'member',
            label: '成员',
        },
        {
            key: 'department',
            label: '部门',
        },
        {
            key: 'role',
            label: '角色',
        },
    ];

    const contentListNoTitle: Record<string, React.ReactNode> = {
        member: <Member onTabChange={onTab2Change} />,
        department: <Department />,
        role: <Role />,
    };

    return (
        <div style={{ height: 'calc(100vh - 72px)' }}>
            <Breadcrumb
                style={{ marginBottom: '16px' }}
                items={[
                    { title: '首页', },
                    { title: '系统设置', },
                    { title: '企业配置', },
                ]}
            />
            <Card
                style={{ height: 'calc(100% - 38px)', overflow: 'hidden' }}
                tabList={tabListNoTitle}
                activeTabKey={activeTabKey2}
                onTabChange={onTab2Change}
                tabProps={{
                    size: 'middle',
                }}
                bordered={false}
                bodyStyle={{
                    padding: 0,
                    height: 'calc(100% - 46px)'
                }}
            >
                {contentListNoTitle[activeTabKey2]}
            </Card>
        </div>
    );
};

export default App;