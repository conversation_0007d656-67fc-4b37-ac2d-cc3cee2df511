import { EllipsisOutlined } from '@ant-design/icons';
import {
    LightFilter,
    ProFormDatePicker,
    ProTable,
} from '@ant-design/pro-components';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Input, DatePicker, Flex, Modal, Table, message, Breadcrumb, Progress, Tag } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { getRequest, postRequest } from '@/services/api/api';

const { RangePicker } = DatePicker;

export type TableListItem = {
    key: number;
    name: string;
    containers: number;
    creator: string;
};
const tableListDataSource: TableListItem[] = [];

const creators = ['付小小', '曲丽丽', '林东东', '陈帅帅', '兼某某'];

for (let i = 0; i < 10; i += 1) {
    tableListDataSource.push({
        key: i,
        name: 'AppName',
        containers: Math.floor(Math.random() * 20),
        creator: creators[Math.floor(Math.random() * creators.length)],
    });
}

const ProcessMap = {
    close: 'normal',
    running: 'active',
    online: 'success',
    error: 'exception',
} as const;


export default () => {
    const ref = useRef();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [title, setTitle] = useState('操作日志');
    const [queryForm, setQueryForm] = useState({
        keyWord: '',
        begin: '',
        end: '',
        timer: undefined
    });


    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '钻具序列号',
            dataIndex: 'serialNum',
        },
        {
            title: '钻具名称',
            dataIndex: 'title',
        },
        {
            title: '钻具类型',
            dataIndex: 'model',
        },
        {
            title: '规格型号',
            dataIndex: 'specs',
        },
        {
            title: '制造日期',
            dataIndex: 'productionTime',
            width: 150,
        },
        {
            title: '购置日期',
            dataIndex: 'purchaseTime',
            width: 150,
        },
        {
            title: '使用寿命',
            dataIndex: 'lifespan',
            render: (_, record) => {
                const { usageCount, lifespan } = record
                const percentage = (usageCount / lifespan) * 100;
                return (<>
                    <Progress percent={Math.round(percentage)} />
                </>)
            }
        },
        {
            title: '使用状态',
            dataIndex: 'status',
            render: (text, record,) => {
                const options = [
                    {
                        label: '未使用',
                        color: ''
                    },
                    {
                        label: '使用中',
                        color: 'blue'
                    },
                ]
                const field = options[text]
                return (
                    <Tag color={field.color}>
                        {field.label}
                    </Tag>
                )
            },
        },
        {
            title: '上次使用日期',
            dataIndex: 'lastUsedTime',
            width: 150,
        },
        {
            title: '下次维护日期',
            dataIndex: 'nextRepaireTime',
            width: 150,
        },
        {
            title: '备注',
            dataIndex: 'description',
            ellipsis: true,
        },
    ];

    const columns1 = [
        {
            title: '变更字段',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '变更前',
            dataIndex: 'age',
            key: 'age',
        },
        {
            title: '变更后',
            dataIndex: 'address',
            key: 'address',
        },
    ];

    const data1 = [
        {
            key: '1',
            name: 'John Brown',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];

    useEffect(() => {
    }, []);

    return (
        <>
            {contextHolder}
            <Breadcrumb
                items={[
                    { title: '首页', },
                    { title: '资产管理', },
                    { title: '钻具管理', },
                ]}
            />
            <ProTable<TableListItem>
                style={{ marginTop: '24px' }}
                actionRef={ref}
                columns={columns}
                request={async (params, sorter, filter) => {
                    // 表单搜索项会从 params 传入，传递给后端接口。
                    console.log('6666666666666', params, sorter, filter, queryForm);
                    let postData = {
                        page: params.current,
                        perPage: params.pageSize
                    }
                    const filteredData = {};
                    for (const key in queryForm) {
                        if (queryForm.hasOwnProperty(key)) {
                            const value = queryForm[key];
                            if (typeof value === 'number' && !isNaN(value) || typeof value === 'string' && value.trim() !== '') {
                                filteredData[key] = value;
                            }
                        }
                    }
                    postData = {
                        ...postData,
                        ...filteredData
                    }
                    const result = await postRequest('rod/get_ls', postData);
                    const { data, status, msg } = result
                    let dataSource
                    let total
                    if (status === 0) {
                        dataSource = data.items
                        total = data.total
                    } else {
                        messageApi.open({
                            type: 'error',
                            content: msg,
                        });
                    }
                    return Promise.resolve({
                        data: dataSource,
                        total: total,
                        success: true,
                    });
                }}
                toolbar={{
                    search: <Flex>
                        <Input style={{ width: '250px' }}
                            allowClear
                            value={queryForm.keyWord}
                            onChange={(e) => {
                                console.log('e', e.target.value);
                                setQueryForm({ ...queryForm, keyWord: e.target.value })
                                // ref.current.submit();
                                ref.current.reload();
                            }}
                            placeholder="搜索" />
                        <RangePicker style={{ width: '320px', marginLeft: '6px' }}
                            format='YYYY-MM-DD'
                            onChange={(dates, dateStrings) => {
                                console.log('dates', dates, dateStrings);
                                setQueryForm({
                                    ...queryForm,
                                    dateStart: dateStrings[0],
                                    dateEnd: dateStrings[1]
                                })
                                // // ref.current.submit();
                                ref.current.reload();
                            }} />
                    </Flex>
                }}
                rowKey="key"
                search={false}
            />
            <Modal title={title} open={isModalOpen} footer={null} onCancel={handleCancel}>
                <Table columns={columns1} dataSource={data1} pagination={false} />
            </Modal>
        </>
    );
};