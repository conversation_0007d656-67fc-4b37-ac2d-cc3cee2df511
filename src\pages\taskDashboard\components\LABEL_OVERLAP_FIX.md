# 目标达成分析图表标签重叠修复 - 数值差距大的情况

## 🎯 **问题描述**
当目标达成分析图表中两个数据差距很大时（如9800和30），数值较小的标签会与左侧的分类标签重叠，影响可读性。

## 📊 **问题场景**
```
场景示例：
- 总进尺: 9800米 (长柱状图)
- 实际进尺: 30米 (很短的柱状图)

问题：30米的标签会显示在很短的柱状图右端，与"实际进尺"文字重叠
```

## 🔧 **解决方案**

### **智能标签偏移**
通过计算数值相对于最大值的比例，为小数值添加额外的空格偏移：

```javascript
.label({
  text: (d: any) => {
    // 当数值为0或很小时，不显示标签，避免与分类标签重叠
    if (d.value === 0 || d.value < 0.1) return '';
    
    // 计算数值相对于最大值的比例
    const maxValue = Math.max(...progressData.map(item => item.value));
    const ratio = d.value / maxValue;
    
    // 对于小数值，在标签前添加空格来增加偏移
    const padding = ratio < 0.2 ? '        ' : '  '; // 小数值添加更多空格
    return `${padding}${d.value.toLocaleString()} 米`;
  },
  position: 'right',
  style: { 
    fill: '#fff', 
    fontSize: 14,
    textAlign: 'left', // 左对齐，配合空格偏移
  },
})
```

## 📐 **技术实现原理**

### **1. 比例计算**
```javascript
const maxValue = Math.max(...progressData.map(item => item.value));
const ratio = d.value / maxValue;
```
- 计算当前数值占最大值的比例
- 用于判断是否需要额外偏移

### **2. 动态偏移**
```javascript
const padding = ratio < 0.2 ? '        ' : '  ';
```
- 当比例小于20%时，添加8个空格的偏移
- 正常情况下添加2个空格的基础偏移

### **3. 文本对齐**
```javascript
style: { 
  textAlign: 'left', // 左对齐，配合空格偏移
}
```
- 使用左对齐确保空格偏移生效

## 🎨 **视觉效果对比**

### **修复前**
```
总进尺    [████████████████████████] 9,800 米
实际进尺  [█] 30 米  ← 与"实际进尺"重叠
```

### **修复后**
```
总进尺    [████████████████████████]   9,800 米
实际进尺  [█]         30 米  ← 通过空格偏移，避免重叠
```

## 📊 **适用场景**

### **场景1: 极大数值差距**
- 数据: `{ 总进尺: 9800, 实际进尺: 30 }`
- 比例: 30/9800 = 0.003 (0.3%)
- 处理: 添加8个空格偏移

### **场景2: 中等数值差距**
- 数据: `{ 总进尺: 1000, 实际进尺: 150 }`
- 比例: 150/1000 = 0.15 (15%)
- 处理: 添加8个空格偏移

### **场景3: 正常数值差距**
- 数据: `{ 总进尺: 1000, 实际进尺: 800 }`
- 比例: 800/1000 = 0.8 (80%)
- 处理: 添加2个空格基础偏移

### **场景4: 数值为0**
- 数据: `{ 总进尺: 1000, 实际进尺: 0 }`
- 处理: 不显示标签，避免重叠

## ✅ **修复特点**

1. **智能判断**: 根据数值比例自动调整偏移量
2. **渐进式处理**: 不同比例使用不同的偏移策略
3. **保持美观**: 通过空格偏移而非硬编码位置
4. **兼容性好**: 适用于各种数值范围的数据
5. **无副作用**: 不影响正常数值的显示效果

## 🔍 **测试验证**

### **测试用例1: 极端差距**
```javascript
输入: [{ value: 9800 }, { value: 30 }]
预期: 30米标签有足够偏移，不与分类标签重叠
```

### **测试用例2: 中等差距**
```javascript
输入: [{ value: 1000 }, { value: 150 }]
预期: 150米标签有适当偏移
```

### **测试用例3: 相近数值**
```javascript
输入: [{ value: 1000 }, { value: 800 }]
预期: 800米标签正常显示，基础偏移
```

### **测试用例4: 零值**
```javascript
输入: [{ value: 1000 }, { value: 0 }]
预期: 0值不显示标签
```

## 🚀 **用户体验提升**

1. **可读性增强**: 消除了标签重叠问题
2. **视觉清晰**: 所有数值都能清楚显示
3. **智能适应**: 自动适应不同的数据范围
4. **一致性**: 保持了图表的整体美观

现在目标达成分析图表在任何数值差距下都能正确显示标签，避免重叠问题！
