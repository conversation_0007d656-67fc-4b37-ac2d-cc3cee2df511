/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\RadarComparisonChart.tsx
 * @Description: 钻机效率雷达对比组件 - 使用G2雷达图展示多台设备在不同维度的性能对比
 */

import { Card, Empty } from 'antd';
import { RadarChartOutlined } from '@ant-design/icons';
import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';
import styles from '../newTaskDashboard.less';

interface RadarData {
  equipment: string;
  metric: string;
  value: number;
}

interface RadarComparisonChartProps {
  // 雷达图数据
  radarData?: RadarData[];
}

const RadarComparisonChart: React.FC<RadarComparisonChartProps> = ({ radarData }) => {
  const radarChartRef = useRef<HTMLDivElement>(null);

  // 默认雷达图数据 - 两台设备对比，匹配设计图
  const defaultRadarData = [
    // ZJ-1002 (粉色)
    { equipment: 'ZJ-1002', metric: '进程速度', value: 85 },
    { equipment: 'ZJ-1002', metric: '维护成本', value: 75 },
    { equipment: 'ZJ-1002', metric: '利用率', value: 60 },
    { equipment: 'ZJ-1002', metric: '故障率', value: 45 },
    { equipment: 'ZJ-1002', metric: 'EEE', value: 70 },
    { equipment: 'ZJ-1002', metric: '能耗效率', value: 85 },

    // ZJ-1003 (蓝色)
    { equipment: 'ZJ-1003', metric: '进程速度', value: 75 },
    { equipment: 'ZJ-1003', metric: '维护成本', value: 85 },
    { equipment: 'ZJ-1003', metric: '利用率', value: 90 },
    { equipment: 'ZJ-1003', metric: '故障率', value: 65 },
    { equipment: 'ZJ-1003', metric: 'EEE', value: 55 },
    { equipment: 'ZJ-1003', metric: '能耗效率', value: 70 },
  ];

  const chartData = radarData || defaultRadarData;

  // 判断是否有有效数据
  const hasValidData = chartData.length > 0;

  // G2雷达图渲染函数 - 使用正确的G2 5.x API语法
  const renderRadarChart = () => {
    if (!radarChartRef.current) return;

    const chart = new Chart({
      container: radarChartRef.current,
      autoFit: true,
      height: 350,
    });

    // 使用正确的G2 5.x API - 链式调用方法
    chart
      .coordinate({ type: 'polar' })
      .scale('x', { padding: 0.5, align: 0 })
      .scale('y', { tickCount: 5, domainMax: 100, domainMin: 0 })
      .scale('color', { range: ['#E91E63', '#2196F3'] })
      .axis('x', {
        grid: true,
        title: false,
        labelFill: '#FFFFFF',
        labelFontSize: 12,
        labelFontWeight: 'bold',
        gridStroke: '#ffffff',
        gridLineWidth: 1,
      })
      .axis('y', {
        title: false,
        label: false, // 隐藏Y轴标签（中间的数字100, 80, 60...）
        gridStroke: '#ffffff',
        gridLineWidth: 1,
      })
      .legend('color', {
        position: 'bottom',
        itemLabelFill: '#FFFFFF',
        itemLabelFontSize: 14,
        itemLabelFontWeight: 'bold',
        itemMarkerSymbol: 'square',
        itemMarkerSize: 10,
        itemSpacing: 10,
        layout: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      });

    // 添加面积图层
    chart
      .area()
      .data(chartData)
      .encode('x', 'metric')
      .encode('y', 'value')
      .encode('color', 'equipment')
      .style('fillOpacity', 0.3);

    // 添加线条图层
    chart
      .line()
      .data(chartData)
      .encode('x', 'metric')
      .encode('y', 'value')
      .encode('color', 'equipment')
      .style('lineWidth', 2);

    // 添加点图层
    chart
      .point()
      .data(chartData)
      .encode('x', 'metric')
      .encode('y', 'value')
      .encode('color', 'equipment')
      .style('stroke', '#fff')
      .style('strokeWidth', 2)
      .style('r', 4);

    chart.render();
    return () => chart.destroy();
  };

  useEffect(() => {
    const cleanup = renderRadarChart();
    return cleanup;
  }, [chartData]);

  return (
    <Card
      title="钻机效率雷达对比"
      bordered={false}
      className={styles.chartCard}
      headStyle={{
        fontSize: '22px',
        fontWeight: 'normal',
        fontFamily: 'PingFang SC',
        color: '#ffffff',
      }}
    >
      <div style={{ padding: '0px' }}>
        {hasValidData ? (
          /* G2雷达图容器 */
          <div ref={radarChartRef} style={{ height: '350px' }}></div>
        ) : (
          /* 无数据状态 */
          <div style={{ height: '350px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Empty
              image={<RadarChartOutlined style={{ fontSize: 48, color: '#666' }} />}
              description={
                <span style={{ color: '#999', fontSize: '14px' }}>
                  暂无雷达对比数据
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default RadarComparisonChart;
