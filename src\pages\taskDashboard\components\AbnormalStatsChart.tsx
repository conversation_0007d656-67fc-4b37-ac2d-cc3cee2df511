/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-07-16 10:00:00
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-07-16 10:00:00
 * @FilePath: \diy_tfl_pc\src\pages\taskDashboard\components\AbnormalStatsChart.tsx
 * @Description: 异常事件统计图表组件 - 使用G2横向柱状图展示不同类型异常事件的统计数据
 */

import { Card, Empty } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';
import React, { useEffect, useRef } from 'react';
import { Chart } from '@antv/g2';
import styles from '../newTaskDashboard.less';

interface AbnormalStatsData {
  type: string;
  count: number;
  color: string;
}

interface AbnormalStatsChartProps {
  // 异常统计数据
  abnormalStatsData?: AbnormalStatsData[];
}

const AbnormalStatsChart: React.FC<AbnormalStatsChartProps> = ({ abnormalStatsData }) => {
  const abnormalBarRef = useRef<HTMLDivElement>(null);

  // 默认异常统计数据 - 使用0值
  const defaultAbnormalStatsData = [
    { type: '传感器异常', count: 0, color: '#007fff' },
    { type: '钻杆顺序异常', count: 0, color: '#FF9200' },
    { type: '钻杆断裂', count: 0, color: '#6c63f0' },
    { type: '压钻', count: 0, color: '#8B5CF6' },
    { type: '瓦斯异常', count: 0, color: '#00b042' },
    { type: '冒烟', count: 0, color: '#FF5722' },
    { type: '喷孔', count: 0, color: '#9C27B0' },
    { type: '见岩偏差过大', count: 0, color: '#C2C2C2' },
  ];

  const chartData = abnormalStatsData || defaultAbnormalStatsData;

  // 判断是否有有效数据
  const hasValidData = chartData.some(item => item.count > 0);

  // 渲染异常事件横向柱状图
  const renderAbnormalBarChart = () => {
    if (!abnormalBarRef.current) return;

    const chart = new Chart({
      container: abnormalBarRef.current,
      autoFit: true,
      height: 280,
      theme: 'dark',
    });

    // 横向柱状图 - 使用G2内置交互
    chart
      .coordinate({ transform: [{ type: 'transpose' }] })
      .interval()
      .data(chartData)
      .encode('x', 'type')
      .encode('y', 'count')
      .encode('color', 'type')
      .scale('color', { range: ['#007fff', '#FF9200', '#6c63f0', '#8B5CF6', '#00b042', '#FF5722', '#9C27B0', '#C2C2C2'] })
      .scale('x', {
        type: 'band',
        paddingInner: 0.4,
        paddingOuter: 0.2,
      })
      .axis('x', {
        title: false,
        labelStyle: {
          fill: '#fff',
          fontSize: 14,
          textAlign: 'center',
          textBaseline: 'middle',
        },
        line: false,
        tick: false,
      })
      .axis('y', {
        title: false,
        labelStyle: { fill: '#fff', fontSize: 12 },
        line: false,
        tick: false,
        grid: { stroke: '#434343', lineWidth: 1 },
      })
      .style({
        radius: 4,
      })
      .legend({
        color: {
          position: 'bottom',
          layout: { justifyContent: 'center' },
          itemLabelFill: '#FFFFFF',
          itemLabelFontSize: 12,
          itemMarkerSymbol: 'circle',
          itemMarkerSize: 12,
          itemSpacing: 20,
        },
      })
      .interaction('legendFilter'); // 启用图例过滤交互

    chart.render();
    return () => chart.destroy();
  };

  useEffect(() => {
    const cleanup = renderAbnormalBarChart();
    return cleanup;
  }, [chartData]);

  return (
    <Card
      title="异常事件统计"
      bordered={false}
      className={styles.chartCard}
      headStyle={{
        fontSize: '22px',
        fontWeight: 'normal',
        fontFamily: 'PingFang SC',
        color: '#ffffff',
      }}
    >
      <div style={{ paddingTop: '20px' }}>
        {hasValidData ? (
          /* G2横向柱状图容器 - 现在使用内置图例交互 */
          <div ref={abnormalBarRef} style={{ height: '350px' }}></div>
        ) : (
          /* 无数据状态 */
          <div style={{ height: '350px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Empty
              image={<BarChartOutlined style={{ fontSize: 48, color: '#666' }} />}
              description={
                <span style={{ color: '#999', fontSize: '14px' }}>
                  暂无异常事件数据
                </span>
              }
            />
          </div>
        )}
      </div>
    </Card>
  );
};

export default AbnormalStatsChart;
