import React, { useState, useRef, useEffect } from 'react';
import { Flex, Button, message, Modal, Form, Input, Drawer, TreeSelect } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CaretRightOutlined, CaretDownOutlined, LoadingOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-components';
import { postRequest } from '@/services/api/api';
import type { TreeSelectProps } from 'antd';
import type { DefaultOptionType } from 'antd/es/select';

interface ApiResponse {
    status: number;
    msg?: string;
    data: DepartmentItem[];
}

interface DepartmentItem {
    id: string;
    name: string;
    parentId: string;
    hasChildren: boolean;
    number?: number;
    children?: DepartmentItem[];
}

interface TableListItem extends DepartmentItem {
    parentName: string;
}

interface TreeNode extends DefaultOptionType {
    id: string;
    pId: string;
    value: string;
    title: string;
    isLeaf: boolean;
}

const Department: React.FC = () => {
    const actionRef = useRef<ActionType>();
    const [messageApi, contextHolder] = message.useMessage();
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [editingDepartment, setEditingDepartment] = useState<DepartmentItem | undefined>(undefined);
    const [form] = Form.useForm();
    const [departmentList, setDepartmentList] = useState<DepartmentItem[]>([]);
    const [tableData, setTableData] = useState<TableListItem[]>([]);
    const [loadedDepts, setLoadedDepts] = useState<Set<string>>(new Set());
    const [expandedDepts, setExpandedDepts] = useState<Set<string>>(new Set());
    const [deptCache, setDeptCache] = useState<Record<string, TableListItem[]>>({});
    const [loadingDepts, setLoadingDepts] = useState<Set<string>>(new Set());
    const [deptTreeData, setDeptTreeData] = useState<TreeNode[]>([]);

    // 转换部门数据为树节点
    const transformToDeptNode = (item: DepartmentItem): TreeNode => ({
        id: item.id,
        pId: item.parentId,
        value: item.id,
        title: item.name,
        isLeaf: !item.hasChildren,
        key: item.id,
        children: undefined
    });

    // 获取部门列表
    const fetchDepartmentList = async () => {
        try {
            const result = await postRequest('department/get_ls', { parentId: '0' }) as ApiResponse;
            if (result.status === 0) {
                setDepartmentList(result.data);
                setTableData(result.data.map(item => ({
                    ...item,
                    parentName: ''
                })));
            } else {
                messageApi.error(result.msg || '获取部门列表失败');
            }
        } catch (error) {
            messageApi.error('获取部门列表失败');
        }
    };

    // 初始化加载部门列表
    useEffect(() => {
        fetchDepartmentList();
    }, []);

    // 处理添加部门
    const handleAdd = async (values: { name: string; parentId: string }) => {
        try {
            const result = await postRequest('department/post_add', {
                name: values.name,
                parentId: values.parentId || '0'
            }) as ApiResponse;

            if (result.status === 0) {
                messageApi.success('添加成功');
                setEditModalVisible(false);
                form.resetFields();
                fetchDepartmentList();
            } else {
                messageApi.error(result.msg || '添加失败');
            }
        } catch (error) {
            messageApi.error('添加失败');
        }
    };

    // 处理编辑部门
    const handleEdit = async (values: { name: string; parentId: string }) => {
        if (!editingDepartment) return;

        try {
            const result = await postRequest('department/post_modify', {
                id: editingDepartment.id,
                name: values.name,
                parentId: values.parentId || '0'
            }) as ApiResponse;

            if (result.status === 0) {
                messageApi.success('修改成功');
                setEditModalVisible(false);
                setEditingDepartment(undefined);
                form.resetFields();
                fetchDepartmentList();
            } else {
                messageApi.error(result.msg || '修改失败');
            }
        } catch (error) {
            messageApi.error('修改失败');
        }
    };

    // 处理删除
    const handleDelete = async (id: string) => {
        try {
            const result = await postRequest('department/post_del', { id }) as ApiResponse;
            if (result.status === 0) {
                messageApi.success('删除成功');
                // 清除已加载记录，重新加载数据
                setLoadedDepts(new Set());
                actionRef.current?.reload();
            } else {
                messageApi.error(result.msg || '删除失败');
            }
        } catch (error) {
            messageApi.error('删除失败');
        }
    };

    // 处理部门点击
    const handleDepartmentClick = async (record: TableListItem) => {
        if (!record.hasChildren) return;

        if (expandedDepts.has(record.id)) {
            // 收起部门
            setExpandedDepts(prev => {
                const next = new Set(prev);
                next.delete(record.id);
                return next;
            });
            // 从表格数据中移除子部门
            const newData = tableData.filter(item => !item.parentId || item.parentId !== record.id);
            setTableData(newData);
        } else {
            if (!loadedDepts.has(record.id)) {
                // 设置加载状态
                setLoadingDepts(prev => new Set([...prev, record.id]));
            }
            
            // 展开部门
            setExpandedDepts(prev => new Set([...prev, record.id]));
            
            if (loadedDepts.has(record.id)) {
                // 使用缓存数据
                const cachedChildren = deptCache[record.id] || [];
                const index = tableData.findIndex(item => item.id === record.id);
                if (index > -1) {
                    const newData = [
                        ...tableData.slice(0, index + 1),
                        ...cachedChildren,
                        ...tableData.slice(index + 1)
                    ];
                    setTableData(newData);
                }
            } else {
                // 加载新数据
                try {
                    const result = await postRequest('department/get_ls', {
                        parentId: record.id
                    }) as ApiResponse;
                    if (result.status === 0 && Array.isArray(result.data)) {
                        const childDepts = result.data.map(item => ({
                            id: item.id,
                            name: item.name,
                            number: item.number,
                            parentId: item.parentId,
                            parentName: item.parentName || '',
                            hasChildren: item.hasChildren,
                        }));
                        // 缓存数据
                        setDeptCache(prev => ({
                            ...prev,
                            [record.id]: childDepts
                        }));
                        // 找到点击行的索引
                        const index = tableData.findIndex(item => item.id === record.id);
                        if (index > -1) {
                            // 在点击行后面插入子部门数据
                            const newData = [
                                ...tableData.slice(0, index + 1),
                                ...childDepts,
                                ...tableData.slice(index + 1)
                            ];
                            setTableData(newData);
                        }
                        // 记录已加载的部门ID
                        setLoadedDepts(prev => new Set([...prev, record.id]));
                    }
                } catch (error) {
                    messageApi.error('加载子部门失败');
                    // 加载失败时收起部门
                    setExpandedDepts(prev => {
                        const next = new Set(prev);
                        next.delete(record.id);
                        return next;
                    });
                } finally {
                    // 清除加载状态
                    setLoadingDepts(prev => {
                        const next = new Set(prev);
                        next.delete(record.id);
                        return next;
                    });
                }
            }
        }
    };

    // 计算部门层级
    const calculateLevel = (record: TableListItem): number => {
        let level = 0;
        let currentId = record.parentId;
        const visited = new Set<string>();

        while (currentId && currentId !== '0' && !visited.has(currentId)) {
            visited.add(currentId);
            level++;
            // 在当前表格数据中查找父部门
            const parent = tableData.find(item => item.id === currentId);
            if (parent) {
                currentId = parent.parentId;
            } else {
                break;
            }
        }
        return level;
    };

    const columns: ProColumns<TableListItem>[] = [
        {
            title: '部门名称',
            dataIndex: 'name',
            width: 300,
            render: (text, record) => {
                const isExpanded = expandedDepts.has(record.id);
                const isLoading = loadingDepts.has(record.id);
                const level = calculateLevel(record);
                const indentWidth = 24; // 每级缩进的宽度
                const baseIndent = record.hasChildren ? 8 : 24; // 基础缩进（有/无箭头的情况）

                return (
                    <a onClick={() => handleDepartmentClick(record)} style={{ color: '#dcdcdc' }}>
                        <span style={{ marginLeft: level * indentWidth }}>
                            {record.hasChildren ? (
                                isLoading ? 
                                    <LoadingOutlined style={{ color: '#dcdcdc' }} /> :
                                    isExpanded ? 
                                        <CaretDownOutlined style={{ color: '#dcdcdc' }} /> : 
                                        <CaretRightOutlined style={{ color: '#dcdcdc' }} />
                            ) : null}
                            <span style={{ marginLeft: baseIndent }}>{text}</span>
                        </span>
                    </a>
                );
            },
        },
        {
            title: '成员数',
            dataIndex: 'number',
            width: 100,
            align: 'center',
            render: (text, record) => `${record.number ?? 0}`,
        },
        {
            title: '操作',
            valueType: 'option',
            width: 120,
            align: 'center',
            render: (_, record) => [
                <Button
                    key="edit"
                    type="link"
                  
                    onClick={() => {
                        setEditingDepartment(record);
                        form.setFieldsValue({
                            name: record.name,
                            parentId: record.parentId || '0'
                        });
                        setEditModalVisible(true);
                    }}
                >
                    编辑
                </Button>,
                <Button
                    key="delete"
                    type="link"
                
                 
                    onClick={() => {
                        Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除部门 ${record.name} 吗？删除后不可恢复！`,
                            okText: '确认',
                            cancelText: '取消',
                            onOk: () => handleDelete(record.id)
                        });
                    }}
                >
                    删除
                </Button>,
            ],
        },
    ];

    // 加载部门树数据
    const loadDeptTreeData = async () => {
        try {
            const result = await postRequest('department/get_ls', { parentId: '0' }) as ApiResponse;
            if (result.status === 0) {
                const treeData = result.data.map(transformToDeptNode);
                setDeptTreeData(treeData);
            }
        } catch (error) {
            messageApi.error('加载部门数据失败');
        }
    };

    // 异步加载子部门数据
    const onLoadDeptData: TreeSelectProps['loadData'] = async (node) => {
        const treeNode = node as unknown as TreeNode;
        try {
            const result = await postRequest('department/get_ls', { parentId: treeNode.id }) as ApiResponse;
            if (result.status === 0) {
                const childNodes = result.data.map(transformToDeptNode);
                setDeptTreeData(prev => [...prev, ...childNodes]);
            }
        } catch (error) {
            messageApi.error('加载子部门失败');
        }
    };

    // 打开抽屉时重新加载部门树数据
    useEffect(() => {
        if (editModalVisible) {
            loadDeptTreeData();
        }
    }, [editModalVisible]);

    // 表单组件
    const renderForm = () => (
        <Form
            form={form}
            layout="vertical"
            onFinish={editingDepartment ? handleEdit : handleAdd}
        >
            <Form.Item
                name="name"
                label="部门名称"
                rules={[{ required: true, message: '请输入部门名称' }]}
            >
                <Input placeholder="请输入部门名称" />
            </Form.Item>

            <Form.Item
                name="parentId"
                label="上级部门"
                initialValue={editingDepartment?.parentId || ''}
                rules={[{ required: true, message: '请选择上级部门' }]}
            >
                <TreeSelect
                    treeDataSimpleMode
                    style={{ width: '100%' }}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    placeholder="请选择上级部门"
                    loadData={onLoadDeptData}
                    treeData={deptTreeData}
                    showSearch
                    treeNodeFilterProp="title"
                    />
                    {/* disabled={editingDepartment?.id === '0'} // 根部门不能选择上级部门 */}
            </Form.Item>
        </Form>
    );

    // 抽屉组件
    const renderDrawer = () => (
        <Drawer
            title={editingDepartment ? '编辑部门' : '添加部门'}
            width={400}
            onClose={() => {
                setEditModalVisible(false);
                setEditingDepartment(null);
                form.resetFields();
            }}
            open={editModalVisible}
            footer={
                <Flex justify="flex-end" gap="small">
                    <Button onClick={() => {
                        setEditModalVisible(false);
                        setEditingDepartment(null);
                        form.resetFields();
                    }}>
                        取消
                    </Button>
                    <Button type="primary" onClick={form.submit}>
                        确定
                    </Button>
                </Flex>
            }
        >
            {renderForm()}
        </Drawer>
    );

    return (
        <div>
            {contextHolder}
            <ProTable<TableListItem>
                columns={columns}
                actionRef={actionRef}
                dataSource={tableData}
                scroll={{ y: 'calc(100vh - 200px)' }}
                style={{ height: 'calc(100vh - 120px)' }}
                request={async (params) => {
                    try {
                        const result = await postRequest('department/get_ls', {
                            ...params,
                            parentId: '0'
                        }) as ApiResponse;
                        const data = result.data.map(item => ({
                            id: item.id,
                            name: item.name,
                            number: item.number,
                            parentId: item.parentId,
                            parentName: item.parentName || '',
                            hasChildren: item.hasChildren,
                        })) as TableListItem[];
                        setTableData(data);
                        // 清除已加载记录
                        setLoadedDepts(new Set());
                        return {
                            data,
                            success: result.status === 0,
                        };
                    } catch (error) {
                        return {
                            data: [],
                            success: false,
                        };
                    }
                }}
                rowKey="id"
                search={false}
                pagination={false}
                expandable={{
                    expandRowByClick: true,
                    onExpand: async (expanded, record) => {
                        if (expanded && record.hasChildren && !record.children) {
                            try {
                                const result = await postRequest('department/get_ls', {
                                    parentId: record.id
                                }) as ApiResponse;
                                if (result.status === 0 && Array.isArray(result.data)) {
                                    // 将子部门数据添加到当前记录
                                    record.children = result.data.map(item => ({
                                        id: item.id,
                                        name: item.name,
                                        number: item.number,
                                        parentId: item.parentId,
                                        parentName: item.parentName || '',
                                        hasChildren: item.hasChildren,
                                    }));
                                    // 强制表格重新渲染
                                    actionRef.current?.reload();
                                }
                            } catch (error) {
                                messageApi.error('加载子部门失败');
                            }
                        }
                    }
                }}
                headerTitle="部门列表"
                toolBarRender={() => [
                    <Button
                        key="add"
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                            form.resetFields();
                            setEditingDepartment(undefined);
                            setEditModalVisible(true);
                        }}
                    >
                        添加部门
                    </Button>,
                ]}
            />

            {renderDrawer()}
        </div>
    );
};

export default Department;