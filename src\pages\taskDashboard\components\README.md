# 任务看板组件说明

本目录包含了任务看板页面拆分后的各个独立组件，每个组件都有明确的职责和功能。

## 组件结构

### 1. MetricsCards（核心指标卡片组件）
- **文件**: `MetricsCards.tsx`
- **功能**: 展示总进尺、运行钻机数、总任务数、异常事件、设备开机数量等关键指标
- **特点**: 
  - 接收外部数据作为props
  - 响应式布局设计
  - 统一的卡片样式

### 2. GoalAnalysisChart（目标达成分析图表组件）
- **文件**: `GoalAnalysisChart.tsx`
- **功能**: 使用G2进度条图表展示计划总进尺与实际总进尺的对比
- **特点**:
  - 自动计算目标完成率和偏差率
  - 支持外部数据传入或使用默认模拟数据
  - 深色主题适配

### 3. EquipmentPieChart（设备投入产出比饼图组件）
- **文件**: `EquipmentPieChart.tsx`
- **功能**: 使用G2饼图展示设备状态分布，包含钻机利用率和单机效率指标
- **特点**:
  - 中空饼图设计
  - 内置图例交互功能
  - 显示钻机利用率和单机效率

### 4. AbnormalStatsChart（异常事件统计图表组件）
- **文件**: `AbnormalStatsChart.tsx`
- **功能**: 使用G2横向柱状图展示不同类型异常事件的统计数据
- **特点**:
  - 横向柱状图布局
  - 支持图例过滤交互
  - 多种异常类型颜色区分

### 5. RadarComparisonChart（钻机效率雷达对比组件）
- **文件**: `RadarComparisonChart.tsx`
- **功能**: 使用G2雷达图展示多台设备在不同维度的性能对比
- **特点**:
  - 多设备性能对比
  - 六个维度评估（进程速度、维护成本、利用率、故障率、EEE、能耗效率）
  - 面积图+线条图+点图组合

### 6. EquipmentHealthRanking（设备健康评分排行榜组件）
- **文件**: `EquipmentHealthRanking.tsx`
- **功能**: 展示设备健康评分排行，支持自动分页滚动
- **特点**:
  - 自动分页滚动功能
  - 排名颜色区分（前三名特殊颜色）
  - 进度条显示健康评分

### 7. TeamEfficiencyTable（班组效能对比表格组件）
- **文件**: `TeamEfficiencyTable.tsx`
- **功能**: 展示各班组的任务完成率、已完成任务数、平均进尺、异常次数等关键指标
- **特点**:
  - 表格形式展示
  - 支持自定义分页
  - 响应式设计

## 使用方式

### 单独使用组件
```tsx
import { MetricsCards } from './components';

// 使用核心指标卡片组件
<MetricsCards
  totalDistance="12456"
  distanceChange="123"
  stationCount="24"
  operationRate="80"
  // ... 其他props
/>
```

### 批量导入
```tsx
import {
  MetricsCards,
  GoalAnalysisChart,
  EquipmentPieChart,
  AbnormalStatsChart,
  RadarComparisonChart,
  EquipmentHealthRanking,
  TeamEfficiencyTable
} from './components';
```

## 组件特点

1. **独立性**: 每个组件都是独立的，可以单独使用和测试
2. **可复用性**: 组件设计考虑了复用性，可以在其他页面中使用
3. **数据驱动**: 支持外部数据传入，也提供默认模拟数据
4. **响应式**: 所有组件都支持响应式布局
5. **类型安全**: 使用TypeScript提供完整的类型定义
6. **样式统一**: 复用原有的样式文件，保持视觉一致性

## 维护说明

- 每个组件都有清晰的注释和类型定义
- 组件内部逻辑相对简单，易于维护
- 图表组件使用G2库，需要注意版本兼容性
- 样式文件复用，修改样式时需要考虑对其他组件的影响
