import { getRequest } from '@/services/api/api';
import { history } from '@umijs/max';
import { Breadcrumb, Card, Col, message, Row, Spin, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import Status from './status';
// import Information from './component/information'
// import Life from './component/life'
// import Maintain from './component/maintain'

interface DeviceInfo {
  deviceName: string; // 设备名称
  deviceCode: string; // 设备序列号
  deviceModel: string; // 设备型号
  configurationState?: number; // 配置状态：0-启用 1-禁用
  creatAt: string; // 创建时间
  updateAt: string; // 最后更新时间
  currentState: number; // 当前状态：0-打开 1-关闭 2-损坏
  lastMaintenanceDate?: string; // 最后保养日期
  miningFace?: string; // 采面
  lane?: string; // 巷道
  drillSite?: string; // 矿场
  companyName?: string; // 客户名称
  director?: string; // 设备负责人
  account?: string; // 设备负责人账号
  label?: string; // 设备标签
  deviceModelId: string; // 设备型号ID
}

interface ApiResponse<T> {
  status: number;
  msg?: string;
  data: T;
}

// 获取配置状态显示文本和颜色
const getConfigStateText = (state?: number) => {
  switch (state) {
    case 0:
      return { text: '启用', color: 'success' };
    case 1:
      return { text: '禁用', color: 'error' };
    default:
      return { text: '-', color: 'default' };
  }
};

// 获取当前状态显示文本和颜色
const getCurrentStateText = (state: number) => {
  switch (state) {
    case 0:
      return { text: '打开', color: 'success' };
    case 1:
      return { text: '关闭', color: 'warning' };
    case 2:
      return { text: '损坏', color: 'error' };
    default:
      return { text: '-', color: 'default' };
  }
};

export default () => {
  const { location } = history;
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>();
  const [loading, setLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const [activeTabKey, setActiveTabKey] = useState('0');
  const [infoLoaded, setInfoLoaded] = useState(false);

  // 获取设备详情
  const fetchDeviceInfo = async () => {
    const searchParams = new URLSearchParams(location.search);
    const id = searchParams.get('id');

    if (!id) {
      messageApi.error('设备ID不能为空');
      return;
    }

    setLoading(true);
    setInfoLoaded(false);
    try {
      const result = await getRequest<ApiResponse<DeviceInfo>>('drilltask/get_info', {
        id,
      });

      if (result.status === 0) {
        setDeviceInfo(result.data);
        setInfoLoaded(true);
      } else {
        messageApi.error(result.msg || '获取任务信息失败');
      }
    } catch (error) {
      messageApi.error('请求失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeviceInfo();
  }, []);

  const tabListNoTitle = [
    // {
    //     label: '打孔管理',
    //     key: '0',
    // },
    // {
    //     label: '异常记录',
    //     key: '1',
    // },
    // {
    //     label: '保养计划记录',
    //     key: '2',
    // },
    // {
    //     label: '设备寿命跟踪',
    //     key: '3',
    // },
    // {
    //     label: '数据转移记录',
    //     key: '4',
    // },
  ];

  const contentListNoTitle: Record<string, React.ReactNode> = {
    '0': infoLoaded && deviceInfo ? <Status number={deviceInfo.deviceCode} /> : <Spin />,
    // '1': infoLoaded ? <History /> : <Spin />,
  };

  const onTabChange = (key: string) => {
    setActiveTabKey(key);
  };

  return (
    <>
      {contextHolder}
      <Breadcrumb items={[{ title: '首页' }, { title: '施工管理' },   { title: <a style={{background:'#000'}} onClick={() => history.replace('/project/deliver')}>下发任务</a>, },
                    { title: '下发任务详情' },]} />
      <Card bordered={false} loading={loading} style={{ marginTop: '24px',marginBottom:'24px' }}>
       
        <div style={{ marginTop: '6px', fontWeight: 500, fontSize: '20px' }}>
          {deviceInfo?.taskCode || '-'}
        </div>
        <Row style={{ marginTop: '14px' }} gutter={[16, 8]}>
          <Col span={6}>任务名称：{deviceInfo?.taskName || '-'}</Col>
          <Col span={6}>负责人员：{deviceInfo?.curatorName || '-'}</Col>
          <Col span={6}>创建人员：{deviceInfo?.creatorName || '-'}</Col>
          <Col span={6}>
            任务状态：
            {deviceInfo?.status === 0 ? (
              <Tag color="#1890ff">进行中</Tag>
            ) : (
              <Tag color="#52c41a">已完成</Tag>
            )}
          </Col>
          <Col span={6}>创建时间：{deviceInfo?.createdAt || '-'}</Col>
          <Col span={6}>修改时间：{deviceInfo?.updateAt || '-'}</Col>
        </Row>
      </Card>
      {deviceInfo ? <Status number={deviceInfo}  /> : <Spin />}
      {/* <Card
                bordered={false}
                tabList={tabListNoTitle}
                activeTabKey={activeTabKey}
                onTabChange={onTabChange}
                style={{ marginTop: '24px' }}
                tabProps={{
                    size: 'middle',
                }}
            >
                {contentListNoTitle[activeTabKey]}
            </Card> */}
    </>
  );
};
