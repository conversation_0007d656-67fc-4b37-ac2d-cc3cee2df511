/*
 * @Author: 祁强 <EMAIL>
 * @Date: 2025-03-14 14:18:11
 * @LastEditors: 祁强 <EMAIL>
 * @LastEditTime: 2025-03-20 17:22:34
 * @FilePath: \diy_tfl_pc\src\pages\Home\Operation\components\work.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { Card, Row, Col, Typography, Space, Descriptions } from 'antd';

const { Text } = Typography;

interface WorkProps {
  className?: string; // 添加一个可选的className属性
}

const Work: React.FC<WorkProps> = () => {
  // 修改工作详情数据结构以匹配图片布局
  const workDetails = [
    {
      title: '工作区域',
      items: [
        { label: '当前区域', value: '1号区域' },
        { label: '工作状态', value: '运行中' },
        { label: '开始时间', value: '2024-3-20 12:00' },
        { label: '预计结束', value: '2024-3-20 18:00' },
      ]
    },
    {
      title: '工作参数',
      items: [
        { label: '工作模式', value: '自动模式' },
        { label: '运行速度', value: '2.5 m/s' },
        { label: '工作压力', value: '150 MPa' },
        { label: '设备温度', value: '65℃' },
      ]
    },
    {
      title: '工作统计',
      items: [
        { label: '完成面积', value: '150 m²' },
        { label: '剩余面积', value: '350 m²' },
        { label: '工作效率', value: '90%' },
        { label: '异常次数', value: '0 次' },
      ]
    }
  ];

  return (
    <Row gutter={[16, 16]}>
      {workDetails.map((section, index) => (
        <Col span={8} key={index}>
          <Card
            title={section.title}
            bordered={false}
            style={{ border: 'none' }}  // 添加无边框样式
            headStyle={{ 
              color: '#fff',
              border: 'none'
            }}
          >
            <Descriptions
              bordered={false}
              layout="vertical"
              column={2}
              contentStyle={{ fontSize: '18px' }}  // 设置内容字体大小
              labelStyle={{ fontSize: '18px' }} 
              items={section.items.map(item => ({
                key: item.label,
                label: item.label,
                children: item.value
              }))}
            />
          </Card>
        </Col>
      ))}
    </Row>
  );
};

export default Work;
